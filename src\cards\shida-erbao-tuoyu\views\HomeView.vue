<script setup lang="ts">
import { ref, onMounted } from 'vue'
import DigitalHuman from '../components/DigitalHuman.vue'
import NavigationButtons from '../components/NavigationButtons.vue'

const slogan = ref('欢迎来到师大儿保托育中心，我是中心客服赵梓桉，有什么我可以帮您的？')

onMounted(() => {
  document.title = '师大儿保托育中心'
})
</script>

<template>
  <div class="home-container">
    <div class="digital-human-container">
      <div class="digital-human-wrapper">
        <DigitalHuman />
      </div>
    </div>
    
    <div class="bottom-section">
      <div class="slogan-container">
        <div class="slogan-wrapper">
          <h1 class="slogan">{{ slogan }}</h1>
          <div class="tech-line"></div>
        </div>
      </div>
      
      <div class="navigation-container">
        <NavigationButtons />
      </div>
    </div>
  </div>
</template>

<style scoped>
.home-container {
  position: relative;
  height: 100vh;
  max-height: 100vh;
  overflow: hidden;
}

.digital-human-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.digital-human-wrapper {
  width: 100%;
  height: 100%;
}

.bottom-section {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, transparent, rgba(255, 255, 255, 0.85) 15%, rgba(255, 255, 255, 0.95));
  padding-top: 1rem;
  z-index: 2;
  backdrop-filter: blur(5px);
}

.slogan-container {
  padding: 1rem 1rem 0.5rem 1rem;
  display: flex;
  justify-content: center;
}

.slogan-wrapper {
  position: relative;
  max-width: 340px;
  padding-bottom: 0.5rem;
}

.tech-line {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(to right, transparent, #5dae57 30%, #5dae57 70%, transparent);
}

.slogan {
  font-size: 1.25rem;
  line-height: 1.5;
  background: linear-gradient(135deg, #5dae57, #7fc379);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
  text-align: center;
  font-weight: 500;
}

.navigation-container {
  padding: 1rem 1rem calc(env(safe-area-inset-bottom) + 2rem) 1rem;
}

@media (min-width: 768px) {
  .bottom-section {
    padding-top: 4rem;
  }
  
  .digital-human-container {
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
  }
  
  .digital-human-wrapper {
    width: auto;
    height: 100%;
    min-height: 100vh;
    max-width: none;
    display: flex;
    justify-content: center;
  }
  
  .digital-human-wrapper :deep(img),
  .digital-human-wrapper :deep(video) {
    height: 100%;
    min-height: 100vh;
    width: auto;
    object-fit: cover;
    object-position: center;
  }
  
  .slogan-wrapper {
    max-width: 500px;
    padding-bottom: 0.75rem;
  }

  .slogan {
    font-size: 1.75rem;
    background: linear-gradient(135deg, #5dae57, #7fc379);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .slogan-container {
    padding: 1.5rem 1.5rem 0.75rem 1.5rem;
  }
  
  .navigation-container {
    padding: 0.75rem 1.5rem calc(env(safe-area-inset-bottom) + 2.5rem) 1.5rem;
  }
}

@media (min-width: 1200px) {
  .bottom-section {
    padding-top: 5rem;
  }
}
</style> 