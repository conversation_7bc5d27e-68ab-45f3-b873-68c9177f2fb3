<script setup lang="ts">
import { useRouter } from 'vue-router'
import {
  ArrowLeft,
  Connection,
  TrendCharts,
  Star,
  Trophy,
  Promotion,
  DataAnalysis,
  Service,
  Timer,
  Check
} from '@element-plus/icons-vue'
import { onMounted } from 'vue'
import TabBar from '../components/TabBar.vue'

const router = useRouter()

const goBack = () => {
  router.push('/card/smart-chain')
}

onMounted(() => {
  document.title = '智链 - 项目介绍'
})
</script>

<template>
  <div class="view-container">
    <div class="header">
      <el-button type="text" @click="goBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <h1>项目介绍</h1>
    </div>

    <div class="content">
      <!-- 第一部分：开篇·我们的起点 -->
      <section class="intro-section">
        <div class="section-header">
          <div class="section-icon">
            <el-icon><Connection /></el-icon>
          </div>
          <h2 class="section-title">开篇·我们的起点</h2>
        </div>

        <div class="content-card">
          <h3 class="card-title">您的增长，始于每一次"链接"的困境</h3>
          <p class="intro-text">在数字时代，企业增长的本质是与客户建立有效链接。然而，您是否也正面临着这样的"链接鸿沟"？</p>

          <div class="pain-points">
            <div class="pain-item">
              <div class="pain-icon">
                <el-icon><Star /></el-icon>
              </div>
              <div class="pain-content">
                <h4>展示之痛："我是谁，我有多好"</h4>
                <p>面对新客户，如何才能不依赖人工，7x24小时地、专业且完整地讲清楚您的产品、服务与价值？</p>
              </div>
            </div>

            <div class="pain-item">
              <div class="pain-icon">
                <el-icon><TrendCharts /></el-icon>
              </div>
              <div class="pain-content">
                <h4>营销之痛："我的客户在哪里"</h4>
                <p>市场预算花出去了，曝光量上来了，但如何才能不做无效投入，精准地找到那些真正对您感兴趣的人？</p>
              </div>
            </div>

            <div class="pain-item">
              <div class="pain-icon">
                <el-icon><DataAnalysis /></el-icon>
              </div>
              <div class="pain-content">
                <h4>转化之痛："流量来了，然后呢"</h4>
                <p>公众号文章10万+，视频内容被点赞，但如何才能高效地将这些"访客"转化为真实的"客户"，不错过任何一个潜在商机？</p>
              </div>
            </div>
          </div>

          <div class="conclusion">
            <p>这些问题的本质，都指向企业与客户之间<strong>"链接"</strong>的效率与质量。</p>
          </div>
        </div>
      </section>

      <!-- 第二部分：品牌·我们的使命 -->
      <section class="mission-section">
        <div class="section-header">
          <div class="section-icon">
            <el-icon><Trophy /></el-icon>
          </div>
          <h2 class="section-title">品牌·我们的使命</h2>
        </div>

        <div class="content-card">
          <h3 class="card-title">智链——为您的企业，注入AI增长引擎</h3>
          <p class="mission-text">"智链"，并非一个单一的产品，而是一个致力于陪伴企业共同成长的、分阶段进化的AI赋能服务品牌。</p>

          <div class="mission-highlight">
            <div class="highlight-content">
              <h4>我们的核心使命只有一句：</h4>
              <div class="mission-quote">智能链接每一位客户</div>
            </div>
          </div>

          <p class="mission-promise">我们承诺，通过前沿的AI技术，为您打通从"高效展示"到"精准获客"，再到"深度运营"的全链路增长路径，让每一次对外接触，都成为一次价值的传递与生意的开始。</p>
        </div>
      </section>

      <!-- 第三部分：蓝图·我们的三阶进化路径 -->
      <section class="roadmap-section">
        <div class="section-header">
          <div class="section-icon">
            <el-icon><Promotion /></el-icon>
          </div>
          <h2 class="section-title">蓝图·我们的三阶进化路径</h2>
        </div>

        <div class="content-card">
          <h3 class="card-title">陪伴式成长：智链的三阶进化之旅</h3>
          <p class="roadmap-intro">我们深知您的成长需要按部就班。因此，"智链"为您规划了清晰的三阶进化路径，在您发展的每一个关键节点，提供最精准的AI助力。</p>

          <div class="phases">
            <div class="phase-item active">
              <div class="phase-header">
                <div class="phase-number">1</div>
                <div class="phase-info">
                  <h4>第一阶段：展示与链接</h4>
                  <p class="phase-subtitle">—— 让世界更好地认识您</p>
                </div>
                <div class="phase-status online">
                  <el-icon><Check /></el-icon>
                  <span>产品已上线</span>
                </div>
              </div>
              <div class="phase-content">
                <p><strong>核心目标：</strong> 解决您基础的"自我介绍"和"初步链接"问题。我们为您打造一个专业的"数字门面"和"AI发言人"，确保您的每一次亮相都专业、高效、令人印象深刻。</p>
              </div>
            </div>

            <div class="phase-item testing">
              <div class="phase-header">
                <div class="phase-number">2</div>
                <div class="phase-info">
                  <h4>第二阶段：营销与获客</h4>
                  <p class="phase-subtitle">—— 帮助您更精准地找到客户</p>
                </div>
                <div class="phase-status testing">
                  <el-icon><Timer /></el-icon>
                  <span>产品内测中</span>
                </div>
              </div>
              <div class="phase-content">
                <p><strong>核心目标：</strong> 解决您核心的"市场增长"和"获取新客"难题。在您拥有了专业的"门面"后，我们为您配备一位"首席AI营销顾问"，让您的营销投入更精准、回报更高。</p>
              </div>
            </div>

            <div class="phase-item future">
              <div class="phase-header">
                <div class="phase-number">3</div>
                <div class="phase-info">
                  <h4>第三阶段：运营与生态</h4>
                  <p class="phase-subtitle">—— 与您的客户建立深度连接</p>
                </div>
                <div class="phase-status future">
                  <el-icon><Service /></el-icon>
                  <span>未来愿景</span>
                </div>
              </div>
              <div class="phase-content">
                <p><strong>核心目标：</strong> 解决您长期的"客户经营"和"价值最大化"问题。我们将为您构建一位"智能化客户关系总监"，帮助您沉淀客户资产，实现可持续的私域增长。</p>
              </div>
            </div>
          </div>

          <div class="promise-section">
            <h4>我们的承诺：</h4>
            <div class="promise-list">
              <p>我们用第一阶段为您打下坚实地基，</p>
              <p>用第二阶段助您建起增长高楼，</p>
              <p>用第三阶段为您构筑长青护城河。</p>
            </div>
            <div class="final-message">
              <p>选择"智链"，就是选择一个能与您一同进化的AI战略伙伴。</p>
            </div>
          </div>
        </div>
      </section>
    </div>

    <TabBar />
  </div>
</template>

<style scoped>
.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8fafc;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  min-width: 100vw;
  box-sizing: border-box;
}

.header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, #1976d2, #42a5f5);
  color: white;
  height: 3rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 100;
  box-sizing: border-box;
}

.back-button {
  color: white;
  margin-right: 1rem;
  font-size: 1.25rem;
  padding: 0.5rem;
  position: absolute;
  left: 0.5rem;
  z-index: 1;
}

.header h1 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  width: 100%;
  text-align: center;
}

.content {
  flex: 1;
  padding-top: 4.5rem;
  padding-bottom: 5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
  padding-left: 1rem;
  padding-right: 1rem;
}

/* 通用样式 */
section {
  margin-bottom: 2rem;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
  gap: 0.75rem;
}

.section-icon {
  width: 2.5rem;
  height: 2.5rem;
  background: linear-gradient(135deg, #1976d2, #42a5f5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
  flex-shrink: 0;
}

.section-title {
  margin: 0;
  font-size: 1.4rem;
  font-weight: 600;
  color: #1976d2;
}

.content-card {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(25, 118, 210, 0.1);
}

.card-title {
  margin: 0 0 1rem 0;
  font-size: 1.3rem;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
}

/* 第一部分样式 */
.intro-text {
  font-size: 1rem;
  line-height: 1.6;
  color: #555;
  margin-bottom: 1.5rem;
}

.pain-points {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
  margin-bottom: 1.5rem;
}

.pain-item {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  background: #f8fbff;
  border-radius: 0.75rem;
  border-left: 4px solid #1976d2;
}

.pain-icon {
  width: 2rem;
  height: 2rem;
  background: linear-gradient(135deg, #1976d2, #42a5f5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1rem;
  flex-shrink: 0;
  margin-top: 0.2rem;
}

.pain-content h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1976d2;
}

.pain-content p {
  margin: 0;
  font-size: 0.95rem;
  line-height: 1.5;
  color: #666;
}

.conclusion {
  padding: 1rem;
  background: linear-gradient(135deg, rgba(25, 118, 210, 0.05), rgba(66, 165, 245, 0.05));
  border-radius: 0.75rem;
  border: 1px solid rgba(25, 118, 210, 0.2);
}

.conclusion p {
  margin: 0;
  font-size: 1rem;
  line-height: 1.5;
  color: #333;
  text-align: center;
}

.conclusion strong {
  color: #1976d2;
  font-weight: 600;
}

/* 第二部分样式 */
.mission-text {
  font-size: 1rem;
  line-height: 1.6;
  color: #555;
  margin-bottom: 1.5rem;
}

.mission-highlight {
  margin: 1.5rem 0;
  padding: 1.5rem;
  background: linear-gradient(135deg, #1976d2, #42a5f5);
  border-radius: 1rem;
  text-align: center;
}

.highlight-content h4 {
  margin: 0 0 1rem 0;
  color: white;
  font-size: 1.1rem;
  font-weight: 500;
}

.mission-quote {
  font-size: 1.5rem;
  font-weight: 600;
  color: white;
  line-height: 1.4;
  position: relative;
}

.mission-quote::before,
.mission-quote::after {
  content: '"';
  font-size: 2rem;
  opacity: 0.7;
}

.mission-promise {
  font-size: 1rem;
  line-height: 1.6;
  color: #555;
  margin: 0;
}

/* 第三部分样式 */
.roadmap-intro {
  font-size: 1rem;
  line-height: 1.6;
  color: #555;
  margin-bottom: 2rem;
}

.phases {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.phase-item {
  border-radius: 1rem;
  overflow: hidden;
  border: 2px solid #e3f2fd;
  transition: all 0.3s ease;
}

.phase-item.active {
  border-color: #1976d2;
  box-shadow: 0 4px 15px rgba(25, 118, 210, 0.15);
}

.phase-item.testing {
  border-color: #ff9800;
  box-shadow: 0 4px 15px rgba(255, 152, 0, 0.15);
}

.phase-item.future {
  border-color: #9e9e9e;
}

.phase-header {
  display: flex;
  align-items: center;
  padding: 1.25rem;
  background: #f8fbff;
  gap: 1rem;
}

.phase-item.active .phase-header {
  background: linear-gradient(135deg, rgba(25, 118, 210, 0.1), rgba(66, 165, 245, 0.05));
}

.phase-item.testing .phase-header {
  background: linear-gradient(135deg, rgba(255, 152, 0, 0.1), rgba(255, 193, 7, 0.05));
}

.phase-item.future .phase-header {
  background: linear-gradient(135deg, rgba(158, 158, 158, 0.1), rgba(189, 189, 189, 0.05));
}

.phase-number {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  font-weight: 600;
  color: white;
  flex-shrink: 0;
}

.phase-item.active .phase-number {
  background: linear-gradient(135deg, #1976d2, #42a5f5);
}

.phase-item.testing .phase-number {
  background: linear-gradient(135deg, #ff9800, #ffc107);
}

.phase-item.future .phase-number {
  background: linear-gradient(135deg, #9e9e9e, #bdbdbd);
}

.phase-info {
  flex: 1;
}

.phase-info h4 {
  margin: 0 0 0.25rem 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
}

.phase-subtitle {
  margin: 0;
  font-size: 0.9rem;
  color: #666;
  font-style: italic;
}

.phase-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 1.5rem;
  font-size: 0.85rem;
  font-weight: 500;
}

.phase-status.online {
  background: rgba(76, 175, 80, 0.1);
  color: #4caf50;
}

.phase-status.testing {
  background: rgba(255, 152, 0, 0.1);
  color: #ff9800;
}

.phase-status.future {
  background: rgba(158, 158, 158, 0.1);
  color: #9e9e9e;
}

.phase-content {
  padding: 1.25rem;
  background: white;
}

.phase-content p {
  margin: 0 0 0.75rem 0;
  font-size: 0.95rem;
  line-height: 1.5;
  color: #666;
}

.phase-content p:last-child {
  margin-bottom: 0;
}

.phase-content strong {
  color: #333;
  font-weight: 600;
}

.promise-section {
  padding: 1.5rem;
  background: linear-gradient(135deg, rgba(25, 118, 210, 0.05), rgba(66, 165, 245, 0.05));
  border-radius: 1rem;
  border: 1px solid rgba(25, 118, 210, 0.2);
}

.promise-section h4 {
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1976d2;
  text-align: center;
}

.promise-list {
  margin-bottom: 1rem;
}

.promise-list p {
  margin: 0.5rem 0;
  font-size: 1rem;
  line-height: 1.5;
  color: #555;
  text-align: center;
}

.final-message {
  padding-top: 1rem;
  border-top: 1px solid rgba(25, 118, 210, 0.2);
}

.final-message p {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1976d2;
  text-align: center;
  line-height: 1.4;
}

/* 响应式设计 */
@media (min-width: 768px) {
  .content {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .content-card {
    padding: 2rem;
  }

  .section-title {
    font-size: 1.6rem;
  }

  .card-title {
    font-size: 1.5rem;
  }

  .pain-points {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .mission-quote {
    font-size: 1.8rem;
  }

  .phases {
    gap: 2rem;
  }

  .phase-header {
    padding: 1.5rem;
  }

  .phase-content {
    padding: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .pain-points {
    grid-template-columns: 1fr;
  }

  .mission-quote {
    font-size: 2rem;
  }
}
</style>
