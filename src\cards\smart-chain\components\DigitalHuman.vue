<script setup lang="ts">
// 智链数字人组件
interface Props {
  imageUrl?: string
}

const props = withDefaults(defineProps<Props>(), {
  imageUrl: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/ZLSZR.png'
})
</script>

<template>
  <div class="digital-human">
    <img
      :src="props.imageUrl"
      alt="智链"
      class="digital-human-image"
    />
  </div>
</template>

<style scoped>
.digital-human {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.digital-human-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

@media (min-width: 768px) {
  .digital-human-image {
    width: auto;
    height: 100%;
    max-width: none;
  }
}
</style>
