import { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'productAiSalesHome',
    component: () => import('./views/HomeView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ChanPinAITuiXiaoGuan/LOGO.png',
      title: '产品AI推销官'
    }
  },
  {
    path: '/product-intro',
    name: 'productAiSalesProductIntro',
    component: () => import('./views/ProductIntroView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ChanPinAITuiXiaoGuan/LOGO.png',
      title: '产品AI推销官 - 产品介绍'
    }
  },
  {
    path: '/case-center',
    name: 'productAiSalesCaseCenter',
    component: () => import('./views/CaseCenterView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ChanPinAITuiXiaoGuan/LOGO.png',
      title: '产品AI推销官 - 案例中心'
    }
  }
]

export default routes
