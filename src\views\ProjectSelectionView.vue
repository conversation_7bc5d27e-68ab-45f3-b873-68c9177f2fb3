<template>
  <div class="project-selection-container">
    <div class="header-section">
      <h1 class="main-title">项目名片中心</h1>
      <p class="subtitle">请选择要查看的项目</p>
    </div>
    
    <div class="project-grid">
      <div class="project-card" @click="navigateTo('/card/zhi-lian')">
        <div class="card-content">
          <div class="icon-wrapper">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Project/logo.png" alt="智链项目" class="project-icon">
          </div>
          <div class="text-content">
            <h2>智链项目（旧）</h2>
            <p>智能链接生态系统</p>
          </div>
        </div>
      </div>
      
      <div class="project-card" @click="navigateTo('/card/gfq-scjgj')">
        <div class="card-content">
          <div class="icon-wrapper">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/GuangFengShiChang/logo.png" alt="广丰区市场监督管理局" class="project-icon">
          </div>
          <div class="text-content">
            <h2>广丰区市场监督管理局</h2>
            <p>规范市场秩序 保护知识产权</p>
          </div>
        </div>
      </div>
      
      <div class="project-card" @click="navigateTo('/card/houri-capital')">
        <div class="card-content">
          <div class="icon-wrapper">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/HouriLogo-1.jpg" alt="后日资本" class="project-icon">
          </div>
          <div class="text-content">
            <h2>后日资本</h2>
            <p>产业赋能 数字生态</p>
          </div>
        </div>
      </div>
      
      <div class="project-card" @click="navigateTo('/card/shida-erbao-tuoyu')">
        <div class="card-content">
          <div class="icon-wrapper">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ShiDaErBao/Logo.jpg" alt="师大儿保托育中心" class="project-icon">
          </div>
          <div class="text-content">
            <h2>师大儿保托育中心</h2>
            <p>专业托育 呵护成长</p>
          </div>
        </div>
      </div>
      
      <div class="project-card" @click="navigateTo('/card/csj-szrc-shangrao')">
        <div class="card-content">
          <div class="icon-wrapper">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/RenCaiJiDi/Logo.jpg" alt="长三角数字人才上饶创新基地" class="project-icon">
          </div>
          <div class="text-content">
            <h2>长三角数字人才上饶创新基地</h2>
            <p>人才培养 创新引领</p>
          </div>
        </div>
      </div>
      
      <div class="project-card" @click="navigateTo('/card/ai-sz-agent')">
        <div class="card-content">
          <div class="icon-wrapper">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/AIShuZhiDaiYanRen/Logo.jpg" alt="AI数智代言人" class="project-icon">
          </div>
          <div class="text-content">
            <h2>AI数智代言人</h2>
            <p>AI数智代言人 创新服务</p>
          </div>
        </div>
      </div>
      
      <div class="project-card" @click="navigateTo('/card/qiye-ai-mingpian')">
        <div class="card-content">
          <div class="icon-wrapper">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/qiyeAImingpianLogo.jpg" alt="企业AI宣传官" class="project-icon">
          </div>
          <div class="text-content">
            <h2>企业AI宣传官</h2>
            <p>AI宣传员 智能营销</p>
          </div>
        </div>
      </div>
      
      <div class="project-card" @click="navigateTo('/card/ai-sz-guide')">
        <div class="card-content">
          <div class="icon-wrapper">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/AIShuZhiTuiJieGuan/LOGO.jpeg" alt="AI数智推介官" class="project-icon">
          </div>
          <div class="text-content">
            <h2>AI数智推介官</h2>
            <p>智能推介 数字营销</p>
          </div>
        </div>
      </div>
      
      <div class="project-card" @click="navigateTo('/card/wisestar-tech')">
        <div class="card-content">
          <div class="icon-wrapper">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiYanXingChen/Logo.jpg" alt="杭州智衍星辰科技" class="project-icon">
          </div>
          <div class="text-content">
            <h2>杭州智衍星辰科技</h2>
            <p>智能科技 创新未来</p>
          </div>
        </div>
      </div>

      <div class="project-card" @click="navigateTo('/card/sr-media')">
        <div class="card-content">
          <div class="icon-wrapper">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ShangRaoChuanMei/logo.jpg" alt="上饶传媒集团" class="project-icon">
          </div>
          <div class="text-content">
            <h2>上饶传媒集团</h2>
            <p>传媒服务 智能互动</p>
          </div>
        </div>
      </div>

      <div class="project-card" @click="navigateTo('/card/product-ai-sales')">
        <div class="card-content">
          <div class="icon-wrapper">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ChanPinAITuiXiaoGuan/LOGO.png" alt="产品AI推销官" class="project-icon">
          </div>
          <div class="text-content">
            <h2>产品AI推销官</h2>
            <p>AI推销助手 智能营销</p>
          </div>
        </div>
      </div>

      <div class="project-card" @click="navigateTo('/card/smart-chain')">
        <div class="card-content">
          <div class="icon-wrapper">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Project/logo.png" alt="智链" class="project-icon">
          </div>
          <div class="text-content">
            <h2>智链</h2>
            <p>企业的智能宣传中枢</p>
          </div>
        </div>
      </div>

      <div class="project-card" @click="navigateTo('/card/WanWangKeJi')">
        <div class="card-content">
          <div class="icon-wrapper">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/WanWangKeJi/logo.png" alt="江西万网科技AI名片" class="project-icon">
          </div>
          <div class="text-content">
            <h2>江西万网科技AI名片</h2>
            <p>江西万网科技有限公司</p>
          </div>
        </div>
      </div>

      <div class="project-card" @click="navigateTo('/card/LinTianKeJi2')">
        <div class="card-content">
          <div class="icon-wrapper">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/LinTianKeJi/ZhiHuiWeiJiaXiao/logo1.jpg" alt="霖天科技AI名片" class="project-icon">
          </div>
          <div class="text-content">
            <h2>霖天科技AI名片</h2>
            <p>技术驱动价值，数据赋能未来</p>
          </div>
        </div>
      </div>
      <!-- 未来可以添加更多项目卡片 -->
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

const navigateTo = (route: string) => {
  router.push(route)
}
</script>

<style scoped>
.project-selection-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 100vh;
  padding: 2rem 1rem;
  background: linear-gradient(135deg, #f5f7fa, #e4e8f0);
}

.header-section {
  text-align: center;
  margin-bottom: 2rem;
}

.main-title {
  font-size: 2rem;
  font-weight: 600;
  color: #1b4283;
  margin-bottom: 0.5rem;
}

.subtitle {
  font-size: 1.1rem;
  color: #5a6a85;
  max-width: 600px;
}

.project-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
  width: 100%;
  max-width: 1200px;
}

.project-card {
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
  height: 100%;
}

.project-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 30px rgba(27, 66, 131, 0.15);
}

.card-content {
  display: flex;
  padding: 1.5rem;
  align-items: center;
}

.icon-wrapper {
  flex-shrink: 0;
  width: 60px;
  height: 60px;
  margin-right: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.project-icon {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 8px;
}

.text-content {
  flex-grow: 1;
}

.text-content h2 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 0.5rem 0;
}

.text-content p {
  font-size: 0.9rem;
  color: #666;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .project-grid {
    grid-template-columns: 1fr;
    max-width: 400px;
  }
  
  .main-title {
    font-size: 1.75rem;
  }
  
  .subtitle {
    font-size: 1rem;
  }
  
  .card-content {
    padding: 1.25rem;
  }
  
  .icon-wrapper {
    width: 50px;
    height: 50px;
  }
  
  .text-content h2 {
    font-size: 1.1rem;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .project-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1025px) {
  .project-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
</style> 