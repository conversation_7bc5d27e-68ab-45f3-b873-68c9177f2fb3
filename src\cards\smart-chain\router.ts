import { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'smartChainHome',
    component: () => import('./views/HomeView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Project/logo.png',
      title: '智链'
    }
  },
  {
    path: '/project-intro',
    name: 'smartChainProjectIntro',
    component: () => import('./views/ProjectIntroView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Project/logo.png',
      title: '智链 - 项目介绍'
    }
  },
  {
    path: '/project-services',
    name: 'smartChainProjectServices',
    component: () => import('./views/ProjectServicesView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Project/logo.png',
      title: '智链 - 项目服务'
    }
  }
]

export default routes
