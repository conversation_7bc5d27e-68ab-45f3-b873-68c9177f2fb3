<script setup lang="ts">
import { useRouter, onBeforeRouteLeave } from 'vue-router'
import { ArrowLeft, ArrowRight, Phone, Location, MessageBox, Message } from '@element-plus/icons-vue'
import { ref, onMounted } from 'vue'

const router = useRouter()

const goBack = () => {
  router.push('/card/wisestar-tech')
}

const chatWithAI = () => {
  // 在新页面打开链接，这里可以替换为实际的AI对话链接
  window.open('https://ai.sdtaa.com:3105/chat/share?shareId=p1vb1kt995vg7xgcxdpe493i', '_blank')
}

// 添加打字机效果
const welcomeText = ref('')
const fullText = '您好！我是智衍星辰的AI宣传员星辰，很高兴为您服务。想了解我们的业务、案例或寻求合作？请直接输入您的问题，我会7x24小时为您解答。'
const typingSpeed = 50
let currentIndex = 0
let typingTimer: number | null = null

const typeText = () => {
  if (currentIndex < fullText.length) {
    welcomeText.value += fullText.charAt(currentIndex)
    currentIndex++
    typingTimer = setTimeout(typeText, typingSpeed) as unknown as number
  }
}

onMounted(() => {
  document.title = '杭州智衍星辰科技 - AI宣传员'
  const link = document.querySelector("link[rel~='icon']") as HTMLLinkElement
  if (link) {
    link.href = 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiYanXingChen/Logo.jpg'
  } else {
    const newLink = document.createElement('link')
    newLink.rel = 'icon'
    newLink.href = 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiYanXingChen/Logo.jpg'
    document.head.appendChild(newLink)
  }
  
  setTimeout(() => {
    typeText()
  }, 500)
})

onBeforeRouteLeave(() => {
  if (typingTimer) {
    clearTimeout(typingTimer)
  }
})
</script>

<template>
  <div class="view-container">
    <div class="header">
      <el-button type="text" @click="goBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <h1>AI宣传员：星辰</h1>
    </div>

    <div class="content">
      <div class="ai-promoter-container">
        <!-- AI助手卡片 -->
        <div class="ai-card">
          <div class="ai-image-container">
            <div class="ai-image"></div>
            <div class="image-overlay"></div>
          </div>
          <div class="ai-content">
            <h2>星辰 <span class="badge">AI 智能宣传员</span></h2>
            <div class="typing-container">
              <p class="welcome-text">{{ welcomeText }}<span class="cursor" v-if="welcomeText.length < fullText.length">|</span></p>
            </div>
            <div class="action-container">
              <el-button type="primary" class="chat-btn" @click="chatWithAI">
                立即对话星辰
                <el-icon><ArrowRight /></el-icon>
              </el-button>
            </div>
          </div>
        </div>
        
        <!-- 人工联系方式部分 -->
        <div class="contact-section">
          <h3>需要人工服务？</h3>
          <div class="contact-card">
            <div class="contact-header">
              <el-icon class="contact-icon"><MessageBox /></el-icon>
              <h4>人工联系方式</h4>
            </div>
            <div class="contact-info">
              <div class="info-item">
                <el-icon><Phone /></el-icon>
                <span>联系电话：18479656459</span>
              </div>
              <div class="info-item">
                <el-icon><Message /></el-icon>
                <span>电子邮箱：<EMAIL></span>
              </div>
              <div class="info-item">
                <el-icon><Location /></el-icon>
                <span>地址：浙江省杭州市</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f9ff;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  min-width: 100vw;
  box-sizing: border-box;
}

.header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, #0D47A1, #2196F3);
  color: white;
  height: 3rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 100;
  box-sizing: border-box;
}

.back-button {
  color: white;
  margin-right: 1rem;
  font-size: 1.25rem;
  padding: 0.5rem;
  position: absolute;
  left: 0.5rem;
  z-index: 1;
}

.header h1 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  width: 100%;
  text-align: center;
}

.content {
  flex: 1;
  padding-top: 4.5rem;
  padding-bottom: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
  padding-left: 1rem;
  padding-right: 1rem;
}

.ai-promoter-container {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* AI助手卡片样式 */
.ai-card {
  background: white;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
}

.ai-image-container {
  position: relative;
  height: 300px;
  overflow: hidden;
}

.ai-image {
  width: 100%;
  height: 100%;
  background: url('https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/ZLSZR.png') center top/cover;
}

.image-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 80px;
  background: linear-gradient(to top, white, transparent);
}

.ai-content {
  padding: 1.5rem;
}

.ai-content h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #0D47A1;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.badge {
  background: linear-gradient(135deg, #0D47A1, #2196F3);
  color: white;
  font-size: 0.7rem;
  padding: 0.2rem 0.5rem;
  border-radius: 0.25rem;
  font-weight: 500;
  display: inline-block;
  margin-top: 0.2rem;
}

.typing-container {
  margin-top: 1rem;
  min-height: 4.5rem;
}

.welcome-text {
  font-size: 1rem;
  line-height: 1.5;
  color: #333;
  margin: 0;
}

.cursor {
  display: inline-block;
  width: 2px;
  height: 1.2em;
  background-color: #0D47A1;
  animation: blink 1s infinite;
  vertical-align: middle;
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
}

.action-container {
  margin-top: 1.5rem;
  display: flex;
  justify-content: center;
}

.chat-btn {
  background: linear-gradient(135deg, #0D47A1, #2196F3);
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 2rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1rem;
  font-weight: 500;
  box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
  transition: all 0.3s ease;
}

.chat-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(33, 150, 243, 0.4);
}

/* 人工联系方式部分样式 */
.contact-section {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.contact-section h3 {
  margin: 0 0 1.5rem 0;
  font-size: 1.2rem;
  color: #0D47A1;
  text-align: center;
  font-weight: 600;
}

.contact-card {
  background: rgba(33, 150, 243, 0.05);
  border-radius: 0.75rem;
  padding: 1.5rem;
  border-left: 4px solid #1976D2;
}

.contact-header {
  display: flex;
  align-items: center;
  margin-bottom: 1.25rem;
  gap: 0.75rem;
}

.contact-icon {
  color: #1976D2;
  font-size: 1.5rem;
  background: rgba(25, 118, 210, 0.1);
  padding: 0.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
}

.contact-header h4 {
  margin: 0;
  font-size: 1.1rem;
  color: #0D47A1;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.info-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

.info-item .el-icon {
  color: #1976D2;
  font-size: 1.1rem;
  margin-top: 0.1rem;
  flex-shrink: 0;
}

.info-item span {
  color: #333;
  font-size: 0.95rem;
  line-height: 1.4;
}

/* 媒体查询-平板和桌面端 */
@media (min-width: 768px) {
  .ai-card {
    flex-direction: row;
    max-height: 350px;
  }
  
  .ai-image-container {
    width: 40%;
    height: auto;
  }
  
  .ai-content {
    width: 60%;
    padding: 2rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  
  .image-overlay {
    display: none;
  }
  
  .contact-section {
    padding: 2rem;
  }
  
  .contact-card {
    padding: 1.75rem;
  }
  
  .contact-info {
    padding-left: 1rem;
  }
}

/* 超大屏幕 */
@media (min-width: 1200px) {
  .content {
    padding-left: 2rem;
    padding-right: 2rem;
  }
  
  .ai-promoter-container {
    gap: 2.5rem;
  }
}
</style> 