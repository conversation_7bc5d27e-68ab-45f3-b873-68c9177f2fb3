<script setup lang="ts">
</script>

<template>
  <div class="digital-human">
    <div class="image-container">
      <img 
        src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/HouRiZiBen/HouRiShuZiRen.png"
        alt="后日资本数字人" 
        class="digital-human-image"
      >
      <div class="logo-container">
        <div class="logo-image"></div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.digital-human {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.image-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  position: relative;
}

.digital-human-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.logo-container {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 10;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  padding: 6px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  width: 90px;
  height: 90px;
  overflow: hidden;
}

.logo-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-image: url('https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/HouriLogo-1.jpg');
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
}

@media (min-width: 768px) {
  .digital-human-image {
    object-position: center 10%;
  }
  
  .logo-container {
    width: 80px;
    height: 80px;
    top: 30px;
    left: 30px;
    padding: 5px;
  }
}

@media (min-width: 1200px) {
  .logo-container {
    width: 90px;
    height: 90px;
  }
}
</style> 