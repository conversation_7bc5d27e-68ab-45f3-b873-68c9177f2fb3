<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { 
  HomeFilled, 
  ShoppingBag
} from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()

const tabs = ref([
  {
    id: 'home',
    name: '主页',
    icon: HomeFilled,
    route: '/card/sr-media'
  },
  {
    id: 'product-center',
    name: '产品中心',
    icon: ShoppingBag,
    route: '/card/sr-media/product-center'
  }
])

const currentPath = computed(() => route.path)

const isActive = (tabRoute: string) => {
  return currentPath.value === tabRoute
}

const navigateTo = (tabRoute: string) => {
  router.push(tabRoute)
}
</script>

<template>
  <div class="tab-bar-container">
    <div class="tab-bar">
      <div 
        v-for="tab in tabs" 
        :key="tab.id" 
        class="tab-item"
        :class="{ active: isActive(tab.route) }"
        @click="navigateTo(tab.route)"
      >
        <el-icon class="tab-icon"><component :is="tab.icon" /></el-icon>
        <span class="tab-name">{{ tab.name }}</span>
      </div>
    </div>
    <div class="safe-area-bottom"></div>
  </div>
</template>

<style scoped>
.tab-bar-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
}

.tab-bar {
  display: flex;
  justify-content: space-around;
  align-items: center;
  height: 3.5rem;
  padding: 0 1rem;
}

.tab-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  height: 100%;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #a0a0a0;
}

.tab-item.active {
  color: #3b82f6cc;
}

.tab-icon {
  font-size: 1.4rem;
  margin-bottom: 0.2rem;
}

.tab-name {
  font-size: 0.7rem;
  font-weight: 500;
}

.safe-area-bottom {
  height: env(safe-area-inset-bottom, 0);
  width: 100%;
}

/* 手机端隐藏底部导航栏 */
@media (max-width: 767px) {
  .tab-bar-container {
    display: none;
  }
}
</style>
