import { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'wisestarTechHome',
    component: () => import('./views/HomeView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiYanXingChen/Logo.jpg',
      title: '杭州智衍星辰科技'
    }
  },
  {
    path: '/introduction',
    name: 'wisestarTechIntroduction',
    component: () => import('./views/IntroductionView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiYanXingChen/Logo.jpg',
      title: '杭州智衍星辰科技 - 公司介绍'
    }
  },
  {
    path: '/ai-promoter',
    name: 'wisestarTechAIPromoter',
    component: () => import('./views/AIPromoterView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiYanXingChen/Logo.jpg',
      title: '杭州智衍星辰科技 - AI宣传员'
    }
  },
  {
    path: '/products',
    name: 'wisestarTechProducts',
    component: () => import('./views/ProductsView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiYanXingChen/Logo.jpg',
      title: '杭州智衍星辰科技 - 产品中心'
    }
  },
  {
    path: '/cases',
    name: 'wisestarTechCases',
    component: () => import('./views/CasesView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiYanXingChen/Logo.jpg',
      title: '杭州智衍星辰科技 - 案例中心'
    }
  }
]

export default routes 