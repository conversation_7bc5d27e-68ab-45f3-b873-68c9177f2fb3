<script setup lang="ts">
import { ref, onMounted } from 'vue'
import DigitalHuman from '../components/DigitalHuman.vue'
import NavigationButtons from '../components/NavigationButtons.vue'

const slogan = ref('您好！我是杭州智衍星辰的AI宣传员星辰！')

onMounted(() => {
  document.title = '杭州智衍星辰科技'
  const link = document.querySelector("link[rel~='icon']") as HTMLLinkElement
  if (link) {
    link.href = 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiYanXingChen/Logo.jpg'
  } else {
    const newLink = document.createElement('link')
    newLink.rel = 'icon'
    newLink.href = 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiYanXingChen/Logo.jpg'
    document.head.appendChild(newLink)
  }
})
</script>

<template>
  <div class="home-container">
    <div class="digital-human-container">
      <div class="digital-human-wrapper">
        <DigitalHuman />
      </div>
    </div>
    
    <div class="bottom-section">
      <div class="content-wrapper">
        <div class="slogan-container">
          <div class="slogan-wrapper">
            <h1 class="slogan">{{ slogan }}</h1>
            <div class="tech-line"></div>
          </div>
        </div>
        
        <div class="navigation-container">
          <NavigationButtons />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.home-container {
  position: relative;
  height: 100vh;
  max-height: 100vh;
  overflow: hidden;
}

.digital-human-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.digital-human-wrapper {
  width: 100%;
  height: 100%;
}

.bottom-section {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, transparent, rgba(255, 255, 255, 0.85) 15%, rgba(255, 255, 255, 0.95));
  z-index: 2;
  backdrop-filter: blur(5px);
  height: 45%; /* 增加手机端高度 */
}

.content-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  padding-top: 1rem;
}

.slogan-container {
  padding: 1rem 1rem 0.5rem 1rem;
  display: flex;
  justify-content: center;
}

.slogan-wrapper {
  position: relative;
  max-width: 340px;
  padding-bottom: 0.5rem;
}

.tech-line {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(to right, transparent, #1976D2 30%, #1976D2 70%, transparent);
}

.slogan {
  font-size: 1.25rem;
  line-height: 1.5;
  background: linear-gradient(135deg, #0D47A1, #2196F3);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
  text-align: center;
  font-weight: 500;
}

.navigation-container {
  padding: 1rem 1rem 1.5rem 1rem;
}

@media (min-width: 768px) {
  .bottom-section {
    height: 50%; /* 减小桌面端高度 */
  }
  
  .content-wrapper {
    justify-content: center;
    padding-bottom: 2rem; /* 底部留出空间 */
    padding-top: 1rem; /* 减少顶部空间，使内容往下移 */
  }
  
  .digital-human-container {
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
  }
  
  .digital-human-wrapper {
    width: 100%;
    height: 100%;
    min-height: 100vh;
    max-width: none;
    display: flex;
    justify-content: center;
  }
  
  .digital-human-wrapper :deep(img),
  .digital-human-wrapper :deep(video) {
    height: 100vh;
    width: 100vw;
    object-fit: cover;
    object-position: center;
  }
  
  .slogan-wrapper {
    max-width: 700px;
    padding-bottom: 0.75rem;
  }

  .slogan {
    font-size: 1.75rem;
    background: linear-gradient(135deg, #1565C0, #64B5F6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .slogan-container {
    padding: 0 1.5rem 0.5rem 1.5rem;
    margin-bottom: 1rem; /* 减少与导航按钮的间距 */
  }
  
  .navigation-container {
    padding: 0 1.5rem 0 1.5rem;
  }
}

@media (min-width: 1200px) {
  .bottom-section {
    height: 55%; /* 减小大屏幕设备高度 */
  }
  
  .content-wrapper {
    padding-bottom: 3rem; /* 大屏幕底部留出更多空间 */
    padding-top: 2rem; /* 减少大屏幕顶部空间 */
  }
  
  .slogan-container {
    margin-bottom: 1.5rem; /* 减少大屏幕的间距 */
  }
  
  .slogan {
    font-size: 2rem; /* 大屏幕增加字体大小 */
  }
  
  .slogan-wrapper {
    max-width: 800px;
  }
}

@media (max-width: 767px) {
  .bottom-section {
    height: 50%; /* 增加手机端底部区域高度，使内容更靠上 */
  }
  
  .content-wrapper {
    justify-content: center; /* 在手机端使内容垂直居中 */
    padding-top: 0; /* 移除顶部内边距 */
  }
}
</style> 