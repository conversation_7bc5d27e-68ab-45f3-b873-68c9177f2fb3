import { createRouter, createWebHistory } from 'vue-router'
import zhilianRoutes from '../cards/zhi-lian/router'
import gfqScjgjRoutes from '../cards/gfq-scjgj/router'
import houriCapitalRoutes from '../cards/houri-capital/router'
import shidaErbaoRoutes from '../cards/shida-erbao-tuoyu/router'
import csjSzrcShangraoRoutes from '../cards/csj-szrc-shangrao/router'
import aiSzAgentRoutes from '../cards/ai-sz-agent/router'
import qiyeAIMingpianRoutes from '../cards/qiye-ai-mingpian/router'
import aiSzGuideRoutes from '../cards/ai-sz-guide/router'
import wisestarTechRoutes from '../cards/wisestar-tech/router'
import srMediaRoutes from '../cards/sr-media/router'
import productAiSalesRoutes from '../cards/product-ai-sales/router'
import smartChainRoutes from '../cards/smart-chain/router'
import wanWangKeJiRoutes from '../cards/WanWangKeJi/router'
import linTianKeJi2Routes from '../cards/LinTianKeJi2/router'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    // 主页路由 - 项目选择页面
    {
      path: '/',
      name: 'projectSelection',
      component: () => import('../views/ProjectSelectionView.vue'),
      meta: {
        title: '项目选择 - 项目名片中心'
      }
    },
    // 将智链项目的路由添加到 /card/zhi-lian 前缀下
    ...zhilianRoutes.map(route => ({
      ...route,
      path: `/card/zhi-lian${route.path}`
    })),
    
    // 将广丰区市场监督管理局的路由添加到 /card/gfq-scjgj 前缀下
    ...gfqScjgjRoutes.map(route => ({
      ...route,
      path: `/card/gfq-scjgj${route.path}`
    })),
    
    // 将后日资本的路由添加到 /card/houri-capital 前缀下
    ...houriCapitalRoutes.map(route => ({
      ...route,
      path: `/card/houri-capital${route.path}`
    })),
    
    // 将师大儿保托育中心的路由添加到 /card/shida-erbao-tuoyu 前缀下
    ...shidaErbaoRoutes.map(route => ({
      ...route,
      path: `/card/shida-erbao-tuoyu${route.path}`
    })),
    
    // 将长三角数字人才上饶创新基地的路由添加到 /card/csj-szrc-shangrao 前缀下
    ...csjSzrcShangraoRoutes.map(route => ({
      ...route,
      path: `/card/csj-szrc-shangrao${route.path}`
    })),
    
    // 将AI数智代言人的路由添加到 /card/ai-sz-agent 前缀下
    ...aiSzAgentRoutes.map(route => ({
      ...route,
      path: `/card/ai-sz-agent${route.path}`
    })),
    
    // 将企业AI宣传官的路由添加到 /card/qiye-ai-mingpian 前缀下
    ...qiyeAIMingpianRoutes.map(route => ({
      ...route,
      path: `/card/qiye-ai-mingpian${route.path}`
    })),
    
    // 将AI数智推介官的路由添加到 /card/ai-sz-guide 前缀下
    ...aiSzGuideRoutes.map(route => ({
      ...route,
      path: `/card/ai-sz-guide${route.path}`
    })),
    
    // 将杭州智衍星辰科技的路由添加到 /card/wisestar-tech 前缀下
    ...wisestarTechRoutes.map(route => ({
      ...route,
      path: `/card/wisestar-tech${route.path}`
    })),

    // 将上饶传媒集团的路由添加到 /card/sr-media 前缀下
    ...srMediaRoutes.map(route => ({
      ...route,
      path: `/card/sr-media${route.path}`
    })),

    // 将产品AI推销官的路由添加到 /card/product-ai-sales 前缀下
    ...productAiSalesRoutes.map(route => ({
      ...route,
      path: `/card/product-ai-sales${route.path}`
    })),

    // 将智链的路由添加到 /card/smart-chain 前缀下
    ...smartChainRoutes.map(route => ({
      ...route,
      path: `/card/smart-chain${route.path}`
    })),

    
    // 将万网科技的路由添加到 /card/WanWangKeJi 前缀下
    ...wanWangKeJiRoutes.map(route => ({
      ...route,
      path: `/card/WanWangKeJi${route.path}`
    })),


    // 将霖天科技2的路由添加到 /card/LinTianKeJi2 前缀下
    ...linTianKeJi2Routes.map(route => ({
     ...route,
     path: `/card/LinTianKeJi2${route.path}`
    }))
  ],
  // 添加滚动行为控制
  scrollBehavior(_to, _from, _savedPosition) {
    // 始终滚动到顶部
    return { top: 0 }
  }
})

export default router