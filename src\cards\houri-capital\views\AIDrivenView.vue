<script setup lang="ts">
import { useRouter } from 'vue-router'
import { 
  ArrowLeft, 
  Connection,
  Histogram, 
  TrendCharts,
  Coordinate, 
  OfficeBuilding, 
  Lightning,
  Share,
  Opportunity,
  DataLine,
  Operation
} from '@element-plus/icons-vue'
import { ref, onMounted } from 'vue'
import ImageViewer from '../../../components/ImageViewer.vue'

const router = useRouter()

const goBack = () => {
  router.push('/card/houri-capital')
}

// 视频播放控制
const videoRef = ref<HTMLVideoElement | null>(null)
const isPlaying = ref(false)

const toggleVideo = () => {
  if (videoRef.value) {
    if (isPlaying.value) {
      videoRef.value.pause()
    } else {
      videoRef.value.play()
    }
    isPlaying.value = !isPlaying.value
  }
}

// 图片查看器相关状态
const imageViewerVisible = ref(false)
const currentImage = ref('')

// 打开图片查看器
const openImageViewer = (imageUrl: string) => {
  currentImage.value = imageUrl
  imageViewerVisible.value = true
}

onMounted(() => {
  document.title = '后日资本 - AI驱动：协同增效'
})
</script>

<template>
  <div class="view-container">
    <div class="header">
      <el-button type="text" @click="goBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <h1>AI驱动：协同增效</h1>
    </div>

    <div class="content">
      <div class="ai-driven-container">
        <!-- 视频部分 -->
        <div class="video-section">
          <div class="video-container">
            <video 
              ref="videoRef" 
              src="https://pic.sdtaa.com/ZhiLian/Video/Enterprise/HouRiZiBen/xcsp2.mp4" 
              class="feature-video" 
              @click="toggleVideo"
              poster="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/HouriLogo-1.jpg"
              controls
            ></video>
          </div>
        </div>

        <!-- 方法论部分 -->
        <div class="section methodology-section">
          <div class="section-header">
            <el-icon class="section-icon"><Share /></el-icon>
            <h2>我们的方法论：AIx产业协同，构建多维生态</h2>
          </div>
          <div class="section-content">
            <div class="image-text-container">
              <div class="section-image">
                <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/HouRiZiBen/AIXieTong.png" alt="AIx产业协同" @click="openImageViewer('https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/HouRiZiBen/AIXieTong.png')" />
              </div>
              <div class="section-text">
                <p>后日资本深知，在当前复杂的经济形势下，区域产业发展和资本赋能需要全新的范式。我们凭借深厚的产业研究功底、丰富的投融资经验以及强大的资源整合能力，积极探索AI时代下的新模式，并将其凝练为核心方法论——"AIx产业协同"。</p>
                <p>这是一种超越传统招商引资和产业集聚的智慧化、生态化方法。我们构建起"产、融、智、政"四维联动的产业生态：将产业实际需求、金融资本活力、智能技术支撑与政府治理效能紧密耦合。这不仅为区域政府提供了全方位、一体化的产业赋能服务，更是响应国家战略、以市场需求为导向、以科技创新为驱动的关键路径，旨在发展新质生产力，培育壮大新兴产业。</p>
                <p>我们携手地方政府、行业龙头企业、顶尖科研机构及各类投资伙伴，共同致力于打造新的产业增长极。通过这种多维度、紧密协作的生态构建，我们极大地提升了产业协同的效率和深度，为区域经济的高质量发展注入澎湃动力。</p>
              </div>
            </div>
          </div>
        </div>

        <!-- AI能力部分 -->
        <div class="section ai-capability-section">
          <div class="section-header">
            <el-icon class="section-icon"><Opportunity /></el-icon>
            <h2>AI能力：精准洞察与决策支持</h2>
          </div>
          <div class="section-content">
            <div class="capability-grid">
              <div class="capability-card">
                <div class="capability-icon">
                  <el-icon><Histogram /></el-icon>
                </div>
                <h3>DeepSeek智能分析引擎</h3>
                <p>日均处理<span class="highlight">47亿</span>条产业相关数据，构建动态、鲜活的产业知识图谱，精准定位产业爆发点。</p>
              </div>
              <div class="capability-card">
                <div class="capability-icon">
                  <el-icon><TrendCharts /></el-icon>
                </div>
                <h3>风险预警矩阵</h3>
                <p>提前<span class="highlight">6个月</span>预判产业链潜在断点，结合创新的决策沙盒系统，推演准确率高达<span class="highlight">91.2%</span>。</p>
              </div>
              <div class="capability-card">
                <div class="capability-icon">
                  <el-icon><DataLine /></el-icon>
                </div>
                <h3>数据要素化</h3>
                <p>响应数据要素国家战略，通过AI技术推动数据要素化和数据资产化进程，最大限度挖掘数据资产价值。</p>
              </div>
            </div>
            <p class="capability-summary">持续不断的研发投入确保我们的AI技术始终处于行业前沿，为产业协同提供坚实的底层技术支撑，实现信息萃取和底层逻辑的优化。</p>
          </div>
        </div>

        <!-- AI应用部分 -->
        <div class="section ai-application-section">
          <div class="section-header">
            <el-icon class="section-icon"><Operation /></el-icon>
            <h2>AI应用：优化招商与资源匹配</h2>
          </div>
          <div class="section-content">
            <div class="application-item">
              <div class="application-icon">
                <el-icon><Coordinate /></el-icon>
              </div>
              <div class="application-content">
                <h3>AI产业导航与定制方案</h3>
                <p>基于AI对数智经济、绿色能源、轻新材料等核心赛道趋势的深度洞察，我们能够为您提供动态的产业图谱和前瞻性的招商决策支持。结合顶尖智库力量，我们能为您的区域定制"一链一策"发展方案，实现"数据导航、精准落子"，预测技术迭代与市场拐点，降低布局风险，从而大幅提升区域产业竞争力和招商吸引力，促进产业升级与转型。</p>
              </div>
            </div>
            <div class="application-item">
              <div class="application-icon">
                <el-icon><OfficeBuilding /></el-icon>
              </div>
              <div class="application-content">
                <h3>智能招商投促与产业链优化</h3>
                <p>AI算法能够优化产业链资源匹配，打通全国产业资本网络，实现"需求-供给"的极速响应。我们能够精准链接您区域所需的龙头企业、独角兽企业等优质项目资源，提供包括项目筛选、对接、落地在内的全流程智能招商投促服务。通过制定个性化招商策略，显著提升招商吸引力和成功率，助力补链强链，推动产业集聚发展。</p>
              </div>
            </div>
            <div class="application-item">
              <div class="application-icon">
                <el-icon><Connection /></el-icon>
              </div>
              <div class="application-content">
                <h3>构建高效可信协同生态</h3>
                <p>我们运用包括区块链在内的先进技术，强化"五信工程"在跨区域协作中的可信度和效率，构建高效、可信的合作生态。这有助于降低时空成本，实现快速响应和极简中枢管理，最终形成协同组合和生态资源的优化配置。</p>
              </div>
            </div>
            <p class="application-summary">通过AI技术的全方位应用，后日资本帮助客户提质增效、降本节能，实现价值闭环，推动区域产业实现量变到质变的飞跃，与各方价值共生，共同迎接产业发展的新未来。</p>
          </div>
        </div>

        <!-- 案例实践部分 -->
        <div class="section case-study-section">
          <div class="section-header">
            <el-icon class="section-icon"><Lightning /></el-icon>
            <h2>案例实践：AI赋能印证</h2>
          </div>
          <div class="section-content">
            <p class="case-intro">AI驱动的产业协同，已在多个实践中得到有效验证：</p>
            <div class="case-grid">
              <div class="case-card">
                <div class="case-icon">
                  <el-icon><OfficeBuilding /></el-icon>
                </div>
                <div class="case-content">
                  <h3>长三角工业大市</h3>
                  <div class="case-stats">
                    <div class="stat-item">
                      <span class="stat-value">12</span>
                      <span class="stat-label">AI工厂</span>
                    </div>
                    <div class="stat-item">
                      <span class="stat-value">9.7<span class="percent">%</span></span>
                      <span class="stat-label">利润率提升</span>
                    </div>
                  </div>
                  <p>助力某工业大市12个月内落地12家AI工厂，规上工业利润率提升至<span class="highlight">9.7%</span>，实现产业智能化升级。</p>
                </div>
              </div>
              <div class="case-card">
                <div class="case-icon">
                  <el-icon><Lightning /></el-icon>
                </div>
                <div class="case-content">
                  <h3>西部新能源城市</h3>
                  <div class="case-stats">
                    <div class="stat-item">
                      <span class="stat-value">31<span class="percent">%</span></span>
                      <span class="stat-label">度电成本下降</span>
                    </div>
                    <div class="stat-item">
                      <span class="stat-value">240<span class="percent">%</span></span>
                      <span class="stat-label">产业集群估值年增长</span>
                    </div>
                  </div>
                  <p>为某新能源枢纽城市构建产业生态，实现度电成本下降<span class="highlight">31%</span>，产业集群估值年增长<span class="highlight">240%</span>，打造绿色能源产业标杆。</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 使用图片查看器组件 -->
    <ImageViewer 
      v-model:visible="imageViewerVisible" 
      :image-url="currentImage"
    />
  </div>
</template>

<style scoped>
.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8faf3;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  min-width: 100vw;
  box-sizing: border-box;
}

.header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, #7e8f4e, #9fb25f);
  color: white;
  height: 3rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 100;
  box-sizing: border-box;
}

.back-button {
  color: white;
  margin-right: 1rem;
  font-size: 1.25rem;
  padding: 0.5rem;
  position: absolute;
  left: 0.5rem;
  z-index: 1;
}

.header h1 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  width: 100%;
  text-align: center;
}

.content {
  flex: 1;
  padding-top: 4.5rem;
  padding-bottom: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
  padding-left: 1rem;
  padding-right: 1rem;
}

.ai-driven-container {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

/* 视频部分 */
.video-section {
  width: 100%;
  margin-bottom: 0.5rem;
}

.video-container {
  width: 100%;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  background-color: #000;
  position: relative;
  padding-top: 56.25%; /* 16:9 宽高比 */
}

.feature-video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
}

/* 通用部分样式 */
.section {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  margin-bottom: 0.5rem;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
  border-bottom: 1px solid rgba(126, 143, 78, 0.2);
  padding-bottom: 1rem;
}

.section-icon {
  color: #7e8f4e;
  font-size: 1.5rem;
  margin-right: 0.75rem;
}

.section h2 {
  color: #7e8f4e;
  margin: 0;
  font-size: 1.3rem;
  font-weight: 600;
}

.section-content {
  color: #555;
  line-height: 1.6;
}

/* 方法论部分 */
.image-text-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.section-image {
  width: 100%;
  border-radius: 0.75rem;
  overflow: hidden;
}

.section-image img {
  width: 100%;
  height: auto;
  display: block;
  cursor: pointer; /* 添加指针样式表明可点击 */
}

.section-text p {
  margin-bottom: 1rem;
}

/* AI能力部分 */
.capability-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.capability-card {
  background: rgba(248, 250, 243, 0.6);
  border-radius: 0.75rem;
  padding: 1.5rem;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.capability-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.05);
}

.capability-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: rgba(126, 143, 78, 0.15);
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.capability-icon .el-icon {
  font-size: 30px;
  color: #7e8f4e;
}

.capability-card h3 {
  color: #7e8f4e;
  margin: 0 0 0.75rem;
  font-size: 1.1rem;
}

.capability-card p {
  margin: 0;
  color: #666;
}

.capability-summary {
  text-align: center;
  font-style: italic;
  color: #666;
}

.highlight {
  color: #7e8f4e;
  font-weight: 600;
}

/* AI应用部分 */
.application-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 1.5rem;
  background: rgba(248, 250, 243, 0.6);
  border-radius: 0.75rem;
  padding: 1.5rem;
}

.application-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: rgba(126, 143, 78, 0.15);
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.application-icon .el-icon {
  font-size: 30px;
  color: #7e8f4e;
}

.application-content {
  text-align: center;
}

.application-content h3 {
  color: #7e8f4e;
  margin: 0 0 0.75rem;
  font-size: 1.1rem;
}

.application-content p {
  margin: 0;
  color: #666;
}

.application-summary {
  text-align: center;
  font-style: italic;
  color: #666;
  margin-top: 1rem;
}

/* 案例实践部分 */
.case-intro {
  text-align: center;
  margin-bottom: 1.5rem;
  font-size: 1.1rem;
}

.case-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.case-card {
  background: white;
  border-radius: 0.75rem;
  padding: 1.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border-left: 4px solid #7e8f4e;
  display: flex;
  flex-direction: column;
}

.case-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
}

.case-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: rgba(126, 143, 78, 0.15);
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  align-self: center;
}

.case-icon .el-icon {
  font-size: 30px;
  color: #7e8f4e;
}

.case-content {
  text-align: center;
}

.case-content h3 {
  color: #7e8f4e;
  margin: 0 0 1rem;
  font-size: 1.2rem;
  position: relative;
  padding-bottom: 0.5rem;
}

.case-content h3::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 2px;
  background-color: #7e8f4e;
}

.case-stats {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  margin-bottom: 1rem;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: #7e8f4e;
  line-height: 1;
}

.percent {
  font-size: 1rem;
  vertical-align: super;
}

.stat-label {
  font-size: 0.85rem;
  color: #666;
  margin-top: 0.25rem;
}

.case-content p {
  margin: 0.75rem 0 0;
  color: #666;
}

.case-summary {
  text-align: center;
  font-style: italic;
  color: #666;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px dashed rgba(126, 143, 78, 0.3);
}

@media (min-width: 768px) {
  .content {
    padding-top: 5rem;
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .image-text-container {
    flex-direction: row;
    align-items: center;
  }

  .section-image {
    width: 40%;
    flex-shrink: 0;
  }

  .section-text {
    width: 60%;
  }

  .capability-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .application-item {
    flex-direction: row;
    text-align: left;
  }

  .application-icon {
    margin-right: 1.5rem;
    margin-bottom: 0;
  }

  .application-content {
    text-align: left;
  }

  .case-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .case-card {
    flex-direction: row;
    text-align: left;
    align-items: center;
  }
  
  .case-icon {
    margin-right: 1.5rem;
    margin-bottom: 0;
    flex-shrink: 0;
  }
  
  .case-content {
    text-align: left;
  }
  
  .case-content h3::after {
    left: 0;
    transform: none;
  }
  
  .case-stats {
    justify-content: flex-start;
  }
}
</style> 