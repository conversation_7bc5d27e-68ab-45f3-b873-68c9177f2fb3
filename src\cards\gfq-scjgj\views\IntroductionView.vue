<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { ArrowLeft, CopyDocument, Phone } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const router = useRouter()

const goBack = () => {
  router.push('/card/gfq-scjgj')
}

const copyPhoneNumber = (phone: string) => {
  // 创建临时文本区域元素
  const textArea = document.createElement('textarea')
  textArea.value = phone
  // 设置样式使元素不可见
  textArea.style.position = 'fixed'
  textArea.style.left = '-999999px'
  textArea.style.top = '-999999px'
  document.body.appendChild(textArea)
  
  // 选择文本并复制
  textArea.focus()
  textArea.select()
  
  let successful = false
  try {
    successful = document.execCommand('copy')
  } catch (err) {
    console.error('复制失败:', err)
  }
  
  // 移除临时元素
  document.body.removeChild(textArea)
  
  // 显示提示信息
  if (successful) {
    ElMessage({
      message: '电话号码已复制到剪贴板',
      type: 'success',
      duration: 2000
    })
  } else {
    ElMessage({
      message: '复制失败，请手动复制',
      type: 'error',
      duration: 2000
    })
  }
}

const callPhoneNumber = (phone: string) => {
  window.location.href = `tel:${phone}`
}

const introductionContent = ref([
  {
    title: '一、主要职责',
    content: [
      '(一)贯彻执行国家、省、市、县有关工商行政管理、质量技术监督、食品药品（含食品添加剂、保健食品、化妆品、中药、民族药、医疗器械等，下同）监督管理等方面的法律、法规和政策；拟订并组织实施有关市场监管的规范性文件和政策、措施。',
      '(二)拟订并组织实施市场监督管理事业发展规划和技术机构建设规划；积极推进检验检测机构、市场监管体制、基层执法力量及社会化监管体系的整合；完善技术支撑保障体系，提高市场监督管理科学化水平，构建市场监管长效机制。',
      '(三)负责涉及县级工商行政管理、质量技术监督、食品药品监督管理的各类行政审批和行政许可，并依法监管。',
      '(四)依法承担食品生产、食品流通及餐饮服务的监督管理职责；掌握分析食品安全形势，完善应对措施；建立食品安全隐患排查治理机制；制订食品安全检查年度计划、整顿治理方案并组织落实。',
      '(五)负责食品药品安全科技发展规划的组织实施，推动食品药品评价体系、检验检测体系、电子监管追溯体系和信息化建设；开展食品药品安全宣传、教育培训；推进食品药品质量安全诚信体系建设；负责食品药品安全事故应急体系建设，组织和指导食品药品安全事故应急处置和调查处理工作。',
      '(六)负责药品、医疗器械行政监督和技术监督。监督实施药品、医疗器械研制、生产、流通、使用方面的质量管理规范；负责药品、医疗器械注册的有关工作和监督管理；监督实施国家药品、医疗器械标准，组织开展药品不良反应和医疗器械不良事件监测；建立健全药品安全应急体系；配合有关部门实施国家基本药物制度；组织实施处方药和非处方药分类管理制度。',
      '(七)监督实施保健食品、化妆品标准和技术规范；组织实施保健食品、化妆品安全性检测和评价、不良反应监测和质量监管。',
      '(八)依法规范和维护市场经营秩序，负责市场交易和网络交易及有关服务行为的监管职责，承担辖区相关市场主体的登记注册和监督管理工作，依法查处取缔无照经营行为；组织指导辖区个体工商户、农民专业合作社、企业、商品交易市场信用分类管理，研究分析并依法发布市场主体登记注册基础信息；承担辖区垄断协议、滥用市场支配地位，滥用行政权力排除限制竞争等反垄断执法有关工作（价格垄断行为除外）；依法监督管理直销企业和直销人员及其直销活动，查处传销和违法直销案件，协调相关部门开展打击传销联合行动；查处辖区不正当竞争、商业贿赂、走私贩私等经济违法行为。',
      '(九)承担辖区内拍卖、经纪活动、合同管理和动产抵押物登记等工作。',
      '(十)负责商标、广告监督管理工作，依法保护商标专用权；组织实施商标战略和名牌战略，依法保护中国驰名商标、江西省著名商标、上饶市知名商标以及地理标志商标、特殊标志和官方标志等，鼓励争创中国驰名商标，江西省著名商标、上饶市知名商标；依法推荐中国驰名商标和省、市著名商标及中国、省、市名牌产品；依法保护特殊标志、认证标志；负责地理标志产品日常监督管理工作；承担辖区内广告活动监督管理工作，组织监测各类媒介广告发布情况，查处广告违法行为；指导广告业发展。',
      '（十一）管理和指导全县质量工作，组织实施国家、省、市有关质量振兴的政策措施，推进"质量兴县"工作的开展；研究制订提高本县高质量发展水平的战略和计划；根据分级管理分工，综合管理锅炉、压力容器、压力管道、电梯、起重机械、客运索道、大型游乐设施、场（厂）内专用机动车辆等特种设备的安全监察和质量监督工作，组织协调产品质量重大事故和特种设备事故的调查处理，并提出整改处理意见。协助上级主管部门做好工业产品生产许可证、产品质量认证和质量体系认证的管理工作；依法负责产品防伪的监督管理工作。',
      '（十二）负责管理标准化工作；组织实施国家标准、行业标准和地方标准；监督标准的贯彻执行；推行国际标准和国外先进标准；组织制订农业标准规范；负责企业执行标准的备案管理；组织做好有关组织机构代码、物品编码工作。',
      '（十三）统一管理计量工作；推行国家法定计量单位，组织执行国家计量法律法规；建立县级计量标准和社会公用计量标准，执行计量检定规程和计量技术规范，组织全县量值传递；对计量器具的制造、修理、销售、进口、使用、检定进行监督管理；对计量检定机构、产品质量监督检验机构和为社会提供公正数据的实验室进行计量认证和监督管理；规范和监督商品的计量行为；调解计量纠纷，组织仲裁检定。',
      '（十四）依法承担消费者权益保护牵头协调职责，建立消费者权益保护体系，组织指导消费维权工作；负责涉及工商行政管理、质量技术监督、食品药品监督管理的申诉和举报工作。',
      '（十五）依法查处违反工商行政管理、质量技术监督、食品药品监督管理的法律、法规、规章的行为。',
      '(十六) 加强基层监管力量，完善基层监管体制，构建基层食品药品的网格化监管体系；加强对基层的业务指导，领导和管理所属、直属和派出机构的工作；指导与工商行政管理、质量技术监督、食品药品监督管理业务有关的社会团体的工作。',
      '(十七) 承担区食品安全委员会日常工作；负责食品安全综合协调；推动食品安全应急体系和隐患排查治理。',
      '(十八) 承办区人民政府和上级主管部门交办的其他事项。'
    ]
  },
  {
    title: '二、办公地址',
    content: ['广丰区永丰街道开源路100号']
  },
  {
    title: '三、办公时间',
    content: ['上午8:00-12:00，下午2:30-5:30']
  },
  {
    title: '四、联系方式',
    content: ['0793-2652570']
  }
])
</script>

<template>
  <div class="view-container">
    <div class="header">
      <el-button type="text" @click="goBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <h1>单位简介</h1>
    </div>

    <div class="content">
      <!-- 宣传视频 -->
      <div class="slogan-section">
        <!-- 宣传视频 -->
        <div class="video-container">
          <video 
            controls 
            class="promo-video"
          >
            <source src="https://pic.sdtaa.com/ZhiLian/Video/Enterprise/GuangFengShiChang/GuangfengShiChang.mp4" type="video/mp4">
            您的浏览器不支持视频播放
          </video>
        </div>
      </div>

      <div class="introduction-sections">
        <div 
          v-for="(section, index) in introductionContent" 
          :key="index"
          class="section"
        >
          <h2 class="section-title">{{ section.title }}</h2>
          <div class="section-divider"></div>
          <div class="section-content">
            <!-- 非联系方式部分正常显示 -->
            <template v-if="section.title !== '四、联系方式'">
              <p v-if="!Array.isArray(section.content)">{{ section.content }}</p>
              <p v-for="(item, idx) in section.content" :key="idx" class="content-item">
                {{ item }}
              </p>
            </template>
            
            <!-- 联系方式部分显示带功能按钮的版本 -->
            <div v-else class="phone-actions">
              <span class="phone-number">{{ section.content[0] }}</span>
              <div class="action-buttons">
                <el-button 
                  type="primary" 
                  size="small" 
                  @click="copyPhoneNumber(section.content[0])"
                  class="action-button"
                >
                  <el-icon><CopyDocument /></el-icon>
                  <span>复制</span>
                </el-button>
                <el-button 
                  type="success" 
                  size="small" 
                  @click="callPhoneNumber(section.content[0])"
                  class="action-button"
                >
                  <el-icon><Phone /></el-icon>
                  <span>拨打</span>
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f0f5fa;
}

.header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, #151fa8, #1e2eb7);
  color: white;
  height: 3rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 100;
  box-sizing: border-box;
}

.back-button {
  color: white;
  margin-right: 1rem;
  font-size: 1.25rem;
  padding: 0.5rem;
  position: absolute;
  left: 0.5rem;
  z-index: 1;
}

.header h1 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  width: 100%;
  text-align: center;
}

.content {
  flex: 1;
  padding: 0;
  display: flex;
  flex-direction: column;
  padding-top: 3rem;
}

/* 顶栏Slogan */
.slogan-section {
  background: linear-gradient(135deg, rgba(21, 31, 168, 0.9), rgba(30, 46, 183, 0.9));
  padding: 1.5rem 1rem 2rem;
  color: white;
  text-align: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* 视频容器 */
.video-container {
  max-width: 800px;
  margin: 0 auto;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  background-color: rgba(0, 0, 0, 0.1);
  position: relative;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  aspect-ratio: 16 / 9;
}

.video-container:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.25);
}

.promo-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  background-color: #000;
}

.introduction-sections {
  padding: 1.5rem;
  max-width: 800px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

.section {
  background-color: #ffffff;
  border-radius: 0.5rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.section-title {
  color: #1e2eb7;
  font-size: 1.25rem;
  margin: 0 0 0.75rem 0;
}

.section-divider {
  height: 2px;
  background: linear-gradient(to right, #151fa8, #1e2eb7, transparent);
  margin-bottom: 1rem;
  width: 100px;
}

.section-content {
  color: #333;
  line-height: 1.6;
  text-align: justify;
  margin: 0;
}

.content-item {
  margin-bottom: 0.75rem;
}

.content-item:last-child {
  margin-bottom: 0;
}

/* 电话号码操作样式 */
.phone-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 0.5rem;
  background-color: rgba(30, 46, 183, 0.05);
  padding: 0.75rem;
  border-radius: 0.5rem;
}

.phone-number {
  font-weight: 500;
  color: #1e2eb7;
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

@media (min-width: 768px) {
  .content {
    padding-top: 3rem;
  }

  .slogan-section {
    padding: 2rem 1rem 2.5rem;
  }
  
  .introduction-sections {
    padding: 2rem;
  }
  
  .section-title {
    font-size: 1.5rem;
  }
}
</style> 