import { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'houriCapitalHome',
    component: () => import('./views/HomeView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/HouriLogo-1.jpg',
      title: '后日资本'
    }
  },
  {
    path: '/ai-employee',
    name: 'houriCapitalAIEmployee',
    component: () => import('./views/AIEmployeeView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/HouriLogo-1.jpg',
      title: '智能员工 - 后小资'
    }
  },
  {
    path: '/ai-driven',
    name: 'houriCapitalAIDriven',
    component: () => import('./views/AIDrivenView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/HouriLogo-1.jpg',
      title: 'AI驱动 - 协同增效'
    }
  },
  {
    path: '/digital-ecosystem',
    name: 'houriCapitalEcosystem',
    component: () => import('./views/EcosystemView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/HouriLogo-1.jpg',
      title: '数字生态 - 价值共生'
    }
  },
  {
    path: '/capital',
    name: 'houriCapitalMain',
    component: () => import('./views/CapitalView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/HouriLogo-1.jpg',
      title: '后日资本 - 产业赋能'
    }
  }
]

export default routes 