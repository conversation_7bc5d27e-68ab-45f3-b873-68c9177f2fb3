import { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'srMediaHome',
    component: () => import('./views/HomeView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ShangRaoChuanMei/logo.jpg',
      title: '上饶传媒集团'
    }
  },
  {
    path: '/group-intro',
    name: 'srMediaGroupIntro',
    component: () => import('./views/GroupIntroView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ShangRaoChuanMei/logo.jpg',
      title: '上饶传媒集团 - 集团简介'
    }
  },
  {
    path: '/chat',
    name: 'srMediaChat',
    component: () => import('./views/ChatView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ShangRaoChuanMei/logo.jpg',
      title: '上饶传媒集团 - 与我对话'
    }
  },
  {
    path: '/products',
    name: 'srMediaProducts',
    component: () => import('./views/ProductsView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ShangRaoChuanMei/logo.jpg',
      title: '上饶传媒集团 - 产品服务'
    }
  },
  {
    path: '/contact',
    name: 'srMediaContact',
    component: () => import('./views/ContactView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ShangRaoChuanMei/logo.jpg',
      title: '上饶传媒集团 - 联系我们'
    }
  },
  {
    path: '/product-center',
    name: 'srMediaProductCenter',
    component: () => import('./views/ProductCenterView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ShangRaoChuanMei/logo.jpg',
      title: '上饶传媒集团 - 产品中心'
    }
  }
]

export default routes
