<script setup lang="ts">
import { useRouter } from 'vue-router'
import { 
  ArrowLeft, 
  Connection,
  Promotion,
  Opportunity,
  Compass,
  Aim,
  Management,
  Coin
} from '@element-plus/icons-vue'
import { ref, onMounted, onBeforeUnmount } from 'vue'
import ImageViewer from '../../../components/ImageViewer.vue'

const router = useRouter()

const goBack = () => {
  router.push('/card/houri-capital')
}

// 图片查看器相关状态
const imageViewerVisible = ref(false)
const currentImage = ref('')

// 打开图片查看器
const openImageViewer = (imageUrl: string) => {
  currentImage.value = imageUrl
  imageViewerVisible.value = true
  // 当打开图片查看器时暂停自动轮播
  stopAutoPlay()
}

// 监听图片查看器的关闭事件
const handleImageViewerClose = () => {
  // 当图片查看器关闭时恢复自动轮播
  startAutoPlay()
}

// 视频播放控制
const videoRef = ref<HTMLVideoElement | null>(null)
const isPlaying = ref(false)

const toggleVideo = () => {
  if (videoRef.value) {
    if (isPlaying.value) {
      videoRef.value.pause()
    } else {
      videoRef.value.play()
    }
    isPlaying.value = !isPlaying.value
  }
}

// 轮播图控制
const activeIndex = ref(0)
const platformImages = [
  {
    url: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/HouRiZiBen/TouHang-1.png',
    title: '投行平台企业服务体系'
  },
  {
    url: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/HouRiZiBen/TouHang-2.png',
    title: '投行平台企业运行系统'
  },
  {
    url: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/HouRiZiBen/TouHang-3.png',
    title: '投行平台企业管理模式'
  },
  {
    url: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/HouRiZiBen/TouHang-4.png',
    title: '投行平台企业算法决策'
  },
  {
    url: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/HouRiZiBen/TouHang-5.png',
    title: '投行平台企业价值输出'
  }
]

// 自动轮播定时器
const autoPlayTimer = ref<number | null>(null)
const autoPlayInterval = 3000 // 3秒

// 启动自动轮播
const startAutoPlay = () => {
  stopAutoPlay() // 先清除可能存在的定时器
  autoPlayTimer.value = window.setInterval(() => {
    nextImage()
  }, autoPlayInterval)
}

// 停止自动轮播
const stopAutoPlay = () => {
  if (autoPlayTimer.value !== null) {
    clearInterval(autoPlayTimer.value)
    autoPlayTimer.value = null
  }
}

// 图片加载状态
const imageLoading = ref(true)

const handleImageLoaded = () => {
  imageLoading.value = false
}

const nextImage = () => {
  imageLoading.value = true
  activeIndex.value = (activeIndex.value + 1) % platformImages.length
}

const prevImage = () => {
  imageLoading.value = true
  activeIndex.value = (activeIndex.value - 1 + platformImages.length) % platformImages.length
}

// 手动切换到下一张图片
const handleNextImage = () => {
  stopAutoPlay()
  nextImage()
  startAutoPlay()
}

// 手动切换到上一张图片
const handlePrevImage = () => {
  stopAutoPlay()
  prevImage()
  startAutoPlay()
}

const setActiveImage = (index: number) => {
  if (index !== activeIndex.value) {
    imageLoading.value = true
    activeIndex.value = index
    // 重置自动轮播计时器
    startAutoPlay()
  }
}

// 触摸滑动相关变量和函数
const touchStartX = ref(0)
const touchEndX = ref(0)
const minSwipeDistance = 50 // 最小滑动距离，小于这个距离不触发切换

const handleTouchStart = (e: TouchEvent) => {
  touchStartX.value = e.changedTouches[0].screenX
  // 触摸时暂停自动轮播
  stopAutoPlay()
}

const handleTouchEnd = (e: TouchEvent) => {
  touchEndX.value = e.changedTouches[0].screenX
  handleSwipe()
  // 触摸结束后重新启动自动轮播
  startAutoPlay()
}

const handleSwipe = () => {
  const distance = touchEndX.value - touchStartX.value
  // 如果滑动距离足够长，才触发切换
  if (Math.abs(distance) > minSwipeDistance) {
    if (distance > 0) {
      // 向右滑动，显示上一张
      handlePrevImage()
    } else {
      // 向左滑动，显示下一张
      handleNextImage()
    }
  }
}

// 处理浏览器返回按钮
const handlePopState = (event: PopStateEvent) => {
  if (imageViewerVisible.value) {
    imageViewerVisible.value = false
    document.body.style.overflow = ''
    // 阻止默认的返回行为
    event.preventDefault()
  }
}

onMounted(() => {
  document.title = '后日资本 - 数字生态：价值共生'
  window.addEventListener('popstate', handlePopState)
  // 启动自动轮播
  startAutoPlay()
})

onBeforeUnmount(() => {
  window.removeEventListener('popstate', handlePopState)
  // 清除自动轮播定时器
  stopAutoPlay()
})
</script>

<template>
  <div class="view-container">
    <div class="header">
      <el-button type="text" @click="goBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <h1>数字生态：价值共生</h1>
    </div>

    <div class="content">
      <div class="ecosystem-container">
        <!-- 视频部分 -->
        <div class="video-section">
          <div class="video-container">
            <video 
              ref="videoRef" 
              src="https://pic.sdtaa.com/ZhiLian/Video/Enterprise/HouRiZiBen/xcsp3.mp4" 
              class="feature-video" 
              @click="toggleVideo"
              poster="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/HouriLogo-1.jpg"
              controls
            ></video>
          </div>
        </div>

        <!-- 新时代部分 -->
        <div class="section new-era-section">
          <div class="section-header">
            <el-icon class="section-icon"><Promotion /></el-icon>
            <h2>新时代：从"单体竞争"迈向"生态竞合"</h2>
          </div>
          <div class="section-content">
            <p>当前，数字技术正以前所未有的力量重塑全球产业格局。区域经济的发展模式，也正从过去"拼政策、抢项目"的"单体竞争"，跃迁至更高级、更具活力的"生态竞合"新阶段。传统招商引资模式的零和博弈已难以为继。</p>
            <p>这场由数字技术引发的革命，不仅仅是工具和方法论的升级，更是区域经济从依赖土地、劳动力的"物理集聚"，向技术、数据、资本、人才化学反应驱动的"化学融合"的深刻质变。数字技术的爆发，使得生产要素的流动不再受限于物理边界，区域竞争的核心也随之从单点优势转向"生态重构"。这指向的是一种全新的经济文明——以生态协同为基座，以数字技术为引擎，以实现区域共赢为最终目标的未来。</p>
            <p>在这个新时代，政府的角色也需要随之蜕变：不再是简单的"主导者"或"政策供给者"，而应成为"生态架构师"，通过搭建平台、制定规则、优化环境，激发企业间的"自组织"能力和创新裂变。</p>
          </div>
        </div>

        <!-- 我们的模式部分 -->
        <div class="section our-model-section">
          <div class="section-header">
            <el-icon class="section-icon"><Connection /></el-icon>
            <h2>我们的模式：数字技术驱动的生态</h2>
          </div>
          <div class="section-content">
            <p>后日资本深信，数字技术是构建未来产业生态的"神经中枢"和"新基因"。正如一棵健康的树需要根系、枝干、叶片和土壤协同共生，数字时代的产业生态也需要核心企业为"主干"，上下游企业为"枝蔓"，科研、金融、服务机构为"土壤"，更需要数字技术作为强大的纽带，连接并激活所有要素。</p>
            
            <div class="image-text-container">
              <div class="section-image">
                <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/HouRiZiBen/SiChongGuanJian.png" alt="四重关键跃迁" @click="openImageViewer('https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/HouRiZiBen/SiChongGuanJian.png')" />
              </div>
              <div class="section-text">
                <p>我们通过数字技术，帮助区域实现四重关键跃迁：</p>
                <div class="key-transitions">
                  <div class="transition-item">
                    <div class="transition-icon">
                      <el-icon><Compass /></el-icon>
                    </div>
                    <div class="transition-content">
                      <h3>空间跃迁</h3>
                      <p>从"地理集群"走向"虚拟共同体"，打破物理距离限制，实现要素跨区域协同流动（如云端研发协同）。</p>
                    </div>
                  </div>
                  <div class="transition-item">
                    <div class="transition-icon">
                      <el-icon><Coin /></el-icon>
                    </div>
                    <div class="transition-content">
                      <h3>价值跃迁</h3>
                      <p>从依赖"成本洼地"转向构建"数据高地"，区域价值不再取决于低成本，而是能提供独有的数据资产和智能工具，形成内生黏性。</p>
                    </div>
                  </div>
                  <div class="transition-item">
                    <div class="transition-icon">
                      <el-icon><Management /></el-icon>
                    </div>
                    <div class="transition-content">
                      <h3>治理跃迁</h3>
                      <p>从"行政主导"转向"算法治理"，通过技术和规则，提升效率、透明度和可信度（如基于区块链的政务流程优化）。</p>
                    </div>
                  </div>
                  <div class="transition-item">
                    <div class="transition-icon">
                      <el-icon><Opportunity /></el-icon>
                    </div>
                    <div class="transition-content">
                      <h3>模式跃迁</h3>
                      <p>从"孤岛"竞争走向"大陆"协同，区域在差异化定位中寻找互补，实现"和而不同"的价值共鸣。</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <p>我们协助政府构建数字基座、重构利益链、培育创新雨林。这包括打破"数据孤岛"，实现要素的"泛在连接"；从"零和博弈"走向"价值共生"的利益分配机制；以及通过制度设计让要素实现"自组织生长"。数字技术带来的"数据驱动决策"和"泛在连接"，让产业协作从"机械传动"升级为高效智能的"神经网络"，实现资源最优配置和价值的指数级增长。</p>
          </div>
        </div>

        <!-- 价值成果部分 -->
        <div class="section value-outcome-section">
          <div class="section-header">
            <el-icon class="section-icon"><Aim /></el-icon>
            <h2>价值成果：共生共赢的未来</h2>
          </div>
          <div class="section-content">
            <p>后日资本的实践充分验证了数字生态协同带来的巨大价值。我们在长三角经济带的产业生态协同案例清晰表明：凡是能够率先完成"生态化转身"的区域，其合作企业的存活率高出传统园区<span class="highlight">47%</span>，技术外溢效应更是扩大了<span class="highlight">3倍</span>！</p>
            <p>这背后是数字生态培育出的强大生命力——"自进化、自循环、自增值"。我们通过"耐心资本"和"制度创新"，帮助区域设立产业基金，提供长期资本支持技术孵化和生态培育。同时，通过税收分成、飞地经济等机制，平衡区域利益，避免恶性竞争，实现可持续的价值共创。</p>
            <p>数字革命时代，区域经济发展的底层逻辑正在被改写。谁能在数字土壤中培育出具有强大生命力的产业生态，谁就能掌握未来二十年的增长密钥。后日资本期待与您携手，共同探索数字生态下的企业协同共生新范式，在价值共生的新浪潮中实现区域的持续繁荣。</p>
            
            <!-- 平台企业模式轮播 -->
            <div class="platform-carousel-section">
              <h3>数字生态下的企业协同共生新范式</h3>
              <div class="carousel-container">
                <div class="carousel-wrapper">
                  <div class="carousel-controls">
                    <button class="carousel-btn prev" @click="handlePrevImage">
                      <el-icon><ArrowLeft /></el-icon>
                    </button>
                    <button class="carousel-btn next" @click="handleNextImage">
                      <el-icon><ArrowLeft style="transform: rotate(180deg)" /></el-icon>
                    </button>
                  </div>
                  <div class="carousel-slide" 
                    @touchstart="handleTouchStart" 
                    @touchend="handleTouchEnd"
                    @mouseenter="stopAutoPlay"
                    @mouseleave="startAutoPlay">
                    <div class="swipe-hint">
                      <span class="swipe-text">点击图片可放大</span>
                    </div>
                    <div v-if="imageLoading" class="image-loading">
                      <div class="loading-spinner"></div>
                    </div>
                    <transition name="slide-fade" mode="out-in">
                      <img 
                        :key="activeIndex"
                        :src="platformImages[activeIndex].url" 
                        :alt="platformImages[activeIndex].title" 
                        @click="openImageViewer(platformImages[activeIndex].url)"
                        @load="handleImageLoaded"
                      />
                    </transition>
                  </div>
                  <div class="slide-caption">{{ platformImages[activeIndex].title }}</div>
                </div>
                <div class="carousel-indicators">
                  <button 
                    v-for="(_, index) in platformImages" 
                    :key="index" 
                    :class="['indicator', { active: index === activeIndex }]"
                    @click="setActiveImage(index)"
                  ></button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 使用图片查看器组件 -->
    <ImageViewer 
      v-model:visible="imageViewerVisible" 
      :image-url="currentImage"
      @close="handleImageViewerClose"
    />
  </div>
</template>

<style scoped>
.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8faf3;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  min-width: 100vw;
  box-sizing: border-box;
}

.header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, #7e8f4e, #9fb25f);
  color: white;
  height: 3rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 100;
  box-sizing: border-box;
}

.back-button {
  color: white;
  margin-right: 1rem;
  font-size: 1.25rem;
  padding: 0.5rem;
  position: absolute;
  left: 0.5rem;
  z-index: 1;
}

.header h1 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  width: 100%;
  text-align: center;
}

.content {
  flex: 1;
  padding-top: 4.5rem;
  padding-bottom: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
  padding-left: 1rem;
  padding-right: 1rem;
}

.ecosystem-container {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

/* 视频部分 */
.video-section {
  width: 100%;
  margin-bottom: 0.5rem;
}

.video-container {
  width: 100%;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  background-color: #000;
  position: relative;
  padding-top: 56.25%; /* 16:9 宽高比 */
}

.feature-video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
}

/* 通用部分样式 */
.section {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  margin-bottom: 0.5rem;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
  border-bottom: 1px solid rgba(126, 143, 78, 0.2);
  padding-bottom: 1rem;
}

.section-icon {
  color: #7e8f4e;
  font-size: 1.5rem;
  margin-right: 0.75rem;
}

.section h2 {
  color: #7e8f4e;
  margin: 0;
  font-size: 1.3rem;
  font-weight: 600;
}

.section-content {
  color: #555;
  line-height: 1.6;
}

.section-content p {
  margin-bottom: 1rem;
}

/* 模式部分 */
.image-text-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin: 1.5rem 0;
}

.section-image {
  width: 100%;
  border-radius: 0.75rem;
  overflow: hidden;
}

.section-image img {
  width: 100%;
  height: auto;
  display: block;
}

.key-transitions {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, 1fr); /* 确保两行高度一致 */
  gap: 1rem;
  margin-top: 1rem;
}

.transition-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start; /* 内容从顶部开始 */
  background: rgba(248, 250, 243, 0.6);
  border-radius: 0.75rem;
  padding: 1.25rem 0.75rem; /* 减小左右内边距 */
  transition: all 0.3s ease;
  height: 100%;
  box-sizing: border-box; /* 确保内边距不会增加元素总宽度 */
}

.transition-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.05);
}

.transition-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: rgba(126, 143, 78, 0.15);
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.transition-icon .el-icon {
  font-size: 24px;
  color: #7e8f4e;
}

.transition-content {
  text-align: center;
  width: 100%;
}

.transition-content h3 {
  color: #7e8f4e;
  margin: 0 0 0.5rem;
  font-size: 1.05rem;
}

.transition-content p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.5;
}

/* 价值成果部分 */
.highlight {
  color: #7e8f4e;
  font-weight: 600;
}

.platform-carousel-section {
  margin-top: 2rem;
}

.platform-carousel-section h3 {
  color: #7e8f4e;
  text-align: center;
  margin-bottom: 1.5rem;
  font-size: 1.2rem;
}

.carousel-container {
  width: 100%;
  margin: 0 auto;
}

.carousel-wrapper {
  position: relative;
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  background-color: white;
}

.carousel-controls {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 1rem;
  z-index: 5;
  pointer-events: none;
}

.carousel-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.8);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #7e8f4e;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  pointer-events: auto;
}

.carousel-btn:hover {
  background: white;
  transform: scale(1.1);
}

.carousel-slide {
  width: 100%;
  position: relative;
  touch-action: pan-y; /* 允许垂直滚动，但水平滑动会被捕获 */
  overflow: hidden; /* 确保内容不会溢出 */
}

/* 滑动提示 */
.swipe-hint {
  position: absolute;
  top: 15px;
  right: 15px;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  display: flex;
  align-items: center;
  z-index: 10;
  opacity: 0.8;
  animation: fadeOut 2s forwards 3s;
  white-space: nowrap;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(2px);
}

.swipe-text {
  display: flex;
  align-items: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.swipe-text::before {
  content: "🔍";
  margin-right: 5px;
  font-size: 14px;
}

@keyframes fadeOut {
  from { opacity: 0.7; }
  to { opacity: 0; }
}

/* 加载指示器 */
.image-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(255, 255, 255, 0.7);
  z-index: 5;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(126, 143, 78, 0.3);
  border-top: 4px solid #7e8f4e;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.carousel-slide img {
  width: 100%;
  display: block;
  user-select: none; /* 防止图片被选中 */
}

/* 轮播图过渡效果 */
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.3s ease;
}

.slide-fade-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.slide-fade-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}

.slide-caption {
  background: rgba(126, 143, 78, 0.8);
  color: white;
  padding: 0.75rem;
  text-align: center;
  font-weight: 500;
  margin-top: 0.5rem;
  border-radius: 0 0 0.75rem 0.75rem;
}

.carousel-indicators {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 1rem;
}

.indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: rgba(126, 143, 78, 0.3);
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.indicator.active {
  background: #7e8f4e;
  transform: scale(1.2);
}

@media (min-width: 768px) {
  .content {
    padding-top: 5rem;
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .image-text-container {
    flex-direction: row;
    align-items: center;
  }

  .section-image {
    width: 40%;
    flex-shrink: 0;
  }

  .section-text {
    width: 60%;
  }

  .key-transitions {
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .transition-item {
    flex-direction: row;
    text-align: left;
    align-items: flex-start;
  }

  .transition-icon {
    width: 60px;
    height: 60px;
    margin-right: 1.25rem;
    margin-bottom: 0;
  }

  .transition-icon .el-icon {
    font-size: 28px;
  }

  .transition-content h3 {
    font-size: 1.1rem;
    margin: 0 0 0.75rem;
  }

  .transition-content p {
    font-size: 1rem;
  }

  .carousel-container {
    width: 80%;
  }

  .transition-content {
    text-align: left;
  }

  .transition-content p {
    font-size: 1rem;
  }
}

@media (min-width: 1024px) {
  .key-transitions {
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(2, 1fr);
    gap: 2rem;
  }
}

/* 在小屏幕设备上的特殊样式 */
@media (max-width: 480px) {
  .key-transitions {
    gap: 0.75rem;
  }
  
  .transition-item {
    padding: 1rem 0.5rem;
  }
  
  .transition-icon {
    width: 40px;
    height: 40px;
    margin-bottom: 0.5rem;
  }
  
  .transition-icon .el-icon {
    font-size: 20px;
  }
  
  .transition-content h3 {
    font-size: 0.95rem;
    margin: 0 0 0.4rem;
  }
  
  .transition-content p {
    font-size: 0.85rem;
    line-height: 1.4;
  }
}
</style> 