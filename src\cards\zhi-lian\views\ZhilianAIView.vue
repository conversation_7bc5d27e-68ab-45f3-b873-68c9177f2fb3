<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ArrowLeft, User, VideoCamera, Briefcase, ChatDotRound, Calendar, Document, Share, Promotion } from '@element-plus/icons-vue'

const router = useRouter()

const goBack = () => {
  router.push('/card/zhi-lian')
}

const goToAIAssistant = () => {
  router.push('/card/zhi-lian/ai-assistant')
}

const goToProducts = () => {
  router.push('/card/zhi-lian/products')
}

const activeCard = ref(0)
const timer = ref<number | null>(null)
const touchStart = ref(0)
const touchEnd = ref(0)

const startAutoScroll = () => {
  stopAutoScroll()
  timer.value = setInterval(() => {
    activeCard.value = (activeCard.value + 1) % 4
  }, 3000)
}

const stopAutoScroll = () => {
  if (timer.value) {
    clearInterval(timer.value)
    timer.value = null
  }
}

const setActiveCard = (index: number) => {
  activeCard.value = index
  stopAutoScroll()
  startAutoScroll()
}

const handleTouchStart = (event: TouchEvent) => {
  touchStart.value = event.touches[0].clientX
  stopAutoScroll()
}

const handleTouchEnd = (event: TouchEvent) => {
  touchEnd.value = event.changedTouches[0].clientX
  const diff = touchEnd.value - touchStart.value
  
  if (Math.abs(diff) > 50) { // 最小滑动距离
    if (diff > 0) { // 右滑
      activeCard.value = (activeCard.value - 1 + 4) % 4
    } else { // 左滑
      activeCard.value = (activeCard.value + 1) % 4
    }
  }
  startAutoScroll()
}

onMounted(() => {
  document.title = '智链AI - 企业的智能宣传中枢'
  startAutoScroll()
})

onUnmounted(() => {
  stopAutoScroll()
})
</script>

<template>
  <div class="view-container">
    <div class="header">
      <el-button type="text" @click="goBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <h1>智链AI</h1>
    </div>

    <div class="content">
      <!-- 顶栏Slogan -->
      <div class="slogan-section">
        <div class="slogan-container">
          <h2 class="slogan">智链AI：企业的智能宣传中枢，智能链接每一位客户。</h2>
          <div class="slogan-underline"></div>
        </div>
        
        <!-- 宣传视频 -->
        <div class="video-container">
          <video 
            controls 
            class="promo-video"
          >
            <source src="https://pic.sdtaa.com/ZhiLian/Video/Enterprise/ZhiLian/ZhiLian.mp4" type="video/mp4">
            您的浏览器不支持视频播放
          </video>
        </div>
      </div>
      
      <!-- 痛点卡片 -->
      <div class="section">
        <h2 class="section-title">您的企业宣传，还在为这些烦恼吗？</h2>
        
        <div class="pain-points-container">
          <div class="card-indicators">
            <span 
              v-for="(_, index) in 4" 
              :key="index"
              :class="['indicator', { active: activeCard === index }]"
              @click="setActiveCard(index)"
            ></span>
          </div>
          
          <div 
            class="cards-wrapper" 
            :style="{ '--active-card': activeCard }" 
            @touchstart="handleTouchStart" 
            @touchend="handleTouchEnd"
          >
            <!-- 卡片1 -->
            <div class="pain-point-card">
              <div class="card-icon">
                <el-icon><Document /></el-icon>
              </div>
              <h3>信息散乱</h3>
              <p class="pain">宣传资料散落各处(PDF,公众号文)，客户了解信息费时费力，品牌形象不统一？</p>
              <p class="solution">智链解法：打造您的专属「企业信息智能门户」，信息聚合，专业呈现。</p>
            </div>
            
            <!-- 卡片2 -->
            <div class="pain-point-card">
              <div class="card-icon">
                <el-icon><VideoCamera /></el-icon>
              </div>
              <h3>内容乏力</h3>
              <p class="pain">想用视频/数字人做宣传，但成本高、周期长，内容创意难？</p>
              <p class="solution">智链解法：启用「AI数智内容官」，低成本高效生成专业级图文、视频内容。</p>
            </div>
            
            <!-- 卡片3 -->
            <div class="pain-point-card">
              <div class="card-icon">
                <el-icon><ChatDotRound /></el-icon>
              </div>
              <h3>响应不及时</h3>
              <p class="pain">客户咨询五花八门，人工客服无法7x24小时在线，错失商机？</p>
              <p class="solution">智链解法：部署「AI智能宣传员」，全天候智能应答，主动营销。</p>
            </div>
            
            <!-- 卡片4 -->
            <div class="pain-point-card">
              <div class="card-icon">
                <el-icon><Promotion /></el-icon>
              </div>
              <h3>营销能力不足</h3>
              <p class="pain">团队缺乏统一营销工具和最新知识，推广效果难保证？</p>
              <p class="solution">智链解法：装备「员工营销赋能系统」，提升团队整体作战力。</p>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 四大引擎 -->
      <div class="section engines-section">
        <h2 class="section-title">智链AI系统：四大引擎，重塑您的企业宣传</h2>
        
        <div class="engines-grid">
          <!-- AI智能宣传员 -->
          <div class="engine-card">
            <div class="engine-icon">
              <el-icon><ChatDotRound /></el-icon>
            </div>
            <h3>AI智能宣传员</h3>
            <ul>
              <li><strong>智能应答：</strong>7x24小时在线，精准理解客户意图。</li>
              <li><strong>主动引导：</strong>千人千面互动，巧妙推荐转化。</li>
              <li><strong>商机捕获：</strong>不错失任何潜在咨询与需求。</li>
            </ul>
          </div>
          
          <!-- 员工营销赋能 -->
          <div class="engine-card">
            <div class="engine-icon">
              <el-icon><User /></el-icon>
            </div>
            <h3>员工营销赋能</h3>
            <ul>
              <li><strong>知识同步：</strong>最新产品/营销资料，及时更新。</li>
              <li><strong>工具提效：</strong>标准化工具，赋能日常推广。</li>
              <li><strong>战力提升：</strong>专业化支持，提升团队营销力。</li>
            </ul>
          </div>
          
          <!-- AI数智代言人 -->
          <div class="engine-card">
            <div class="engine-icon">
              <el-icon><VideoCamera /></el-icon>
            </div>
            <h3>AI数智代言人</h3>
            <ul>
              <li><strong>形象定制：</strong>打造专属企业AI数字人形象。</li>
              <li><strong>内容智造：</strong>快速生成专业讲解视频、图文。</li>
              <li><strong>高效传播：</strong>科技感强，提升品牌专业度。</li>
            </ul>
          </div>
          
          <!-- 企业信息智能门户 -->
          <div class="engine-card">
            <div class="engine-icon">
              <el-icon><Briefcase /></el-icon>
            </div>
            <h3>企业信息智能门户</h3>
            <ul>
              <li><strong>信息聚合：</strong>公司、产品、案例，一站式展示。</li>
              <li><strong>专业形象：</strong>统一规范，提升客户信任度。</li>
              <li><strong>便捷分享：</strong>微信小程序，轻松触达。</li>
            </ul>
          </div>
        </div>
      </div>
      
      <!-- 企业收益 -->
      <div class="section benefits-section">
        <h2 class="section-title">选择智链AI，为您的企业带来…</h2>
        
        <div class="benefits-list">
          <div class="benefit-item">
            <span class="benefit-icon">✨</span>
            <div class="benefit-content">
              <h3>形象升级</h3>
              <p>塑造现代、专业、具备AI能力的品牌形象。</p>
            </div>
          </div>
          
          <div class="benefit-item">
            <span class="benefit-icon">⏱️</span>
            <div class="benefit-content">
              <h3>效率倍增</h3>
              <p>自动化宣传流程，降低人力与时间成本。</p>
            </div>
          </div>
          
          <div class="benefit-item">
            <span class="benefit-icon">📈</span>
            <div class="benefit-content">
              <h3>商机激活</h3>
              <p>更广触达，更深互动，有效转化潜在客户。</p>
            </div>
          </div>
          
          <div class="benefit-item">
            <span class="benefit-icon">💪</span>
            <div class="benefit-content">
              <h3>团队赋能</h3>
              <p>提升内部营销协同与专业能力。</p>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 社交宣传 -->
      <div class="section social-section">
        <h2 class="section-title">智链AI，让社交宣传更智能</h2>
        
        <div class="social-grid">
          <div class="social-item">
            <el-icon><ChatDotRound /></el-icon>
            <p>微信社交：新好友/群介绍，AI名片专业开场。</p>
          </div>
          
          <div class="social-item">
            <el-icon><Calendar /></el-icon>
            <p>线上活动：官方信息出口，智能咨询即时响应。</p>
          </div>
          
          <div class="social-item">
            <el-icon><Document /></el-icon>
            <p>内容营销：公号/视频号引流，互动平台聚合转化。</p>
          </div>
          
          <div class="social-item">
            <el-icon><Share /></el-icon>
            <p>资料速递：客户/伙伴/投资人，AI资料包高效传递。</p>
          </div>
          
          <div class="social-item">
            <el-icon><Promotion /></el-icon>
            <p>员工推广：标准化智能工具，赋能日常营销。</p>
          </div>
        </div>
      </div>
      
      <!-- CTA区域 -->
      <div class="cta-section">
        <el-button type="primary" class="cta-button" @click="goToAIAssistant">
          想了解智链？问AI小链！
        </el-button>
        
        <el-button type="primary" plain class="cta-button" @click="goToProducts">
          探索智链产品与服务
        </el-button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: var(--background);
}

.header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  color: white;
  height: 3rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 100;
  box-sizing: border-box;
}

.back-button {
  color: white;
  margin-right: 1rem;
  font-size: 1.25rem;
  padding: 0.5rem;
  position: absolute;
  left: 0.5rem;
  z-index: 1;
}

.header h1 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  width: 100%;
  text-align: center;
}

.content {
  flex: 1;
  padding: 0;
  display: flex;
  flex-direction: column;
  padding-top: 3rem;
}

/* 顶栏Slogan */
.slogan-section {
  background: linear-gradient(135deg, rgba(48, 96, 176, 0.9), rgba(77, 145, 255, 0.9));
  padding: 1.5rem 1rem 2rem;
  color: white;
  text-align: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.slogan-container {
  max-width: 800px;
  margin: 0 auto 1.5rem;
  position: relative;
}

.slogan {
  margin: 0;
  font-size: 1.3rem;
  font-weight: 600;
  line-height: 1.5;
  letter-spacing: 0.5px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  position: relative;
  display: inline-block;
  padding: 0 1rem;
}

.slogan-underline {
  height: 3px;
  width: 80px;
  background: white;
  margin: 0.7rem auto 0;
  border-radius: 3px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

/* 视频容器 */
.video-container {
  max-width: 800px;
  margin: 0 auto;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  background-color: rgba(0, 0, 0, 0.1);
  position: relative;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  aspect-ratio: 16 / 9;
}

.video-container:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.25);
}

.promo-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  background-color: #000;
}

/* 通用部分样式 */
.section {
  padding: 2rem 1rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.section-title {
  text-align: center;
  margin-bottom: 1.5rem;
  font-size: 1.3rem;
  color: var(--primary);
  font-weight: 600;
}

/* 痛点卡片 */
.pain-points-container {
  position: relative;
  overflow: hidden;
  margin: 0 -1rem;
  touch-action: pan-y pinch-zoom;
}

.card-indicators {
  display: flex;
  justify-content: center;
  margin-bottom: 1rem;
}

.indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #ddd;
  margin: 0 5px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.indicator.active {
  background-color: var(--primary);
}

.cards-wrapper {
  display: flex;
  transition: transform 0.5s ease;
  width: 400%;
  transform: translateX(calc(-25% * var(--active-card)));
  touch-action: pan-y pinch-zoom;
}

.pain-point-card {
  width: 100%;
  padding: 1.5rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  margin: 0 1rem;
  flex: 0 0 calc(25% - 2rem);
  touch-action: pan-y pinch-zoom;
  overflow-y: auto;
}

.card-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-light), var(--primary));
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
}

.card-icon .el-icon {
  font-size: 24px;
  color: white;
}

.pain-point-card h3 {
  margin: 0 0 0.75rem;
  color: var(--primary);
  font-size: 1.1rem;
}

.pain {
  color: var(--text);
  margin-bottom: 0.75rem;
  font-size: 0.95rem;
}

.solution {
  color: var(--primary);
  font-weight: 500;
  font-size: 0.95rem;
}

/* 四大引擎 */
.engines-section {
  background-color: #f9fafc;
}

.engines-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

.engine-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s, box-shadow 0.3s;
}

.engine-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.engine-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  background: linear-gradient(135deg, var(--primary-light), var(--primary));
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
}

.engine-icon .el-icon {
  font-size: 24px;
  color: white;
}

.engine-card h3 {
  margin: 0 0 1rem;
  color: var(--primary);
  font-size: 1.1rem;
}

.engine-card ul {
  padding-left: 1.2rem;
  margin: 0;
}

.engine-card li {
  margin-bottom: 0.5rem;
  font-size: 0.95rem;
  color: var(--text);
}

/* 企业收益 */
.benefits-section {
  background: linear-gradient(135deg, #f5f7fa, #eef2f7);
}

.benefits-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.benefit-item {
  display: flex;
  align-items: flex-start;
  background: white;
  border-radius: 12px;
  padding: 1rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.benefit-icon {
  font-size: 1.5rem;
  margin-right: 1rem;
  flex-shrink: 0;
}

.benefit-content {
  flex: 1;
}

.benefit-content h3 {
  margin: 0 0 0.5rem;
  color: var(--primary);
  font-size: 1.1rem;
}

.benefit-content p {
  margin: 0;
  font-size: 0.95rem;
  color: var(--text);
}

/* 社交宣传 */
.social-section {
  background-color: white;
}

.social-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.social-item {
  display: flex;
  align-items: center;
  padding: 1rem;
  background: linear-gradient(135deg, #f5f7fa, #eef2f7);
  border-radius: 12px;
  transition: transform 0.3s;
}

.social-item:hover {
  transform: translateX(5px);
}

.social-item .el-icon {
  font-size: 1.5rem;
  color: var(--primary);
  margin-right: 1rem;
  flex-shrink: 0;
}

.social-item p {
  margin: 0;
  font-size: 0.95rem;
  color: var(--text);
}

/* CTA区域 */
.cta-section {
  padding: 2rem 1rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  background: linear-gradient(135deg, rgba(48, 96, 176, 0.05), rgba(77, 145, 255, 0.05));
  max-width: 800px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

.cta-button {
  width: 100%;
  height: 2.5rem;
  font-size: 1.1rem;
  border-radius: 12px;
  margin: 0;
  font-weight: 500;
  letter-spacing: 0.5px;
  transition: transform 0.3s, box-shadow 0.3s;
}

/* 响应式调整 */
@media (min-width: 768px) {
  .engines-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .social-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .cta-section {
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 3rem 2rem;
    gap: 2rem;
  }
  
  .cta-button {
    min-width: 240px;
    max-width: 320px;
    height: 4rem;
  }
  
  .cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
  
  .slogan {
    font-size: 1.6rem;
  }
  
  .slogan-underline {
    width: 120px;
    height: 4px;
  }
}
</style>