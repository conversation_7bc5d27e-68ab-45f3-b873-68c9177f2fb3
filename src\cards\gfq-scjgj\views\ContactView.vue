<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { 
  ArrowLeft,
  Location, 
  Phone, 
  Clock,
  CopyDocument,
  Promotion
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const router = useRouter()

const goBack = () => {
  router.push('/card/gfq-scjgj')
}

const contactInfo = ref({
  address: '广丰区永丰街道开源路100号',
  phones: [
    { label: '知识产权股', numbers: ['0793-2635511', '0793-2652570'] }
  ],
  workingHours: '上午8:00-12:00，下午2:30-5:30'
})

// 复制电话号码
const copyPhoneNumber = (phone: string) => {
  // 创建临时文本区域元素
  const textArea = document.createElement('textarea')
  textArea.value = phone
  // 设置样式使元素不可见
  textArea.style.position = 'fixed'
  textArea.style.left = '-999999px'
  textArea.style.top = '-999999px'
  document.body.appendChild(textArea)
  
  // 选择文本并复制
  textArea.focus()
  textArea.select()
  
  let successful = false
  try {
    successful = document.execCommand('copy')
  } catch (err) {
    console.error('复制失败:', err)
  }
  
  // 移除临时元素
  document.body.removeChild(textArea)
  
  // 显示提示信息
  if (successful) {
    ElMessage({
      message: '电话号码已复制到剪贴板',
      type: 'success',
      duration: 2000
    })
  } else {
    ElMessage({
      message: '复制失败，请手动复制',
      type: 'error',
      duration: 2000
    })
  }
}

// 拨打电话
const callPhoneNumber = (phone: string) => {
  window.location.href = `tel:${phone}`
}
</script>

<template>
  <div class="view-container">
    <div class="header">
      <el-button type="text" @click="goBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <h1>联系我们</h1>
    </div>

    <div class="content">
      <div class="contact-card">
        <div class="contact-header">
          <h2 class="contact-department">上饶市广丰区市场监督管理局</h2>
          <div class="contact-badge">知识产权保护与促进</div>
        </div>
        
        <div class="contact-divider"></div>
        
        <div class="contact-info-list">
          <div class="contact-info-item">
            <div class="info-icon-container">
              <el-icon class="info-icon"><Location /></el-icon>
            </div>
            <div class="info-content">
              <div class="info-label">地址</div>
              <div class="info-value">{{ contactInfo.address }}</div>
            </div>
          </div>
          
          <div v-for="(phone, index) in contactInfo.phones" :key="index" class="contact-info-item">
            <div class="info-icon-container">
              <el-icon class="info-icon"><Phone /></el-icon>
            </div>
            <div class="info-content">
              <div class="info-label">{{ phone.label }}</div>
              <div class="phone-numbers">
                <div v-for="(number, numIndex) in phone.numbers" :key="numIndex" class="info-value phone-actions">
                  <span class="phone-number">{{ number }}</span>
                  <div class="action-buttons">
                    <el-button 
                      type="primary" 
                      size="small" 
                      @click="copyPhoneNumber(number)"
                      class="action-button"
                    >
                      <el-icon><CopyDocument /></el-icon>
                      <span>复制</span>
                    </el-button>
                    <el-button 
                      type="success" 
                      size="small" 
                      @click="callPhoneNumber(number)"
                      class="action-button"
                    >
                      <el-icon><Phone /></el-icon>
                      <span>拨打</span>
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="contact-info-item">
            <div class="info-icon-container">
              <el-icon class="info-icon"><Clock /></el-icon>
            </div>
            <div class="info-content">
              <div class="info-label">工作时间</div>
              <div class="info-value">{{ contactInfo.workingHours }}</div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="qrcode-container">
        <h3 class="qrcode-title">关注我们</h3>
        <div class="qrcode-frame">
          <img 
            src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/GuangFengShiChang/contact.jpg" 
            alt="广丰区市场监督管理局公众号二维码" 
            class="qrcode-image"
          >
        </div>
        <div class="qrcode-notice">
          <el-icon><Promotion /></el-icon>
          <span>长按图片识别二维码，关注公众号</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f0f5fa;
}

.header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, #151fa8, #1e2eb7);
  color: white;
  height: 3rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 100;
  box-sizing: border-box;
}

.back-button {
  color: white;
  margin-right: 1rem;
  font-size: 1.25rem;
  padding: 0.5rem;
  position: absolute;
  left: 0.5rem;
  z-index: 1;
}

.header h1 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  width: 100%;
  text-align: center;
}

.content {
  flex: 1;
  padding-top: 4.5rem;
  display: flex;
  flex-direction: column;
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  padding-bottom: 1.5rem;
  max-width: 800px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

.contact-card {
  background-color: #ffffff;
  border-radius: 0.75rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.contact-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 1rem;
}

.contact-department {
  color: #1e2eb7;
  font-size: 1.25rem;
  margin: 0 0 0.75rem 0;
  text-align: center;
}

.contact-badge {
  background: linear-gradient(135deg, #151fa8, #1e2eb7);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.85rem;
}

.contact-divider {
  height: 1px;
  background: linear-gradient(to right, transparent, rgba(30, 46, 183, 0.3), transparent);
  margin: 1rem 0;
}

.contact-info-list {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.contact-info-item {
  display: flex;
  align-items: flex-start;
}

.info-icon-container {
  flex-shrink: 0;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  background: linear-gradient(135deg, #151fa8, #1e2eb7);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
}

.info-icon {
  color: white;
  font-size: 1rem;
}

.info-content {
  flex-grow: 1;
}

.info-label {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 0.25rem;
}

.info-value {
  color: #333;
  font-weight: 500;
}

.phone-numbers {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

/* 电话号码操作样式 */
.phone-actions {
  display: flex;
  flex-direction: column;
  background-color: rgba(30, 46, 183, 0.05);
  padding: 0.75rem;
  border-radius: 0.5rem;
  margin-top: 0.25rem;
}

.phone-number {
  font-weight: 500;
  color: #1e2eb7;
  margin-bottom: 0.5rem;
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

/* 二维码容器样式 */
.qrcode-container {
  margin-bottom: 2rem;
  background-color: #ffffff;
  border-radius: 0.75rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.qrcode-title {
  color: #1e2eb7;
  font-size: 1.1rem;
  margin-bottom: 1.5rem;
  text-align: center;
}

.qrcode-frame {
  width: 200px;
  height: 200px;
  margin: 0 auto;
  padding: 0.5rem;
  background-color: #fff;
  border: 1px solid rgba(30, 46, 183, 0.2);
  border-radius: 0.5rem;
  overflow: hidden;
}

.qrcode-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.qrcode-notice {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 1rem;
  color: #666;
  font-size: 0.9rem;
}

@media (min-width: 768px) {
  .content {
    padding-top: 5rem;
    padding-left: 2rem;
    padding-right: 2rem;
    padding-bottom: 2rem;
  }
  
  .contact-department {
    font-size: 1.5rem;
  }
  
  .qrcode-title {
    font-size: 1.25rem;
  }
  
  .phone-actions {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }
  
  .phone-number {
    margin-bottom: 0;
  }
  
  .qrcode-frame {
    width: 240px;
    height: 240px;
  }
}
</style> 