<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import NavigationButtons from '../components/NavigationButtons.vue'
import TabBar from '../components/TabBar.vue'

const slogan = ref('您好，我是上饶传媒集团的AI主播饶小媒')
const pcBackgroundVideo = 'https://pic.sdtaa.com/ZhiLian/Video/Enterprise/ShangRaoChuanMei/ShiPingHengPing.mp4'
const mobileBackgroundVideo = 'https://pic.sdtaa.com/ZhiLian/Video/Enterprise/ShangRaoChuanMei/ShiPingShuPing.mp4'
// const fallbackBackgroundImage = 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ShangRaoChuanMei/srcm-szr-sy2.jpg'
const fallbackBackgroundImage = 'https://pic.sdtaa.com/ZhiLian/Video/Enterprise/ShangRaoChuanMei/ShiPingShuPing.gif'

// 检测微信浏览器环境
const isWeChatBrowser = ref(false)

// 检测微信浏览器的函数
const detectWeChatBrowser = () => {
  const ua = navigator.userAgent.toLowerCase()
  // 检测微信浏览器
  const isWeChat = ua.includes('micromessenger')
  // 检测微信小程序
  const isMiniProgram = ua.includes('miniprogram') || (window as any).__wxjs_environment === 'miniprogram'

  return isWeChat || isMiniProgram
}

// 是否应该显示背景图片而不是视频
const shouldShowBackgroundImage = computed(() => {
  return isWeChatBrowser.value
})

// 强制播放视频，解决微信浏览器自动播放问题
const playVideos = () => {
  const mobileVideo = document.querySelector('.mobile-background-video') as HTMLVideoElement
  const pcVideo = document.querySelector('.pc-background-video') as HTMLVideoElement

  if (mobileVideo) {
    mobileVideo.play().catch(err => {
      console.log('Mobile video autoplay failed:', err)
    })
  }

  if (pcVideo) {
    pcVideo.play().catch(err => {
      console.log('PC video autoplay failed:', err)
    })
  }
}

onMounted(() => {
  // 检测微信浏览器环境
  isWeChatBrowser.value = detectWeChatBrowser()

  // 如果不是微信浏览器，则尝试播放视频
  if (!isWeChatBrowser.value) {
    // 页面加载后立即尝试播放
    playVideos()

    // 监听用户交互事件，确保视频能够播放
    const handleUserInteraction = () => {
      playVideos()
      // 移除事件监听器，避免重复触发
      document.removeEventListener('touchstart', handleUserInteraction)
      document.removeEventListener('click', handleUserInteraction)
    }

    document.addEventListener('touchstart', handleUserInteraction, { once: true })
    document.addEventListener('click', handleUserInteraction, { once: true })
  }
})
</script>

<template>
  <div class="home-container">
    <div class="digital-human-container">
      <!-- 微信浏览器环境下显示背景图片 -->
      <div
        v-if="shouldShowBackgroundImage"
        class="wechat-background-image"
        :style="{ backgroundImage: `url(${fallbackBackgroundImage})` }"
      ></div>

      <!-- 非微信浏览器环境下显示背景视频 -->
      <template v-else>
        <!-- 手机端背景视频 -->
        <video
          class="mobile-background-video"
          :src="mobileBackgroundVideo"
          autoplay
          muted
          loop
          playsinline
          webkit-playsinline
          x5-playsinline
          x5-video-player-type="h5"
          x5-video-player-fullscreen="false"
          preload="auto"
        ></video>

        <!-- 桌面端背景视频 -->
        <video
          class="pc-background-video"
          :src="pcBackgroundVideo"
          autoplay
          muted
          loop
          playsinline
          webkit-playsinline
          x5-playsinline
          x5-video-player-type="h5"
          x5-video-player-fullscreen="false"
          preload="auto"
        ></video>
      </template>
    </div>
    
    <div class="bottom-section">
      <div class="content-wrapper">
        <div class="slogan-container">
          <div class="slogan-wrapper">
            <h1 class="slogan">{{ slogan }}</h1>
            <div class="tech-line"></div>
          </div>
        </div>
        
        <div class="navigation-container">
          <NavigationButtons />
        </div>
      </div>
    </div>
    
    <TabBar />
  </div>
</template>

<style scoped>
.home-container {
  position: relative;
  height: 100vh;
  max-height: 100vh;
  overflow: hidden;
}

.digital-human-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

/* 微信浏览器背景图片 */
.wechat-background-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  z-index: -1;
}

/* 手机端背景视频 */
.mobile-background-video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  z-index: -1;
  /* 微信浏览器兼容性 */
  -webkit-playsinline: true;
  -webkit-appearance: none;
  appearance: none;
  outline: none;
}

/* 桌面端背景视频 */
.pc-background-video {
  display: none;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  z-index: -1;
  /* 微信浏览器兼容性 */
  -webkit-playsinline: true;
  -webkit-appearance: none;
  appearance: none;
  outline: none;
}

.bottom-section {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, transparent, rgba(255, 255, 255, 0.85) 15%, rgba(255, 255, 255, 0.95));
  z-index: 2;
  backdrop-filter: blur(5px);
  height: 45%; /* 增加手机端高度 */
}

.content-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  padding-top: 1rem;
}

.slogan-container {
  padding: 1rem 1rem 0.5rem 1rem;
  display: flex;
  justify-content: center;
}

.slogan-wrapper {
  position: relative;
  max-width: 340px;
  padding-bottom: 0.5rem;
}

.tech-line {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(to right, transparent, #3b82f6cc 30%, #3b82f6cc 70%, transparent);
}

.slogan {
  font-size: 1.18rem;
  line-height: 1.5;
  background: linear-gradient(135deg, #3b82f6cc, #60a5facc);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
  text-align: center;
  font-weight: 500;
}

.navigation-container {
  padding: 1rem 1rem 1.5rem 1rem;
}

@media (min-width: 768px) {
  .bottom-section {
    height: 50%; /* 减小桌面端高度 */
  }
  
  .content-wrapper {
    justify-content: center;
    padding-bottom: 2rem; /* 底部留出空间 */
    padding-top: 1rem; /* 减少顶部空间，使内容往下移 */
  }
  
  .digital-human-container {
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
  }

  /* 桌面端显示桌面端视频，隐藏手机端视频 */
  .mobile-background-video {
    display: none;
  }

  .pc-background-video {
    display: block;
  }
  
  .slogan-wrapper {
    max-width: 700px; /* 增加PC端slogan的最大宽度，使其能容纳更多文字 */
    padding-bottom: 0.75rem;
  }

  .slogan {
    font-size: 1.75rem;
    background: linear-gradient(135deg, #3b82f6cc, #60a5facc);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .slogan-container {
    padding: 0 1.5rem 0.5rem 1.5rem;
    margin-bottom: 1rem; /* 减少与导航按钮的间距 */
  }
  
  .navigation-container {
    padding: 0 1.5rem 0 1.5rem;
  }
}

@media (min-width: 1200px) {
  .bottom-section {
    height: 55%; /* 减小大屏幕设备高度 */
  }
  
  .content-wrapper {
    padding-bottom: 3rem; /* 大屏幕底部留出更多空间 */
    padding-top: 2rem; /* 减少大屏幕顶部空间 */
  }
  
  .slogan-container {
    margin-bottom: 1.5rem; /* 减少大屏幕的间距 */
  }
  
  .slogan {
    font-size: 2rem; /* 大屏幕增加字体大小 */
  }
  
  .navigation-container .navigation-grid {
    max-width: 1200px; /* 大屏幕增加导航按钮的最大宽度 */
  }
  
  .slogan-wrapper {
    max-width: 800px; /* 在大屏幕上进一步增加宽度 */
  }
}
</style>
