<script setup lang="ts">
import { useRouter } from 'vue-router'
import { 
  ArrowLeft, 
  Connection, 
  Aim, 
  TrendCharts, 
  Service,
  User,
  School,
  Opportunity,
  DataAnalysis,
  Share,
  SuitcaseLine,
  ChatLineRound
} from '@element-plus/icons-vue'
import { onMounted } from 'vue'

const router = useRouter()

const goBack = () => {
  router.push('/card/wisestar-tech')
}

const goToAIPromoter = () => {
  router.push('/card/wisestar-tech/ai-promoter')
}

onMounted(() => {
  document.title = '杭州智衍星辰科技 - 公司介绍'
  const link = document.querySelector("link[rel~='icon']") as HTMLLinkElement
  if (link) {
    link.href = 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiYanXingChen/Logo.jpg'
  } else {
    const newLink = document.createElement('link')
    newLink.rel = 'icon'
    newLink.href = 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiYanXingChen/Logo.jpg'
    document.head.appendChild(newLink)
  }
})
</script>

<template>
  <div class="view-container">
    <div class="header">
      <el-button type="text" @click="goBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <h1>公司介绍</h1>
    </div>

    <div class="content">
      <!-- 欢迎页 -->
      <div class="welcome-section">
        <div class="welcome-content">
          <h2 class="company-name">杭州智衍星辰科技</h2>
          <p class="company-slogan">AI应用的实践者与赋能者</p>
        </div>
      </div>

      <!-- 关于我们 -->
      <div class="section about-section">
        <h2 class="section-title">关于我们</h2>
        <div class="about-content">
          <p>我们是一家专注于人工智能（AI）技术应用与落地的创新型科技企业。</p>
          <p>我们的使命，是将前沿AI技术，转化为驱动产业升级与人才发展的核心动能。</p>
          <div class="mission-box">
            <p>我们致力于：</p>
            <ul>
              <li>将AI从概念，变为现实</li>
              <li>将技术，变为价值</li>
            </ul>
          </div>
        </div>
      </div>

      <!-- 核心业务 -->
      <div class="section business-section">
        <h2 class="section-title">核心业务</h2>
        
        <!-- AI + 数字营销 -->
        <div class="business-card">
          <div class="business-header">
            <div class="business-icon">
              <el-icon><Connection /></el-icon>
            </div>
            <h3>AI + 数字营销</h3>
          </div>
          <div class="business-subtitle">重塑组织的沟通与链接</div>
          <div class="business-content">
            <h4>核心价值：</h4>
            <p>我们帮助企业和政府，解决对外沟通与营销效率低下的核心痛点，打造一个7x24小时在线的"AI宣传中枢"。</p>
            
            <h4>服务对象与路径：</h4>
            <div class="service-path">
              <div class="service-item">
                <div class="service-title">
                  <el-icon><SuitcaseLine /></el-icon>
                  <span>赋能企业 (To-B)：</span>
                </div>
                <p>通过 企业AI名片 与 AI营销专家 服务，重塑品牌第一印象，赋能每位员工，实现高效客户链接。</p>
              </div>
              <div class="service-item">
                <div class="service-title">
                  <el-icon><Service /></el-icon>
                  <span>服务政府 (To-G)：</span>
                </div>
                <p>通过 AI数智推介官 与 AI数智代言人 服务，创新政策宣传、招商引资和城市形象的展示范式。</p>
              </div>
            </div>
          </div>
        </div>
        
        <!-- AI + 职业教育 -->
        <div class="business-card">
          <div class="business-header">
            <div class="business-icon">
              <el-icon><User /></el-icon>
            </div>
            <h3>AI + 职业教育</h3>
          </div>
          <div class="business-subtitle">弥合AI时代的"知行鸿沟"</div>
          <div class="business-content">
            <h4>核心价值：</h4>
            <p>我们聚焦解决AI领域"学用脱节"的难题，成为连接AI技术与产业应用的桥梁，加速人才与企业的智能化转型。</p>
            
            <h4>核心服务模式：</h4>
            <div class="service-path">
              <div class="service-item">
                <div class="service-title">
                  <el-icon><Aim /></el-icon>
                  <span>产业人才定向培养：</span>
                </div>
                <p>深度链接企业需求，提供从培养到就业的闭环服务，精准解决"招人难"与"就业难"。</p>
              </div>
              <div class="service-item">
                <div class="service-title">
                  <el-icon><TrendCharts /></el-icon>
                  <span>"知行AI"线上平台：</span>
                </div>
                <p>以"让AI真正用起来"为核心，通过"学、练、用"一体化设计，帮助职场人将AI快速转化为生产力。</p>
              </div>
            </div>
          </div>
        </div>
        
        <!-- AI + 中小学教育 -->
        <div class="business-card">
          <div class="business-header">
            <div class="business-icon">
              <el-icon><School /></el-icon>
            </div>
            <h3>AI + 中小学教育</h3>
          </div>
          <div class="business-content">
            <h4>核心价值：</h4>
            <p>我们是中小学AI素养教育的推动者和资源整合者，致力于让优质AI教育资源普惠更多孩子，为下一代的成长播下人工智能的种子。</p>
            
            <h4>我们的方式：</h4>
            <div class="service-path">
              <div class="service-item">
                <div class="service-title">
                  <el-icon><Opportunity /></el-icon>
                  <span>AI科普与实践：</span>
                </div>
                <p>通过"人工智能进校园"、主题兴趣班、研学活动等形式，激发孩子对未来科技的兴趣。</p>
              </div>
              <div class="service-item">
                <div class="service-title">
                  <el-icon><Share /></el-icon>
                  <span>区域教育生态平台：</span>
                </div>
                <p>打造面向区域的"教育地图"，连接家长、学生与机构，以AI启蒙内容为切入点，服务本地教育生态。</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 选择我们 -->
      <div class="section choose-section">
        <h2 class="section-title">选择我们</h2>
        <div class="choose-subtitle">我们是AI时代的"连接器"与"赋能者"</div>
        
        <div class="advantages-grid">
          <div class="advantage-card">
            <div class="advantage-icon">
              <el-icon><Aim /></el-icon>
            </div>
            <h4>场景化落地</h4>
            <p>我们深入业务一线，只提供能解决真实痛点、创造实际价值的AI解决方案。</p>
          </div>
          
          <div class="advantage-card">
            <div class="advantage-icon">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <h4>技术驱动创新</h4>
            <p>我们的核心是强大的AI引擎，确保您获得的不是概念，而是真正的智能化体验与赋能。</p>
          </div>
          
          <div class="advantage-card">
            <div class="advantage-icon">
              <el-icon><Share /></el-icon>
            </div>
            <h4>生态化布局</h4>
            <p>我们着眼长远，通过平台化服务构建共赢生态，与客户、伙伴共同成长。</p>
          </div>
          
          <div class="advantage-card">
            <div class="advantage-icon">
              <el-icon><DataAnalysis /></el-icon>
            </div>
            <h4>客户价值导向</h4>
            <p>您的成功是我们前行的动力。我们提供从搭建到运维的全流程服务，让您专注核心业务，后顾无忧。</p>
          </div>
        </div>
      </div>

      <!-- 联系我们 -->
      <div class="section contact-section">
        <h2 class="section-title">联系我们 / 开启合作</h2>
        <div class="contact-content">
          <h3>开启您的AI升级之旅</h3>
          <p>我们已准备好为您提供专业的咨询与服务。</p>
          <p>立即联系我们，获取专属的AI解决方案。</p>
          
          <div class="contact-action">
            <el-button type="primary" class="consult-btn" @click="goToAIPromoter">
              <el-icon><ChatLineRound /></el-icon>
              一键咨询
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f9ff;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  min-width: 100vw;
  box-sizing: border-box;
}

.header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, #0D47A1, #2196F3);
  color: white;
  height: 3rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 100;
  box-sizing: border-box;
}

.back-button {
  color: white;
  margin-right: 1rem;
  font-size: 1.25rem;
  padding: 0.5rem;
  position: absolute;
  left: 0.5rem;
  z-index: 1;
}

.header h1 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  width: 100%;
  text-align: center;
}

.content {
  flex: 1;
  padding-top: 4.5rem;
  padding-bottom: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
  padding-left: 1rem;
  padding-right: 1rem;
}

/* 欢迎页样式 */
.welcome-section {
  background: linear-gradient(135deg, #1565C0, #42A5F5);
  border-radius: 1rem;
  padding: 2.5rem 1.5rem;
  margin-bottom: 1.5rem;
  text-align: center;
  color: white;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.company-name {
  font-size: 1.8rem;
  margin: 0 0 0.5rem 0;
  font-weight: 600;
}

.company-slogan {
  font-size: 1.2rem;
  margin: 0;
  opacity: 0.9;
}

/* 通用部分样式 */
.section {
  background-color: white;
  border-radius: 1rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.section-title {
  font-size: 1.5rem;
  color: #0D47A1;
  margin-top: 0;
  margin-bottom: 1.25rem;
  position: relative;
  padding-bottom: 0.5rem;
  display: inline-block;
}

.section-title::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 50px;
  height: 3px;
  background: linear-gradient(to right, #1976D2, #64B5F6);
  border-radius: 3px;
}

/* 关于我们部分 */
.about-content p {
  margin-bottom: 1rem;
  line-height: 1.6;
  font-size: 1rem;
}

.mission-box {
  background: rgba(33, 150, 243, 0.05);
  border-left: 4px solid #1976D2;
  padding: 1rem 1.25rem;
  border-radius: 0.5rem;
  margin-top: 1.5rem;
}

.mission-box p {
  margin-top: 0;
  font-weight: 500;
}

.mission-box ul {
  padding-left: 1.25rem;
  margin-bottom: 0;
}

.mission-box li {
  margin-bottom: 0.5rem;
  line-height: 1.5;
}

.mission-box li:last-child {
  margin-bottom: 0;
}

/* 核心业务部分 */
.business-card {
  background: rgba(33, 150, 243, 0.03);
  border-radius: 0.75rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border: 1px solid rgba(25, 118, 210, 0.1);
}

.business-card:last-child {
  margin-bottom: 0;
}

.business-header {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
  gap: 0.75rem;
}

.business-icon {
  width: 3rem;
  height: 3rem;
  font-size: 1.5rem;
  color: #1976D2;
  background: rgba(25, 118, 210, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.business-icon .el-icon {
  font-size: 1.5rem;
}

.business-header h3 {
  font-size: 1.25rem;
  color: #0D47A1;
  margin: 0;
}

.business-subtitle {
  font-size: 1rem;
  color: #1976D2;
  margin-bottom: 1rem;
  font-style: italic;
}

.business-content h4 {
  font-size: 1rem;
  color: #333;
  margin: 1.25rem 0 0.5rem 0;
}

.business-content h4:first-child {
  margin-top: 0;
}

.business-content p {
  margin: 0 0 1rem 0;
  line-height: 1.6;
  color: #444;
}

.service-path {
  padding-left: 0.5rem;
}

.service-item {
  margin-bottom: 1rem;
}

.service-item:last-child {
  margin-bottom: 0;
}

.service-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.service-title .el-icon {
  color: #1976D2;
  font-size: 1.1rem;
}

.service-title span {
  font-weight: 500;
  color: #333;
}

.service-item p {
  margin: 0;
  padding-left: 1.6rem;
}

/* 选择我们部分 */
.choose-subtitle {
  font-size: 1.1rem;
  color: #1976D2;
  margin-bottom: 1.5rem;
  text-align: center;
}

.advantages-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.25rem;
}

.advantage-card {
  background: white;
  border-radius: 0.75rem;
  padding: 1.5rem;
  text-align: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(25, 118, 210, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.advantage-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(25, 118, 210, 0.15);
}

.advantage-icon {
  width: 3.5rem;
  height: 3.5rem;
  background: linear-gradient(135deg, #1976D2, #42A5F5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem auto;
}

.advantage-icon .el-icon {
  font-size: 1.5rem;
  color: white;
}

.advantage-card h4 {
  font-size: 1.1rem;
  color: #0D47A1;
  margin: 0 0 0.75rem 0;
}

.advantage-card p {
  margin: 0;
  color: #555;
  line-height: 1.5;
}

/* 联系我们部分 */
.contact-section {
  text-align: center;
}

.contact-content h3 {
  font-size: 1.3rem;
  color: #0D47A1;
  margin: 0 0 1rem 0;
}

.contact-content p {
  margin: 0 0 0.75rem 0;
  color: #444;
  line-height: 1.5;
}

.contact-action {
  margin-top: 2rem;
}

.consult-btn {
  background: linear-gradient(135deg, #0D47A1, #2196F3);
  border: none;
  padding: 0.75rem 2rem;
  border-radius: 2rem;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1rem;
  font-weight: 500;
  box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
  transition: all 0.3s ease;
}

.consult-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(33, 150, 243, 0.4);
}

.consult-btn .el-icon {
  font-size: 1.1rem;
}

/* 媒体查询 - 平板和桌面端 */
@media (min-width: 768px) {
  .content {
    padding-left: 2rem;
    padding-right: 2rem;
  }
  
  .welcome-section {
    padding: 3.5rem 2rem;
  }
  
  .company-name {
    font-size: 2.5rem;
  }
  
  .company-slogan {
    font-size: 1.5rem;
  }
  
  .section {
    padding: 2rem;
  }
  
  .section-title {
    font-size: 1.8rem;
  }
  
  .section-title::after {
    width: 70px;
  }
  
  .business-header h3 {
    font-size: 1.5rem;
  }
  
  .advantages-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* 媒体查询 - 大屏幕 */
@media (min-width: 1024px) {
  .advantages-grid {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .business-card {
    padding: 2rem;
  }
}
</style> 