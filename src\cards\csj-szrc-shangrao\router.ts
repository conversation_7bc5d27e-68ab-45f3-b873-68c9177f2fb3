import { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'csjSzrcShangraoHome',
    component: () => import('./views/HomeView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/RenCaiJiDi/Logo.jpg',
      title: '长三角数字人才上饶创新基地'
    }
  },
  {
    path: '/introduction',
    name: 'csjSzrcShangraoIntroduction',
    component: () => import('./views/IntroductionView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/RenCaiJiDi/Logo.jpg',
      title: '长三角数字人才上饶创新基地 - 基地简介'
    }
  },
  {
    path: '/ai-promoter',
    name: 'csjSzrcShangraoAIPromoter',
    component: () => import('./views/AIPromoterView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/RenCaiJiDi/Logo.jpg',
      title: '长三角数字人才上饶创新基地 - AI智能宣传员'
    }
  },
  {
    path: '/services',
    name: 'csjSzrcShangraoServices',
    component: () => import('./views/ServicesView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/RenCaiJiDi/Logo.jpg',
      title: '长三角数字人才上饶创新基地 - 主要服务'
    }
  },
  {
    path: '/news',
    name: 'csjSzrcShangraoNews',
    component: () => import('./views/NewsView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/RenCaiJiDi/Logo.jpg',
      title: '长三角数字人才上饶创新基地 - 基地动态'
    }
  }
]

export default routes 