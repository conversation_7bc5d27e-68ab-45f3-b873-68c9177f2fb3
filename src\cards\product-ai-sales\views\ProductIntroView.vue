<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import {
  ArrowLeft,
  TrendCharts,
  Money,
  User,
  ChatDotRound,
  VideoPlay,
  Document,
  ArrowRight,
  Phone,
  Link
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { onMounted } from 'vue'


const router = useRouter()
const showPhoneModal = ref(false)
const phoneNumber = '18479656459'

const goBack = () => {
  router.push('/card/product-ai-sales')
}

const openAIChat = () => {
  window.open('https://ai.sdtaa.com:3105/chat/share?shareId=vlg8u11l58mmqtu9enrz53v8', '_blank')
}

const showPhone = () => {
  showPhoneModal.value = true
}

const copyPhone = async () => {
  try {
    await navigator.clipboard.writeText(phoneNumber)
    ElMessage.success('已复制到剪贴板')
  } catch (err) {
    // 如果剪贴板API不可用，使用备用方法
    const textArea = document.createElement('textarea')
    textArea.value = phoneNumber
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    ElMessage.success('已复制到剪贴板')
  }
}

const callPhone = () => {
  window.location.href = `tel:${phoneNumber}`
}

const goToCaseCenter = () => {
  // 跳转到主页的案例中心页面
  router.push('/card/product-ai-sales/case-center')
}

onMounted(() => {
  document.title = '产品AI推销官 - 产品介绍'
})
</script>

<template>
  <div class="view-container">
    <div class="header">
      <el-button type="text" @click="goBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <h1>产品介绍</h1>
    </div>

    <div class="content">
      <!-- 板块一：主标题和视频 -->
      <section class="hero-section">
        <div class="hero-content">
          <h1 class="main-title">为您的核心产品，配备一位永不离线的AI推销专家</h1>
          <p class="subtitle">产品AI推销官：将每一次产品咨询，都转化为一次深刻的价值认同。</p>

          <div class="video-container">
            <video
              src="https://pic.sdtaa.com/ZhiLian/Video/Enterprise/ChanPinAITuiXiaoGuan/XuanChuangShiPin.mp4"
              controls
              class="promo-video"
            >
              您的浏览器不支持视频播放
            </video>
            <div class="video-caption">
              <el-icon class="play-icon"><VideoPlay /></el-icon>
              <span>点击播放，见证AI如何让您的产品"开口说话"</span>
            </div>
          </div>
        </div>
      </section>

      <!-- 板块二：三大价值 -->
      <section class="values-section">
        <h2 class="section-title">我们为您的产品销售，实现三大价值</h2>
        <div class="values-grid">
          <div class="value-card">
            <div class="value-icon">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <h3 class="value-title">提升销售转化率 📈</h3>
            <p class="value-desc">AI精准讲解，深挖客户意向。我们的AI推销官能根据客户问题，智能匹配产品卖点与案例，让每一次互动都直击痛点，有效提升成交率。</p>
          </div>

          <div class="value-card">
            <div class="value-icon">
              <el-icon><Money /></el-icon>
            </div>
            <h3 class="value-title">降低沟通成本 💰</h3>
            <p class="value-desc">AI 7x24小时自动应答，解放专家人力。将团队从重复性的产品介绍和基础问答中解放出来，聚焦于最高价值的商务谈判与客户攻坚。</p>
          </div>

          <div class="value-card">
            <div class="value-icon">
              <el-icon><User /></el-icon>
            </div>
            <h3 class="value-title">赋能销售团队 👥</h3>
            <p class="value-desc">AI标准化金牌话术，团队战力瞬间拉满。将最优秀的产品知识固化到AI中，确保每一位销售人员，都能像顶级专家一样介绍您的产品。</p>
          </div>
        </div>
      </section>

      <!-- 板块三：解决方案 -->
      <section class="solutions-section">
        <h2 class="section-title">这些产品推广难题，我们一"官"解决</h2>
        <div class="solutions-list">
          <div class="solution-item">
            <div class="problem">产品太复杂，讲不清？</div>
            <el-icon class="arrow-icon"><ArrowRight /></el-icon>
            <div class="solution">AI深度解读，技术优势、应用场景一目了然。</div>
          </div>

          <div class="solution-item">
            <div class="problem">客户问题多，答不完？</div>
            <el-icon class="arrow-icon"><ArrowRight /></el-icon>
            <div class="solution">AI智能问答，7x24小时从容应对。</div>
          </div>

          <div class="solution-item">
            <div class="problem">销售口径不一，效果差？</div>
            <el-icon class="arrow-icon"><ArrowRight /></el-icon>
            <div class="solution">AI统一话术，确保每一次产品展示都完美无瑕。</div>
          </div>
        </div>
      </section>

      <!-- 板块四：核心模块 -->
      <section class="modules-section">
        <h2 class="section-title">三大核心模块，构成您的专属产品营销利器</h2>
        <div class="modules-grid">
          <div class="module-card">
            <div class="module-icon">
              <el-icon><ChatDotRound /></el-icon>
            </div>
            <h3 class="module-title">AI产品专家 <span class="core-tag">(核心引擎)</span></h3>
            <p class="module-desc">一位精通您产品所有细节的"技术型销售"，能深度讲解、智能问答、打消客户所有疑虑。</p>
          </div>

          <div class="module-card">
            <div class="module-icon">
              <el-icon><VideoPlay /></el-icon>
            </div>
            <h3 class="module-title">AI产品演示视频 <span class="visual-tag">(视觉锤)</span></h3>
            <p class="module-desc">运用AI数智人技术，生动、直观地演示产品核心功能与使用场景，让价值看得见。</p>
          </div>

          <div class="module-card">
            <div class="module-icon">
              <el-icon><Document /></el-icon>
            </div>
            <h3 class="module-title">AI产品门户 <span class="portal-tag">(线上说明书)</span></h3>
            <p class="module-desc">一个专为本产品打造的、结构化的线上信息中心，就是一本永不过期、随时可查的"智能产品说明书"。</p>
          </div>
        </div>
      </section>

      <!-- 板块五：优势对比 -->
      <section class="advantages-section">
        <h2 class="section-title">为何我们的介绍方式更胜一筹？</h2>
        <div class="comparison-grid">
          <div class="comparison-card">
            <h3 class="comparison-title">相比"产品官网/PPT/PDF"：</h3>
            <p class="comparison-desc">我们可互动、更智能。访客不再是被动浏览，而是可以与AI进行深度对话，按需获取信息，体验远超静态图文。</p>
          </div>

          <div class="comparison-card">
            <h3 class="comparison-title">相比"其他电子名片/H5"：</h3>
            <p class="comparison-desc">我们由AI驱动、会思考。核心是拥有"大脑"的AI推销官，能理解客户意图并主动引导，而非简单的信息陈列。</p>
          </div>
        </div>

        <div class="summary-quote">
          <p>我们不是要替代您的销售团队，而是为他们每人配备一个<strong>"永不疲倦、无所不知的产品专家助理"</strong>。</p>
        </div>
      </section>

      <!-- 板块六：服务方案 -->
      <section class="pricing-section">
        <h2 class="section-title">我们的服务方案</h2>
        <div class="pricing-grid">
          <div class="pricing-card">
            <h3 class="plan-title">标准版 <span class="plan-subtitle">(不含视频)</span></h3>
            <ul class="features-list">
              <li>专属AI产品专家</li>
              <li>专属AI产品门户</li>
              <li>微信小程序载体</li>
              <li>团队后台管理</li>
            </ul>
            <div class="price">¥ 6,000 <span class="price-unit">/年</span></div>
          </div>

          <div class="pricing-card featured">
            <h3 class="plan-title">专业版 <span class="plan-subtitle">(含视频)</span></h3>
            <p class="plan-desc">包含标准版全部功能，并增加：</p>
            <ul class="features-list">
              <li>AI数智人产品演示视频（1个）</li>
            </ul>
            <div class="price">¥ 8,000 <span class="price-unit">/年</span></div>
          </div>

          <div class="pricing-card">
            <h3 class="plan-title">高级版 <span class="plan-subtitle">(企业定制)</span></h3>
            <ul class="features-list">
              <li>高级语音交互</li>
              <li>实时数字人交互</li>
              <li>线下一体化大屏部署</li>
              <li>电影级宣传视频</li>
              <li>品牌化网页深度定制</li>
            </ul>
            <div class="price">按需定制，请洽谈</div>
          </div>
        </div>
      </section>

      <!-- 板块七：案例展示 -->
      <section class="cases-section">
        <h2 class="section-title">深受各行业王牌产品的信赖</h2>
        <p class="cases-desc">无论是前沿的AI解决方案，还是专业的咨询服务，"产品AI推销官"都能为其注入强大的营销动能。</p>
        <div class="case-link" @click="goToCaseCenter">
          <el-icon class="arrow-icon"><ArrowRight /></el-icon>
          <span>查看"产品AI推销官"成功案例</span>
        </div>
      </section>

      <!-- 板块八：流程说明 -->
      <section class="process-section">
        <h2 class="section-title">两步开启，让您的产品自己"跑业务"</h2>
        <div class="process-flow">
          <div class="process-step">
            <div class="step-number">1</div>
            <div class="step-content">
              <h3 class="step-title">您提供产品资料</h3>
              <p class="step-desc">(只需将您现有的产品手册、介绍PPT等打包给我们)</p>
            </div>
          </div>

          <div class="process-arrow">
            <el-icon><ArrowRight /></el-icon>
          </div>

          <div class="process-step">
            <div class="step-number">2</div>
            <div class="step-content">
              <h3 class="step-title">我们为您打造专属AI推销官</h3>
              <p class="step-desc">(从AI训练、视频生成到门户设计，我们全流程服务)</p>
            </div>
          </div>
        </div>

        <div class="core-promise">
          <p>"您负责打造伟大的产品，我们负责让世界听懂它的伟大。"</p>
        </div>
      </section>

      <!-- 板块九：行动号召 -->
      <section class="cta-section">
        <h2 class="section-title">立即行动，给您的产品一个金牌销售</h2>
        <div class="cta-buttons">
          <button class="cta-primary" @click="showPhone">
            <el-icon><Phone /></el-icon>
            预约专属方案演示
          </button>
          <button class="cta-secondary" @click="openAIChat">
            <el-icon><Link /></el-icon>
            与我的AI推销官聊聊
          </button>
        </div>
      </section>
    </div>

    <!-- 电话号码弹窗 -->
    <el-dialog v-model="showPhoneModal" width="300px" center>
      <template #header>
        <div class="dialog-header">联系我们</div>
      </template>
      <div class="phone-modal">
        <p class="phone-number">{{ phoneNumber }}</p>
        <div class="phone-actions">
          <el-button @click="copyPhone" type="primary" plain>复制号码</el-button>
          <el-button @click="callPhone" type="primary">立即拨打</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<style scoped>
.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e3f2fd 100%);
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  min-width: 100vw;
  box-sizing: border-box;
}

.header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, #1976d2, #42a5f5);
  color: white;
  height: 3rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 100;
  box-sizing: border-box;
  backdrop-filter: blur(10px);
}

.back-button {
  color: white;
  margin-right: 1rem;
  font-size: 1.25rem;
  padding: 0.5rem;
  position: absolute;
  left: 0.5rem;
  z-index: 1;
}

.header h1 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  width: 100%;
  text-align: center;
}

.content {
  flex: 1;
  padding-top: 4.5rem;
  padding-bottom: 2rem; /* 移除TabBar预留空间 */
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
  padding-left: 1rem;
  padding-right: 1rem;
}

/* 通用样式 */
section {
  margin-bottom: 3rem;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  text-align: center;
  margin-bottom: 2rem;
  background: linear-gradient(135deg, #1976d2, #42a5f5);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 板块一：主标题和视频 */
.hero-section {
  text-align: center;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 1.5rem;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  margin-bottom: 3rem;
}

.main-title {
  font-size: 1.7rem;
  font-weight: 700;
  background: linear-gradient(135deg, #1976d2, #42a5f5);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 1rem;
  line-height: 1.4;
}

.subtitle {
  font-size: 1.1rem;
  color: #666;
  margin-bottom: 2rem;
  line-height: 1.6;
}

.video-container {
  position: relative;
  max-width: 800px;
  margin: 0 auto;
}

.promo-video {
  width: 100%;
  border-radius: 1rem;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.video-caption {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 1rem;
  color: #1976d2;
  font-size: 0.8rem;
}

.play-icon {
  margin-right: 0.5rem;
  font-size: 1.2rem;
}

/* 板块二：三大价值 */
.values-section {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 1.5rem;
  padding: 2rem;
  backdrop-filter: blur(10px);
}

.values-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

.value-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 1rem;
  padding: 1.5rem;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.value-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.value-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  background: linear-gradient(135deg, #1976d2, #42a5f5);
  border-radius: 50%;
  margin-bottom: 1rem;
  color: white;
  font-size: 1.5rem;
}

.value-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #1976d2;
  margin-bottom: 1rem;
}

.value-desc {
  color: #666;
  line-height: 1.6;
  font-size: 0.95rem;
}

/* 板块三：解决方案 */
.solutions-section {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 1.5rem;
  padding: 2rem;
  backdrop-filter: blur(10px);
}

.solutions-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.solution-item {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease;
}

.solution-item:hover {
  transform: translateX(5px);
}

.problem {
  flex: 1;
  font-weight: 500;
  color: #333;
}

.arrow-icon {
  margin: 0 1rem;
  color: #1976d2;
  font-size: 1.2rem;
}

.solution {
  flex: 1;
  color: #1976d2;
  font-weight: 500;
}

/* 板块四：核心模块 */
.modules-section {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 1.5rem;
  padding: 2rem;
  backdrop-filter: blur(10px);
}

.modules-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

.module-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 1rem;
  padding: 1.5rem;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.module-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.module-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  background: linear-gradient(135deg, #1976d2, #42a5f5);
  border-radius: 50%;
  margin-bottom: 1rem;
  color: white;
  font-size: 1.5rem;
}

.module-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1976d2;
  margin-bottom: 1rem;
}

.core-tag, .visual-tag, .portal-tag {
  font-size: 0.8rem;
  padding: 0.2rem 0.5rem;
  border-radius: 0.5rem;
  font-weight: 400;
}

.core-tag {
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  color: white;
}

.visual-tag {
  background: linear-gradient(135deg, #4ecdc4, #7fdbda);
  color: white;
}

.portal-tag {
  background: linear-gradient(135deg, #45b7d1, #74c7ec);
  color: white;
}

.module-desc {
  color: #666;
  line-height: 1.6;
  font-size: 0.95rem;
}

/* 板块五：优势对比 */
.advantages-section {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 1.5rem;
  padding: 2rem;
  backdrop-filter: blur(10px);
}

.comparison-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.comparison-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.comparison-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1976d2;
  margin-bottom: 1rem;
}

.comparison-desc {
  color: #666;
  line-height: 1.6;
  font-size: 0.95rem;
}

.summary-quote {
  background: linear-gradient(135deg, #1976d2, #42a5f5);
  color: white;
  border-radius: 1rem;
  padding: 1.5rem;
  text-align: center;
}

.summary-quote p {
  margin: 0.5rem 0;
  line-height: 1.6;
}

/* 板块六：服务方案 */
.pricing-section {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 1.5rem;
  padding: 2rem;
  backdrop-filter: blur(10px);
}

.pricing-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

.pricing-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
}

.pricing-card.featured {
  border: 2px solid #1976d2;
  transform: scale(1.02);
}

.pricing-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.pricing-card.featured:hover {
  transform: scale(1.02) translateY(-5px);
}

.plan-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #1976d2;
  margin-bottom: 0.5rem;
}

.plan-subtitle {
  font-size: 0.9rem;
  font-weight: 400;
  color: #666;
}

.plan-desc {
  color: #666;
  margin: 1rem 0;
  font-size: 0.95rem;
}

.features-list {
  list-style: none;
  padding: 0;
  margin: 1rem 0;
}

.features-list li {
  padding: 0.5rem 0;
  color: #666;
  position: relative;
  padding-left: 1.5rem;
}

.features-list li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: #1976d2;
  font-weight: bold;
}

.price {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1976d2;
  text-align: center;
  margin-top: 1rem;
}

.price-unit {
  font-size: 1rem;
  font-weight: 400;
  color: #666;
}

/* 板块七：案例展示 */
.cases-section {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 1.5rem;
  padding: 2rem;
  backdrop-filter: blur(10px);
  text-align: center;
}

.cases-desc {
  color: #666;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  font-size: 1rem;
}

.case-link {
  display: inline-flex;
  align-items: center;
  background: linear-gradient(135deg, #1976d2, #42a5f5);
  color: white;
  padding: 0.8rem 1.5rem;
  border-radius: 2rem;
  cursor: pointer;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  font-weight: 500;
}

.case-link .arrow-icon {
  color: white;
}

.case-link:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(25, 118, 210, 0.3);
}

/* 板块八：流程说明 */
.process-section {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 1.5rem;
  padding: 2rem;
  backdrop-filter: blur(10px);
}

.process-flow {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.process-step {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  width: 100%;
  max-width: 500px;
}

.step-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  background: linear-gradient(135deg, #1976d2, #42a5f5);
  color: white;
  border-radius: 50%;
  font-weight: 600;
  font-size: 1.2rem;
  margin-right: 1rem;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
}

.step-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1976d2;
  margin-bottom: 0.5rem;
}

.step-desc {
  color: #666;
  font-size: 0.9rem;
  line-height: 1.5;
}

.process-arrow {
  color: #1976d2;
  font-size: 1.5rem;
}

.core-promise {
  background: linear-gradient(135deg, #1976d2, #42a5f5);
  color: white;
  border-radius: 1rem;
  padding: 1.5rem;
  text-align: center;
}

.core-promise p {
  margin: 0.5rem 0;
  line-height: 1.6;
}

/* 板块九：行动号召 */
.cta-section {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 1.5rem;
  padding: 2rem;
  backdrop-filter: blur(10px);
  text-align: center;
}

.cta-buttons {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  max-width: 400px;
  margin: 0 auto;
}

.cta-primary, .cta-secondary {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 1rem 2rem;
  border-radius: 2rem;
  font-size: 1rem;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cta-primary {
  background: linear-gradient(135deg, #1976d2, #42a5f5);
  color: white;
  box-shadow: 0 4px 15px rgba(25, 118, 210, 0.3);
}

.cta-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(25, 118, 210, 0.4);
}

.cta-secondary {
  background: rgba(25, 118, 210, 0.1);
  color: #1976d2;
  border: 2px solid #1976d2;
}

.cta-secondary:hover {
  background: rgba(25, 118, 210, 0.2);
  transform: translateY(-2px);
}

/* 电话弹窗 */
:deep(.el-dialog__header) {
  text-align: center;
  padding: 20px 20px 10px 20px;
}

.dialog-header {
  text-align: center;
  font-weight: 600;
  color: #1976d2;
  width: 100%;
  display: block;
  margin: 0;
}

.phone-modal {
  text-align: center;
}

.phone-number {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1976d2;
  margin-bottom: 1rem;
}

.phone-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

/* 响应式设计 */
@media (min-width: 768px) {
  .content {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .main-title {
    font-size: 2.2rem;
  }

  .subtitle {
    font-size: 1.2rem;
  }

  .section-title {
    font-size: 1.8rem;
  }

  .values-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .modules-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .comparison-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .pricing-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .process-flow {
    flex-direction: row;
    justify-content: center;
  }

  .process-step {
    max-width: 300px;
  }

  .process-arrow {
    transform: rotate(0deg);
  }

  .cta-buttons {
    max-width: 600px;
  }

  .solution-item {
    flex-direction: row;
  }
}

@media (min-width: 1200px) {
  .hero-section {
    padding: 3rem;
  }

  .main-title {
    font-size: 2.5rem;
  }

  .subtitle {
    font-size: 1.3rem;
  }

  .section-title {
    font-size: 2rem;
  }

  section {
    margin-bottom: 4rem;
  }
}
</style>
