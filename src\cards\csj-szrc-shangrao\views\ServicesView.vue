<script setup lang="ts">
import { useRouter } from 'vue-router'
import { ArrowLeft, Promotion, SetUp, OfficeBuilding, Connection, Monitor } from '@element-plus/icons-vue'
import { ref, onMounted } from 'vue'

const router = useRouter()

const goBack = () => {
  router.push('/card/csj-szrc-shangrao')
}

onMounted(() => {
  document.title = '长三角数字人才上饶创新基地 - 主要服务'
})

// 视频链接
const aiPlatformVideo = ref('https://pic.sdtaa.com/videos/xhszmp/xxpt.mp4')
const employmentPlatformVideo = ref('https://pic.sdtaa.com/videos/xhszmp/jypt.mp4')

// 控制折叠面板状态
const activeNames = ref(['1', '2', '3', '4', '5'])
</script>

<template>
  <div class="view-container">
    <div class="header">
      <el-button type="text" @click="goBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <h1>主要服务</h1>
    </div>

    <div class="content">
      <div class="services-container">
        <!-- 服务概述 -->
        <div class="service-overview">
          <p class="overview-text">我们围绕"人才培养、技术创新、产业孵化、招商引资"四大核心方向，采取以下主要举措：</p>
        </div>
        
        <!-- 服务模块列表 -->
        <el-collapse v-model="activeNames" class="service-collapse">
          <!-- 数字人才培养 -->
          <el-collapse-item name="1">
            <template #title>
              <div class="collapse-header">
                <div class="icon-container">
                  <el-icon class="service-icon"><Promotion /></el-icon>
                </div>
                <span class="collapse-title">数字人才培养 (强基育才)</span>
              </div>
            </template>
            
            <div class="service-content">
              <div class="service-goal">
                <h4>目标：</h4>
                <p>构建适应产业需求的数字人才培养体系，培养高素质应用型、复合型数字人才。</p>
              </div>
              
              <div class="service-detail">
                <h4>课程体系：</h4>
                <p>采用"1+5+N"多层次、递进式课程，覆盖大专院校学生、社会人员及企业员工。</p>
                <ul class="detail-list">
                  <li><strong>"1"指人工智能应用：</strong>培养学员的数字思维、创新意识和解决问题的能力，为后续专业学习奠定基础。</li>
                  <li><strong>"5"指五大产业方向：</strong>基于上饶市及江西省地区的产业发展优势，设置数字营销、智转数改、数字文旅、低空经济、数据要素五大核心产业方向。</li>
                  <li><strong>"N"指N个细分行业/岗位：</strong>在产业方向的基础上，针对具体行业和岗位需求，设置更加细分的专业技能课程。</li>
                </ul>
              </div>
              
              <div class="service-measures">
                <h4>关键举措：</h4>
                <ul class="measures-list">
                  <li>整合高校、协会、企业资源，深化职业教育改革，完善校企合作。</li>
                  <li>深化产教融合，与重点企业共建课程、实践平台、联合项目，动态调整专业方向对接江西"1269"计划。</li>
                  <li>优化人才发展环境，完善评价激励机制，打通人才双向流动通道，提供优质就业指导。</li>
                </ul>
              </div>
            </div>
          </el-collapse-item>
          
          <!-- 技术创新与转化 -->
          <el-collapse-item name="2">
            <template #title>
              <div class="collapse-header">
                <div class="icon-container">
                  <el-icon class="service-icon"><SetUp /></el-icon>
                </div>
                <span class="collapse-title">技术创新与转化 (驱动发展)</span>
              </div>
            </template>
            
            <div class="service-content">
              <div class="service-goal">
                <h4>目标：</h4>
                <p>鼓励创新思维，促进数字技术创新和科研成果转化，为区域数字经济发展提供技术支撑。</p>
              </div>
              
              <div class="service-measures">
                <h4>关键举措：</h4>
                <ul class="measures-list">
                  <li>建设数字技术创新孵化平台，吸引和汇聚创新团队与项目，提供全方位服务。</li>
                  <li>积极探索与省内各类创新平台合作，促进科技成果转移转化和产业化落地。</li>
                  <li>引导创新要素向企业集聚，支持企业成为数字技术创新主体。</li>
                  <li>对接江西省数字技术重点攻关方向，通过项目实践与赛事活动，为关键技术应用与攻关提供人才和实践支撑。</li>
                </ul>
              </div>
            </div>
          </el-collapse-item>
          
          <!-- 产业孵化与生态 -->
          <el-collapse-item name="3">
            <template #title>
              <div class="collapse-header">
                <div class="icon-container">
                  <el-icon class="service-icon"><OfficeBuilding /></el-icon>
                </div>
                <span class="collapse-title">产业孵化与生态 (培育新机)</span>
              </div>
            </template>
            
            <div class="service-content">
              <div class="service-goal">
                <h4>目标：</h4>
                <p>为创业团队提供全方位支持，孵化数字经济领域的新兴企业，培育新的经济增长点。</p>
              </div>
              
              <div class="service-measures">
                <h4>关键举措：</h4>
                <ul class="measures-list">
                  <li>常态化举办数字技术技能大赛、创新创业大赛、高峰论坛等活动，打造交流合作平台。</li>
                  <li>通过赛事活动发现和挖掘优秀人才及项目，促进人才、技术、资本等要素有效对接。</li>
                  <li>营造对数字人才友好、鼓励创新创业的生态环境。</li>
                </ul>
              </div>
            </div>
          </el-collapse-item>
          
          <!-- 招商引资与区域服务 -->
          <el-collapse-item name="4">
            <template #title>
              <div class="collapse-header">
                <div class="icon-container">
                  <el-icon class="service-icon"><Connection /></el-icon>
                </div>
                <span class="collapse-title">招商引资与区域服务 (聚链赋能)</span>
              </div>
            </template>
            
            <div class="service-content">
              <div class="service-goal">
                <h4>目标：</h4>
                <p>发挥人才优势，吸引数字经济企业落户，形成人才与产业的良性互动，促进区域数字经济产业集聚。</p>
              </div>
              
              <div class="service-measures">
                <h4>关键举措：</h4>
                <ul class="measures-list">
                  <li>根据区域重点产业发展方向，精准培养输送人才。</li>
                  <li>通过提供人才、技术和孵化服务，增强区域对数字经济企业的吸引力。</li>
                  <li>主动对接并融入省市"一产一策"、"一市一品"和"1269"行动计划中的人才工作，发挥人才在推动产业链、创新链、人才链、资金链深度融合中的支撑作用。</li>
                </ul>
              </div>
            </div>
          </el-collapse-item>
          
          <!-- 核心平台建设 -->
          <el-collapse-item name="5">
            <template #title>
              <div class="collapse-header">
                <div class="icon-container">
                  <el-icon class="service-icon"><Monitor /></el-icon>
                </div>
                <span class="collapse-title">核心平台建设</span>
              </div>
            </template>
            
            <div class="service-content">
              <!-- 平台1：AI学习及应用平台 -->
              <div class="platform-item">
                <h3 class="platform-title">1. 上饶市人工智能学习及应用平台</h3>
                <p class="platform-desc">面向政府公职人员、企业员工及社会大众，提供便捷高效的AI通识与应用学习，提升全民人工智能数字素养。</p>
                
                <div class="platform-section">
                  <h4>产品介绍</h4>
                  <p>上饶市人工智能学习及应用平台是基于本地化DeepSeek大模型、专为公共部门设计的"学-练-用"一体化平台。通过系统化实战课程和智能化伴学支持，帮助政务人员快速掌握AI应用技能，解决实际工作问题，提升政务效能。</p>
                  
                  <div class="video-container">
                    <video controls class="platform-video" :src="aiPlatformVideo"></video>
                    <p class="video-caption">产品宣传视频</p>
                  </div>
                </div>
                
                <div class="platform-section">
                  <h4>市场痛点</h4>
                  <ul class="platform-list">
                    <li><strong>政务人员：</strong>面对人工智能浪潮和工作要求，普遍存在"AI知识盲区"和"工具使用障碍"。传统培训方式效率低、内容滞后、脱离实际工作场景，即使本地部署了先进的DeepSeek大模型，也难以有效学习和应用，导致"学用脱节"</li>
                    <li><strong>政府管理者：</strong>急需提升公务员队伍整体的AI素养和应用能力，以满足"数字政府"建设要求。但缺乏一个统一、权威、高效、且紧密结合本地政务需求的AI学习与实训平台，现有技术优势未能充分转化为实际治理效能</li>
                  </ul>
                </div>
                
                <div class="platform-section">
                  <h4>解决方案</h4>
                  <ul class="platform-list">
                    <li><strong>专属AI学习与应用中心：</strong>打造上饶市首个基于本地化DeepSeek大模型、专为公共部门设计的"学-练-用"一体化平台</li>
                    <li><strong>系统化实战课程：</strong>提供从AI通识到核心政务场景（如公文写作、会议纪要、文件解读、PPT制作等）应用的完整课程体系，内容权威、形式新颖（采用数字人讲解、微课模式）</li>
                    <li><strong>智能化伴学支持：</strong>配备AI伴学助手进行个性化规划与推荐，AI老师实时互动答疑，并内置针对具体任务的"数字员工"（智能体），辅助完成实操练习，确保学得会、用得上</li>
                    <li><strong>学用无缝衔接：</strong>课程直接对接本地DeepSeek能力，并提供配套工具、模板和智能体，让学习成果即时转化为工作能力，聚焦解决实际问题</li>
                  </ul>
                </div>
                
                <div class="platform-section">
                  <h4>预期效果</h4>
                  <ul class="platform-list">
                    <li><strong>人员能力跃升：</strong>帮助全市公务员快速扫除AI认知盲点，熟练掌握DeepSeek等AI工具在政务工作中的应用技巧，显著提升个人工作效率与数字化能力</li>
                    <li><strong>政务效能提升：</strong>通过AI赋能，优化公文处理、会务管理、政策分析等工作流程，缩短响应时间，提高服务质量，有力支撑"数字政府"建设提速增效</li>
                    <li><strong>打造示范标杆：</strong>建成全省领先、具有上饶特色的AI赋能政务学习与应用平台，形成可复制、可推广的"上饶经验"，提升区域数字化转型影响力</li>
                  </ul>
                </div>
              </div>
              
              <!-- 平台2：就业服务平台 -->
              <div class="platform-item">
                <h3 class="platform-title">2. 上饶市数字人才就业服务平台</h3>
                <p class="platform-desc">联合人社部门共建"数字人才就业之家"，提供就业指导、岗位对接、专场招聘等一站式服务，助推人才留饶留赣。</p>
                
                <div class="platform-section">
                  <h4>产品介绍</h4>
                  <p>我们运用生成式AI等前沿技术，打造了一个专注于服务待就业青年及用人单位的数字化人才服务平台。核心目标是精准连接人才与岗位，打通求职到入职的"最后一公里"。</p>
                  
                  <div class="video-container">
                    <video controls class="platform-video" :src="employmentPlatformVideo"></video>
                    <p class="video-caption">产品宣传视频</p>
                  </div>
                </div>
                
                <div class="platform-section">
                  <h4>市场痛点</h4>
                  <ul class="platform-list">
                    <li><strong>求职者之困：</strong>职业方向迷茫，缺乏清晰规划；自身技能与市场需求存在错配；简历投递效率低，面试准备不足</li>
                    <li><strong>企业之难：</strong>招聘周期长，合适人才难寻；用人成本高，人岗匹配效率低</li>
                    <li><strong>培训之惑：</strong>培训内容更新滞后，学员就业转化难</li>
                    <li>这些痛点相互影响，制约了人才价值的实现和区域经济的发展</li>
                  </ul>
                </div>
                
                <div class="platform-section">
                  <h4>解决方案</h4>
                  <ul class="platform-list">
                    <li><strong>赋能待就业青年-精准定位：</strong>提供AI职业诊断与规划，生成能力画像，推荐适配方向</li>
                    <li><strong>赋能待就业青年-高效求职：</strong>智能简历优化工具提升通过率，AI模拟面试助力表现提升</li>
                    <li><strong>赋能待就业青年-持续成长：</strong>动态规划职业学习路径，支持终身学习与技能迭代</li>
                    <li><strong>赋能企业-高效招聘：</strong>AI自动生成岗位JD，大模型智能初筛简历，显著缩短招聘周期</li>
                    <li><strong>赋能企业-精准匹配：</strong>构建企业专属人才库，实现人岗需求的精准对接</li>
                    <li><strong>赋能职业培训机构：</strong>提供AI智能客服、AI教学管理工具等，提升培训效率与效果</li>
                  </ul>
                </div>
                
                <div class="platform-section">
                  <h4>预期效果</h4>
                  <ul class="platform-list">
                    <li><strong>对求职者：</strong>提升求职精准度与成功率；获得清晰的职业发展规划与技能提升路径</li>
                    <li><strong>对企业：</strong>大幅提升招聘效率，降低用人成本；提高人才匹配精准度，找到更合适的人才</li>
                    <li><strong>对区域发展：</strong>有效促进本地青年就业，缓解人才供需矛盾；为数字经济发展提供坚实的人才支撑</li>
                  </ul>
                </div>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
  </div>
</template>

<style scoped>
.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f9ff;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  min-width: 100vw;
  box-sizing: border-box;
}

.header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, #0D47A1, #2196F3);
  color: white;
  height: 3rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 100;
  box-sizing: border-box;
}

.back-button {
  color: white;
  margin-right: 1rem;
  font-size: 1.25rem;
  padding: 0.5rem;
  position: absolute;
  left: 0.5rem;
  z-index: 1;
}

.header h1 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  width: 100%;
  text-align: center;
}

.content {
  flex: 1;
  padding-top: 4.5rem;
  padding-bottom: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
  padding-left: 1rem;
  padding-right: 1rem;
}

.services-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* 服务概述 */
.service-overview {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  text-align: center;
}

.overview-text {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #0D47A1;
  margin: 0;
  font-weight: 500;
}

/* 折叠面板样式 */
.service-collapse {
  --el-collapse-header-height: auto;
  --el-collapse-header-bg-color: white;
  --el-collapse-header-text-color: #0D47A1;
  --el-collapse-header-font-size: 1rem;
  --el-collapse-content-bg-color: white;
  --el-collapse-content-font-size: 0.95rem;
  --el-collapse-border-color: transparent;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border-radius: 1rem;
  overflow: hidden;
}

.service-collapse :deep(.el-collapse-item__header) {
  padding: 1rem;
  border-bottom: 1px solid rgba(25, 118, 210, 0.1);
}

.service-collapse :deep(.el-collapse-item__content) {
  padding: 0;
}

.service-collapse :deep(.el-collapse-item:last-child .el-collapse-item__header) {
  border-bottom: none;
}

.collapse-header {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  background: rgba(33, 150, 243, 0.1);
}

.service-icon {
  color: #1976D2;
  font-size: 1.5rem;
}

.collapse-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #0D47A1;
}

/* 服务内容样式 */
.service-content {
  padding: 1.5rem;
  color: #333;
  line-height: 1.6;
}

.service-goal, .service-detail, .service-measures {
  margin-bottom: 1.5rem;
}

.service-goal h4, .service-detail h4, .service-measures h4, .platform-section h4 {
  color: #0D47A1;
  margin: 0 0 0.5rem 0;
  font-size: 1.05rem;
  font-weight: 600;
}

.detail-list, .measures-list, .platform-list {
  padding-left: 1.25rem;
  margin: 0.75rem 0;
}

.detail-list li, .measures-list li, .platform-list li {
  margin-bottom: 0.75rem;
}

.detail-list li:last-child, .measures-list li:last-child, .platform-list li:last-child {
  margin-bottom: 0;
}

/* 平台样式 */
.platform-item {
  margin-bottom: 2.5rem;
  padding-bottom: 2rem;
  border-bottom: 1px dashed rgba(25, 118, 210, 0.2);
}

.platform-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.platform-title {
  color: #0D47A1;
  font-size: 1.2rem;
  margin: 0 0 0.75rem 0;
}

.platform-desc {
  color: #555;
  font-style: italic;
  margin: 0 0 1.5rem 0;
}

.platform-section {
  margin-bottom: 1.5rem;
}

.platform-section:last-child {
  margin-bottom: 0;
}

.video-container {
  margin: 1.5rem 0;
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.platform-video {
  width: 100%;
  display: block;
  aspect-ratio: 16/9;
  background-color: #000;
}

.video-caption {
  text-align: center;
  background: #f5f5f5;
  margin: 0;
  padding: 0.5rem;
  font-size: 0.9rem;
  color: #555;
}

/* 媒体查询-平板和桌面端 */
@media (min-width: 768px) {
  .service-overview {
    padding: 2rem;
  }
  
  .overview-text {
    font-size: 1.25rem;
  }
  
  .service-collapse :deep(.el-collapse-item__header) {
    padding: 1.25rem;
  }
  
  .icon-container {
    width: 3.5rem;
    height: 3.5rem;
  }
  
  .service-icon {
    font-size: 1.75rem;
  }
  
  .collapse-title {
    font-size: 1.25rem;
  }
  
  .service-content {
    padding: 2rem;
  }
  
  .service-goal h4, .service-detail h4, .service-measures h4, .platform-section h4 {
    font-size: 1.15rem;
  }
  
  .platform-title {
    font-size: 1.35rem;
  }
}

/* 超大屏幕 */
@media (min-width: 1200px) {
  .content {
    padding-left: 2rem;
    padding-right: 2rem;
  }
  
  .services-container {
    gap: 2rem;
  }
}
</style>