<script setup lang="ts">
import { useRouter } from 'vue-router'
import { ref, onMounted } from 'vue'
import { 
  ArrowLeft, 
  Link,
  Briefcase,
  Promotion,
  Avatar
} from '@element-plus/icons-vue'

// 定义案例分类接口
interface CaseCategory {
  id: string;
  title: string;
  icon: any;
  cases: Case[];
}

// 定义案例接口
interface Case {
  id: string;
  name: string;
  description: string;
  logo: string;
  link: string;
}

const router = useRouter()

const goBack = () => {
  router.push('/card/wisestar-tech')
}

// 定义案例数据
const caseCategories = ref<CaseCategory[]>([
  {
    id: 'ai-mingpian-enterprise',
    title: '企业AI名片 - 企业案例',
    icon: Briefcase,
    cases: [
      {
        id: 'sdtaa',
        name: '上饶市数字技术应用协会',
        description: '通过AI名片，实现团队能力标准化，十倍提升对外沟通效率。',
        logo: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/STDAA/sdtaa-logo.jpg',
        link: 'https://www.sdtaa.com/contact'
      },
      {
        id: 'houri-capital',
        name: '后日资本',
        description: '利用AI名片，在多场景下实现高效、专业的品牌输出。',
        logo: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/HouRiZiBen/Logo2.jpg',
        link: 'https://zl.sdtaa.com/card/houri-capital'
      },
      {
        id: 'shida-erbao',
        name: '师大儿保托育中心',
        description: 'AI宣传员7x24小时接待家长，将咨询转化率提升30%。',
        logo: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ShiDaErBao/Logo.jpg',
        link: 'https://zl.sdtaa.com/card/shida-erbao-tuoyu'
      },
      {
        id: 'csj-szrc',
        name: '长三角数字人才创新实践基地',
        description: '通过AI名片，实现团队能力标准化，十倍提升对外沟通效率。',
        logo: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/RenCaiJiDi/Logo.jpg',
        link: 'https://zl.sdtaa.com/card/csj-szrc-shangrao'
      }
    ]
  },
  {
    id: 'ai-mingpian-product',
    title: '企业AI名片 - 产品案例',
    icon: Avatar,
    cases: [
      {
        id: 'ai-sz-agent',
        name: 'AI数智代言人',
        description: '产品AI名片嵌入销售全流程，提升沟通效率，降低销售人员能力要求，提升销售转化率',
        logo: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/AIShuZhiDaiYanRen/Logo.jpg',
        link: 'https://zl.sdtaa.com/card/ai-sz-agent'
      }
    ]
  },
  {
    id: 'ai-sz-guide',
    title: 'AI数智推介官',
    icon: Promotion,
    cases: [
      {
        id: 'srcm',
        name: '上饶传媒集团',
        description: '借助AI名片，打通内部业务壁垒，并开创"AI数智代言人"新增长曲线',
        logo: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ShangRaoChuanMei/logo.jpg',
        link: 'https://www.sdtaa.com/external-promotion/srcm'
      },
      {
        id: 'gfq-scjgj',
        name: '上饶市广丰区市场监督管理局',
        description: '运用AI推介官，将政策咨询效率提升80%，打造智慧政务新标杆。',
        logo: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/GuangFengShiChang/logo.png',
        link: 'https://zl.sdtaa.com/card/gfq-scjgj'
      }
    ]
  }
])

// 打开外部链接
const openExternalLink = (url: string) => {
  window.open(url, '_blank')
}

onMounted(() => {
  document.title = '杭州智衍星辰科技 - 案例中心'
  const link = document.querySelector("link[rel~='icon']") as HTMLLinkElement
  if (link) {
    link.href = 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiYanXingChen/Logo.jpg'
  } else {
    const newLink = document.createElement('link')
    newLink.rel = 'icon'
    newLink.href = 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiYanXingChen/Logo.jpg'
    document.head.appendChild(newLink)
  }
})
</script>

<template>
  <div class="view-container">
    <div class="header">
      <el-button type="text" @click="goBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <h1>案例中心</h1>
    </div>

    <div class="content">
      <div class="cases-intro">
        <h2 class="section-title">成功案例</h2>
        <p class="section-desc">智能科技赋能，助力企业数字化转型</p>
      </div>
      
      <div v-for="category in caseCategories" :key="category.id" class="case-category">
        <div class="category-header">
          <div class="category-icon">
            <el-icon>
              <Briefcase v-if="category.id === 'ai-mingpian-enterprise'" />
              <Avatar v-else-if="category.id === 'ai-mingpian-product'" />
              <Promotion v-else-if="category.id === 'ai-sz-guide'" />
            </el-icon>
          </div>
          <h3 class="category-title">{{ category.title }}</h3>
        </div>
        
        <div class="cases-grid">
          <div v-for="caseItem in category.cases" :key="caseItem.id" class="case-card">
            <div class="case-content">
              <div class="case-logo">
                <img :src="caseItem.logo" :alt="caseItem.name" class="logo-image">
              </div>
              <div class="case-info">
                <h3 class="case-name">{{ caseItem.name }}</h3>
                <p class="case-desc">{{ caseItem.description }}</p>
              </div>
            </div>
            <div class="case-actions">
              <el-button 
                type="primary" 
                class="action-btn link-btn"
                @click="openExternalLink(caseItem.link)"
              >
                <el-icon><Link /></el-icon>
                访问案例
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f9ff;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  min-width: 100vw;
  box-sizing: border-box;
}

.header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, #0D47A1, #2196F3);
  color: white;
  height: 3rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 100;
  box-sizing: border-box;
}

.back-button {
  color: white;
  margin-right: 1rem;
  font-size: 1.25rem;
  padding: 0.5rem;
  position: absolute;
  left: 0.5rem;
  z-index: 1;
}

.header h1 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  width: 100%;
  text-align: center;
}

.content {
  flex: 1;
  padding-top: 4.5rem;
  padding-bottom: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
  padding-left: 1rem;
  padding-right: 1rem;
}

.cases-intro {
  text-align: center;
  margin-bottom: 2rem;
}

.section-title {
  font-size: 1.8rem;
  color: #0D47A1;
  margin: 0 0 0.5rem 0;
  font-weight: 600;
}

.section-desc {
  font-size: 1.1rem;
  color: #1976D2;
  margin: 0;
  opacity: 0.8;
}

.case-category {
  margin-bottom: 2.5rem;
}

.category-header {
  display: flex;
  align-items: center;
  margin-bottom: 1.25rem;
  gap: 0.75rem;
}

.category-icon {
  width: 3rem;
  height: 3rem;
  font-size: 1.5rem;
  color: #1976D2;
  background: rgba(25, 118, 210, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.category-icon .el-icon {
  font-size: 1.5rem;
}

.category-title {
  font-size: 1.4rem;
  color: #0D47A1;
  margin: 0;
  font-weight: 600;
}

.cases-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

.case-card {
  background: white;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  display: flex;
  flex-direction: column;
}

.case-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(25, 118, 210, 0.15);
}

.case-content {
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1.25rem;
  flex: 1;
}

.case-logo {
  width: 80px;
  height: 80px;
  border-radius: 0.75rem;
  overflow: hidden;
  flex-shrink: 0;
  border: 1px solid rgba(25, 118, 210, 0.1);
}

.logo-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.case-info {
  flex: 1;
  position: relative;
}

.case-name {
  font-size: 1.25rem;
  color: #0D47A1;
  margin: 0 0 0.5rem 0;
  font-weight: 600;
}

.case-desc {
  font-size: 0.95rem;
  color: #555;
  margin: 0;
  line-height: 1.5;
}

.case-actions {
  display: flex;
  gap: 0.75rem;
  padding: 0 1.5rem 1.5rem 1.5rem;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  border-radius: 0.5rem;
  padding: 0.6rem 1rem;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.link-btn {
  background: linear-gradient(135deg, #0D47A1, #2196F3);
  border: none;
  color: white;
}

.link-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
}

/* 媒体查询 - 平板和桌面端 */
@media (min-width: 768px) {
  .content {
    padding-left: 2rem;
    padding-right: 2rem;
  }
  
  .cases-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .case-logo {
    width: 100px;
    height: 100px;
  }
  
  .case-name {
    font-size: 1.4rem;
  }
  
  .case-desc {
    font-size: 1rem;
  }
  
  .action-btn {
    font-size: 1rem;
    padding: 0.75rem 1.25rem;
  }
}

/* 媒体查询 - 大屏幕 */
@media (min-width: 1024px) {
  .cases-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
</style> 