<script setup lang="ts">
import { useRouter } from 'vue-router'
import { ArrowLeft, Location, Phone, Star, Link } from '@element-plus/icons-vue'
import { onMounted, ref } from 'vue'

const router = useRouter()

const goBack = () => {
  router.push('/card/shida-erbao-tuoyu')
}

// 复制电话号码到剪贴板
const copyPhone = () => {
  navigator.clipboard.writeText('0793-8221234').then(() => {
    showCopyMessage()
  })
}

// 显示复制成功提示
const isCopied = ref(false)
const showCopyMessage = () => {
  isCopied.value = true
  setTimeout(() => {
    isCopied.value = false
  }, 2000)
}

onMounted(() => {
  document.title = '联系我们 - 师大儿保托育中心'
})
</script>

<template>
  <div class="view-container">
    <div class="header">
      <el-button type="text" @click="goBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <h1>联系我们</h1>
    </div>

    <div class="content">
      <!-- 地址部分 -->
      <div class="section address-section">
        <div class="section-header">
          <el-icon class="section-icon"><Location /></el-icon>
          <h2 class="section-title">地址</h2>
        </div>
        <div class="address-content">
          <p class="address-text">江西省上饶市信州区滨江西路19号新儿保中心二楼</p>
        </div>
      </div>
      
      <!-- 电话部分 -->
      <div class="section phone-section">
        <div class="section-header">
          <el-icon class="section-icon"><Phone /></el-icon>
          <h2 class="section-title">电话</h2>
        </div>
        <div class="phone-content">
          <div class="phone-number">0793-8221234</div>
          <div class="phone-actions">
            <a :href="'tel:0793-8221234'" class="action-button call-button">
              拨打电话
            </a>
            <button class="action-button copy-button" @click="copyPhone">
              复制号码
              <span class="copy-message" :class="{ 'show': isCopied }">已复制</span>
            </button>
          </div>
        </div>
      </div>
      
      <!-- 试课部分 -->
      <div class="section trial-section">
        <div class="section-header">
          <el-icon class="section-icon"><Star /></el-icon>
          <h2 class="section-title">试课</h2>
        </div>
        <div class="trial-content">
          <p class="trial-description">
            提供试课，需提前转发朋友圈并预约时间。
          </p>
          <div class="trial-button-container">
            <a :href="'tel:0793-8221234'" class="trial-button">
              立即预约
            </a>
          </div>
        </div>
      </div>
      
      <!-- 二维码部分 -->
      <div class="section qrcode-section">
        <div class="section-header">
          <el-icon class="section-icon"><Link /></el-icon>
          <h2 class="section-title">关注我们</h2>
        </div>
        <div class="qrcode-content">
          <div class="qrcode-container">
            <img 
              src="https://pic.sdtaa.com/JiaoYu/Picture/JiaoYuJiGou/SDEBTYZX-erweima.png" 
              alt="师大儿保托育中心二维码" 
              class="qrcode-image"
            />
            <p class="qrcode-hint">长按图片识别二维码，了解更多</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8faf3;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  min-width: 100vw;
  box-sizing: border-box;
}

.header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, #5dae57, #7fc379);
  color: white;
  height: 3rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 100;
  box-sizing: border-box;
}

.back-button {
  color: white;
  margin-right: 1rem;
  font-size: 1.25rem;
  padding: 0.5rem;
  position: absolute;
  left: 0.5rem;
  z-index: 1;
}

.header h1 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  width: 100%;
  text-align: center;
}

.content {
  flex: 1;
  padding-top: 4.5rem;
  padding-bottom: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
  padding-left: 1rem;
  padding-right: 1rem;
}

/* 通用部分样式 */
.section {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
}

.section-icon {
  font-size: 1.5rem;
  color: #5dae57;
  margin-right: 0.8rem;
}

.section-title {
  color: #5dae57;
  font-size: 1.3rem;
  margin: 0;
  position: relative;
}

/* 地址部分样式 */
.address-text {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #444;
  margin: 0;
  padding: 0.5rem 0;
}

/* 电话部分样式 */
.phone-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.phone-number {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1.2rem;
}

.phone-actions {
  display: flex;
  gap: 1rem;
  width: 100%;
  max-width: 400px;
}

.action-button {
  flex: 1;
  background: rgba(93, 174, 87, 0.1);
  color: #5dae57;
  border: 1px solid rgba(93, 174, 87, 0.2);
  border-radius: 2rem;
  padding: 0.6rem 0.8rem;
  font-size: 0.9rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.action-button:hover {
  background: rgba(93, 174, 87, 0.2);
  transform: translateY(-2px);
}

.call-button {
  background: linear-gradient(135deg, #5dae57, #7fc379);
  color: white;
}

.copy-button {
  background: white;
  border: 1px solid #5dae57;
}

.copy-message {
  position: absolute;
  top: -30px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 0.3rem 0.8rem;
  border-radius: 1rem;
  font-size: 0.8rem;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.copy-message.show {
  opacity: 1;
}

/* 试课部分样式 */
.trial-description {
  font-size: 1rem;
  line-height: 1.6;
  color: #444;
  margin-top: 0;
  margin-bottom: 1.5rem;
  text-align: center;
}

.trial-button-container {
  display: flex;
  justify-content: center;
}

.trial-button {
  background: linear-gradient(135deg, #5dae57, #7fc379);
  color: white;
  border: none;
  border-radius: 2rem;
  padding: 0.8rem 2rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(93, 174, 87, 0.2);
  text-decoration: none;
  display: inline-block;
}

.trial-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(93, 174, 87, 0.3);
}

/* 二维码部分样式 */
.qrcode-content {
  display: flex;
  justify-content: center;
  align-items: center;
}

.qrcode-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.qrcode-image {
  width: 200px;
  height: 200px;
  object-fit: contain;
  border-radius: 0.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  margin-bottom: 1rem;
  transition: transform 0.3s ease;
}

.qrcode-image:hover {
  transform: scale(1.03);
}

.qrcode-hint {
  color: #666;
  font-size: 0.9rem;
  margin: 0;
}

@media (min-width: 768px) {
  .content {
    padding-top: 5rem;
    padding-left: 2rem;
    padding-right: 2rem;
  }
  
  .phone-content {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
  
  .phone-number {
    margin-bottom: 0;
  }
  
  .phone-actions {
    max-width: 300px;
  }
}

@media (min-width: 1024px) {
  .section {
    padding: 2rem;
  }
}
</style> 