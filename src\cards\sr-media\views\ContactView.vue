<script setup lang="ts">
import { useRouter } from 'vue-router'
import { ref } from 'vue'
import {
  ArrowLeft,
  Location,
  User,
  Phone,
  Message,
  CopyDocument,
  VideoCamera,
  ChatDotRound
} from '@element-plus/icons-vue'
import TabBar from '../components/TabBar.vue'
import { ElMessage } from 'element-plus'

const router = useRouter()

const goBack = () => {
  router.push('/card/sr-media')
}

// 联系信息
const contactInfo = ref({
  address: '江西省上饶市信州区广信大道96号',
  contact: '余雪婷',
  phone: '13677036399',
  email: '<EMAIL>'
})

// 复制功能
const copyToClipboard = async (text: string, type: string) => {
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success(`${type}已复制到剪贴板`)
  } catch (err) {
    ElMessage.error('复制失败')
  }
}

// 拨打电话功能
const makeCall = (phone: string) => {
  window.location.href = `tel:${phone}`
}
</script>

<template>
  <div class="view-container">
    <div class="header">
      <el-button type="text" @click="goBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <h1>联系我们</h1>
    </div>

    <div class="content">
      <!-- 二维码区域 -->
      <div class="qrcode-section">
        <div class="section-title">
          <h2>关注我们</h2>
        </div>
        <div class="qrcode-container">
          <div class="qrcode-item">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ShangRaoChuanMei/srcm-sph.png" alt="视频号二维码" class="qrcode-image">
            <div class="qrcode-label">
              <el-icon class="qrcode-icon"><VideoCamera /></el-icon>
              <span>视频号</span>
            </div>
          </div>
          <div class="qrcode-item">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ShangRaoChuanMei/srcm-gzh.png" alt="公众号二维码" class="qrcode-image">
            <div class="qrcode-label">
              <el-icon class="qrcode-icon"><ChatDotRound /></el-icon>
              <span>公众号</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 联系信息区域 -->
      <div class="contact-section">
        <div class="section-title">
          <h2>联系信息</h2>
        </div>
        <div class="contact-info">
          <!-- 地址信息 -->
          <div class="contact-item">
            <div class="contact-main">
              <div class="contact-icon-wrapper">
                <el-icon class="contact-icon"><Location /></el-icon>
              </div>
              <div class="contact-details">
                <div class="contact-label">地址</div>
                <div class="contact-value">{{ contactInfo.address }}</div>
              </div>
            </div>
          </div>

          <!-- 联系人信息 -->
          <div class="contact-item">
            <div class="contact-main">
              <div class="contact-icon-wrapper">
                <el-icon class="contact-icon"><User /></el-icon>
              </div>
              <div class="contact-details">
                <div class="contact-label">联系人</div>
                <div class="contact-value">{{ contactInfo.contact }}</div>
              </div>
            </div>
          </div>

          <!-- 电话信息 -->
          <div class="contact-item">
            <div class="contact-main">
              <div class="contact-icon-wrapper">
                <el-icon class="contact-icon"><Phone /></el-icon>
              </div>
              <div class="contact-details">
                <div class="contact-label">电话</div>
                <div class="contact-value">{{ contactInfo.phone }}</div>
              </div>
            </div>
            <div class="contact-actions">
              <el-button
                type="primary"
                size="small"
                @click="copyToClipboard(contactInfo.phone, '电话号码')"
                class="action-btn"
              >
                <el-icon><CopyDocument /></el-icon>
                复制
              </el-button>
              <el-button
                type="success"
                size="small"
                @click="makeCall(contactInfo.phone)"
                class="action-btn"
              >
                <el-icon><Phone /></el-icon>
                拨打
              </el-button>
            </div>
          </div>

          <!-- 邮箱信息 -->
          <div class="contact-item">
            <div class="contact-main">
              <div class="contact-icon-wrapper">
                <el-icon class="contact-icon"><Message /></el-icon>
              </div>
              <div class="contact-details">
                <div class="contact-label">邮箱</div>
                <div class="contact-value">{{ contactInfo.email }}</div>
              </div>
            </div>
            <div class="contact-actions">
              <el-button
                type="primary"
                size="small"
                @click="copyToClipboard(contactInfo.email, '邮箱地址')"
                class="action-btn"
              >
                <el-icon><CopyDocument /></el-icon>
                复制
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <TabBar />
  </div>
</template>

<style scoped>
.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8fafc;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  min-width: 100vw;
  box-sizing: border-box;
}

.header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, #3b82f6, #60a5fa);
  color: white;
  height: 3rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 100;
  box-sizing: border-box;
}

.back-button {
  color: white;
  margin-right: 1rem;
  font-size: 1.25rem;
  padding: 0.5rem;
  position: absolute;
  left: 0.5rem;
  z-index: 1;
}

.header h1 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  width: 100%;
  text-align: center;
}

.content {
  flex: 1;
  padding-top: 4.5rem;
  padding-bottom: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
  padding-left: 1rem;
  padding-right: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* 通用区域样式 */
.qrcode-section,
.contact-section {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
}

.section-title {
  text-align: center;
  margin-bottom: 1.5rem;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid #f1f5f9;
}

.section-title h2 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
}

/* 二维码区域样式 */
.qrcode-container {
  display: flex;
  justify-content: center;
  gap: 2rem;
  flex-wrap: wrap;
}

.qrcode-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  background: linear-gradient(135deg, #f8fafc, #f1f5f9);
  border-radius: 1rem;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
  min-width: 140px;
}

.qrcode-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
}

.qrcode-image {
  width: 120px;
  height: 120px;
  border-radius: 0.5rem;
  margin-bottom: 0.75rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.qrcode-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #3b82f6;
  font-weight: 600;
  font-size: 0.9rem;
}

.qrcode-icon {
  font-size: 1.1rem;
}

/* 联系信息区域样式 */
.contact-info {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.contact-item {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 0.75rem;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
  gap: 0.75rem;
}

.contact-main {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 1rem;
}

.contact-item:hover {
  background: #f1f5f9;
  border-color: #3b82f6;
}

.contact-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  background: linear-gradient(135deg, #3b82f6, #60a5fa);
  border-radius: 50%;
  margin-right: 1rem;
  flex-shrink: 0;
}

.contact-icon {
  font-size: 1.1rem;
  color: white;
}

.contact-details {
  flex: 1;
  min-width: 0;
}

.contact-label {
  font-size: 0.85rem;
  color: #64748b;
  margin-bottom: 0.25rem;
  font-weight: 500;
}

.contact-value {
  font-size: 0.95rem;
  color: #1e293b;
  font-weight: 500;
  word-break: break-all;
}

.contact-actions {
  display: flex;
  gap: 0.5rem;
  margin-left: 1rem;
  flex-shrink: 0;
}

.action-btn {
  font-size: 0.8rem;
  padding: 0.4rem 0.8rem;
  height: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .content {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
    gap: 1rem;
  }

  .qrcode-section,
  .contact-section {
    padding: 1rem;
  }

  .qrcode-container {
    gap: 1rem;
    justify-content: space-around;
  }

  .qrcode-item {
    min-width: 120px;
    padding: 0.75rem;
  }

  .qrcode-image {
    width: 100px;
    height: 100px;
  }

  .qrcode-label {
    font-size: 0.8rem;
  }

  .contact-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .contact-main {
    display: flex;
    align-items: center;
    width: 100%;
    gap: 0.75rem;
  }

  .contact-icon-wrapper {
    width: 2rem;
    height: 2rem;
    margin-right: 0;
  }

  .contact-icon {
    font-size: 1rem;
  }

  .contact-details {
    flex: 1;
    min-width: 0;
  }

  .contact-label {
    display: inline;
    margin-right: 0.5rem;
  }

  .contact-value {
    display: inline;
    font-size: 0.9rem;
  }

  .contact-actions {
    margin-left: 2.75rem;
    flex-shrink: 0;
    width: calc(100% - 2.75rem);
  }

  .action-btn {
    font-size: 0.75rem;
    padding: 0.35rem 0.7rem;
  }

  .section-title h2 {
    font-size: 1.1rem;
  }
}

@media (min-width: 768px) {
  .content {
    padding-bottom: 5rem; /* 桌面端保留底部导航栏空间 */
  }
}

@media (min-width: 769px) {
  .qrcode-container {
    gap: 3rem;
  }

  .qrcode-image {
    width: 140px;
    height: 140px;
  }

  .qrcode-item {
    min-width: 160px;
    padding: 1.25rem;
  }

  .contact-item {
    flex-direction: row;
    align-items: center;
    gap: 1rem;
  }

  .contact-main {
    flex: 1;
  }

  .contact-actions {
    margin-left: 1rem;
    width: auto;
    flex-direction: row;
  }
}

@media (min-width: 1200px) {
  .qrcode-container {
    gap: 4rem;
  }

  .contact-info {
    gap: 1.5rem;
  }

  .contact-item {
    padding: 1.25rem;
  }
}
</style>
