<script setup lang="ts">
import { useRouter } from 'vue-router'
import {
  ArrowLeft,
  VideoPlay,
  OfficeBuilding,
  User,
  Trophy,
  Aim,
  Star,
  Location,
  Document,
  Service
} from '@element-plus/icons-vue'
import TabBar from '../components/TabBar.vue'

const router = useRouter()

const goBack = () => {
  router.push('/card/sr-media')
}

</script>

<template>
  <div class="view-container">
    <div class="header">
      <el-button type="text" @click="goBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <h1>集团简介</h1>
    </div>

    <div class="content">
      <!-- 宣传视频区域 -->
      <div class="video-section">
        <div class="section-header">
          <el-icon class="section-icon"><VideoPlay /></el-icon>
          <h2>宣传视频</h2>
        </div>
        <div class="video-container">
          <video
            controls
            poster="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ShangRaoChuanMei/srcm-xcspfm.png"
            class="promo-video"
          >
            <source src="https://pic.sdtaa.com/ZhiLian/Video/Enterprise/ShangRaoChuanMei/xuanchuanshipin.mp4" type="video/mp4">
            您的浏览器不支持视频播放。
          </video>
        </div>
      </div>

      <!-- 集团简介区域 -->
      <div class="intro-section">
        <div class="section-header">
          <el-icon class="section-icon"><OfficeBuilding /></el-icon>
          <h2>集团简介</h2>
        </div>
        <div class="intro-content">
          <p>上饶传媒集团成立于 2023年9月，是上饶市委市政府管理的国有一类企业,也是上饶传媒行业的领军企业。集团整合了上饶日报社、上饶市广播电视台的经营性资产，汇聚了传媒行业精英，拥有丰富的媒体资源和广泛的市场影响力。</p>
          <p>集团大力发展以"政务服务、广告运营、影视制作、数字经济、活动策划、文化演艺"为主的六大核心业务，充分发挥报纸、广播、电视、新媒体、客户端等全媒体矩阵优势，积极推进内容、渠道、平台、经营、管理等方面深度融合和一体化发展，秉持优质、专业、高效的理念，致力于为客户提供全方位、个性化的传媒服务，着力打造拥有强大实力的地方旗舰新型传媒集团。</p>
        </div>
      </div>

      <!-- 企业文化区域 -->
      <div class="culture-section">
        <div class="culture-grid">
          <div class="culture-item">
            <div class="culture-header">
              <el-icon class="culture-icon"><Aim /></el-icon>
              <h3>企业愿景</h3>
            </div>
            <p>打造全国知名、全省一流、地方旗舰新型传媒集团</p>
          </div>
          <div class="culture-item">
            <div class="culture-header">
              <el-icon class="culture-icon"><Star /></el-icon>
              <h3>企业精神</h3>
            </div>
            <p>有口碑、有情怀、有品质、有效益</p>
          </div>
          <div class="culture-item">
            <div class="culture-header">
              <el-icon class="culture-icon"><Trophy /></el-icon>
              <h3>企业宗旨</h3>
            </div>
            <p>秉持优质、专业、高效理念致力于提供一站式、全方位、个性化的传媒服务。</p>
          </div>
          <div class="culture-item">
            <div class="culture-header">
              <el-icon class="culture-icon"><Location /></el-icon>
              <h3>发展定位</h3>
            </div>
            <p>品牌化、数智化、多元化</p>
          </div>
        </div>
      </div>

      <!-- 董事长致辞区域 -->
      <div class="message-section">
        <div class="section-header">
          <el-icon class="section-icon"><Document /></el-icon>
          <h2>董事长致辞</h2>
        </div>
        <div class="message-content">
          <h3>建设地方旗舰新型传媒集团 稳健笃行</h3>
          <h4>上饶传媒集团党委书记、董事长吴汉2025新年致辞</h4>
          <div class="message-text">
            <p>上饶传媒集团作为一家现代化国有文化企业集团，始终坚守初心、锐意进取，秉持"科技+文化+融合+创新"的企业定位，聚焦助力文化中心建设、助力建设贯彻落实习近平文化思想的首善之区的职责使命，着力打造新质生产力，倾力提升企业核心竞争力，坚持守正创新、大力发展以"政务服务、广告运营、影视制作、数字经济、活动策划、文化演艺"为主的六大核心业务，充分发挥报纸、广播、电视、新媒体、客户端等全媒体矩阵优势，积极推进内容、渠道、平台、经营、管理等方面深度融合和一体化发展，秉持优质、专业、高效的理念，致力于为客户提供全方位、个性化的传媒服务，持续提升品牌形象力与社会影响力，推动实现将社会效益摆在首位，不断追求更高质量的"双效统一"的拥有强大实力的地方旗舰新型传媒集团。</p>
            <p>在砥砺前行的道路上，上饶传媒集团孜孜不倦坚守以实干精神凝聚发展之力，以赤子之心肩负社会担当，只争朝夕，不负韶华。我们深知，每一次成功、每一步奋进都离不开上传人的拼搏奋斗和无私奉献，离不开合作伙伴的鼎力相助和肝胆相照，更离不开社会各界朋友的真诚信赖和深情厚谊。在此谨致以诚挚的感谢和崇高的敬意！我们将深深铭记每一份关爱、信任和支持，将其化作未来阔步前行的不竭动力。</p>
            <p>当今世界正处于百年未有之大变局，中国特色社会主义展现出蓬勃旺盛的生机与活力，文化繁荣兴盛的动力强劲、机遇无限、盛景可期。面对新征程新挑战，上饶传媒集团将一如既往坚守初心，坚定信心，接续奋斗，抓住"文化+科技"深度融合发展的历史机遇，深化改革、加快转型，努力探索具有上传特色的合作共赢发展之路，切实担负起新时代文化使命，为助力谱写中国式现代化上饶篇章贡献上传力量！</p>
          </div>
        </div>
      </div>

      <!-- 组织机构区域 -->
      <div class="organization-section">
        <div class="section-header">
          <el-icon class="section-icon"><User /></el-icon>
          <h2>组织机构</h2>
        </div>
        <div class="organization-content">
          <div class="leadership-section">
            <h3>集团领导</h3>
            <div class="leadership-grid">
              <div class="leader-item">
                <span class="leader-title">党委书记 董事长</span>
                <span class="leader-name">吴汉</span>
              </div>
              <div class="leader-item">
                <span class="leader-title">党委副书记 总经理</span>
                <span class="leader-name">李华</span>
              </div>
              <div class="leader-item">
                <span class="leader-title">副总经理</span>
                <span class="leader-name">高凌</span>
              </div>
              <div class="leader-item">
                <span class="leader-title">副总经理</span>
                <span class="leader-name">胡绍斌</span>
              </div>
            </div>
          </div>

          <div class="departments-section">
            <h3>集团部门</h3>
            <div class="departments-grid">
              <div class="department-item">创意策划部</div>
              <div class="department-item">综合管理部</div>
              <div class="department-item">合规审计部</div>
              <div class="department-item">组织人事部</div>
              <div class="department-item">工会/宣传部</div>
              <div class="department-item">财务中心</div>
              <div class="department-item">战略投资部</div>
              <div class="department-item">经营管理部</div>
              <div class="department-item">技术保障部</div>
            </div>
          </div>

          <div class="subsidiaries-section">
            <h3>下属公司</h3>
            <div class="subsidiaries-grid">
              <div class="subsidiary-item">上饶市传媒集团数字科技有限公司</div>
              <div class="subsidiary-item">上饶市传媒集团影视有限公司</div>
              <div class="subsidiary-item">上饶市传媒集团文化产业有限公司</div>
              <div class="subsidiary-item">上饶市传媒集团演艺有限公司</div>
              <div class="subsidiary-item">上饶市传媒集团融媒体发展有限公司</div>
              <div class="subsidiary-item">上饶市传媒集团广告有限公司</div>
              <div class="subsidiary-item">上饶广播新视听传媒有限公司</div>
              <div class="subsidiary-item">上饶报媒新视线传媒有限公司</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 核心业务区域 -->
      <div class="business-section">
        <div class="section-header">
          <el-icon class="section-icon"><Service /></el-icon>
          <h2>六大核心业务</h2>
        </div>
        <div class="business-grid">
          <div class="business-item">政务服务</div>
          <div class="business-item">广告运营</div>
          <div class="business-item">影视制作</div>
          <div class="business-item">数字经济</div>
          <div class="business-item">活动策划</div>
          <div class="business-item">文化演艺</div>
        </div>
      </div>
    </div>

    <TabBar />
  </div>
</template>

<style scoped>
.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8fafc;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  min-width: 100vw;
  box-sizing: border-box;
}

.header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, #3b82f6, #60a5fa);
  color: white;
  height: 3rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 100;
  box-sizing: border-box;
}

.back-button {
  color: white;
  margin-right: 1rem;
  font-size: 1.25rem;
  padding: 0.5rem;
  position: absolute;
  left: 0.5rem;
  z-index: 1;
}

.header h1 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  width: 100%;
  text-align: center;
}

.content {
  flex: 1;
  padding-top: 4.5rem;
  padding-bottom: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
  padding-left: 1rem;
  padding-right: 1rem;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* 通用区域样式 */
.video-section,
.intro-section,
.culture-section,
.message-section,
.organization-section,
.business-section {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
}

/* 区域标题样式 */
.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid #f1f5f9;
}

.section-icon {
  font-size: 1.5rem;
  color: #3b82f6cc;
  margin-right: 0.75rem;
}

.section-header h2 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
}

/* 视频区域样式 */
.video-container {
  display: flex;
  justify-content: center;
}

.promo-video {
  width: 100%;
  max-width: 800px;
  height: auto;
  border-radius: 0.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* 简介内容样式 */
.intro-content p {
  line-height: 1.8;
  color: #475569;
  margin-bottom: 1rem;
  text-align: justify;
}

.intro-content p:last-child {
  margin-bottom: 0;
}

/* 企业文化区域样式 */
.culture-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.culture-item {
  padding: 1.25rem;
  background: linear-gradient(135deg, #f8fafc, #f1f5f9);
  border-radius: 0.75rem;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.culture-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
}

.culture-header {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
}

.culture-icon {
  font-size: 1.25rem;
  color: #3b82f6cc;
  margin-right: 0.5rem;
}

.culture-header h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
}

.culture-item p {
  margin: 0;
  color: #475569;
  line-height: 1.6;
  font-size: 0.9rem;
}

/* 董事长致辞样式 */
.message-content h3 {
  color: #1e293b;
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  text-align: center;
}

.message-content h4 {
  color: #64748b;
  font-size: 0.95rem;
  font-weight: 500;
  margin-bottom: 1.5rem;
  text-align: center;
}

.message-text p {
  line-height: 1.8;
  color: #475569;
  margin-bottom: 1rem;
  text-align: justify;
  text-indent: 2em;
}

.message-text p:last-child {
  margin-bottom: 0;
}

/* 组织机构样式 */
.organization-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.leadership-section h3,
.departments-section h3,
.subsidiaries-section h3 {
  color: #1e293b;
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.leadership-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.leader-item {
  display: flex;
  flex-direction: column;
  padding: 1rem;
  background: linear-gradient(135deg, #3b82f6cc, #60a5facc);
  color: white;
  border-radius: 0.5rem;
  text-align: center;
}

.leader-title {
  font-size: 0.85rem;
  opacity: 0.9;
  margin-bottom: 0.25rem;
}

.leader-name {
  font-size: 1rem;
  font-weight: 600;
}

.departments-grid,
.subsidiaries-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 0.75rem;
}

.department-item,
.subsidiary-item {
  padding: 0.75rem 1rem;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  color: #475569;
  font-size: 0.9rem;
  text-align: center;
  transition: all 0.3s ease;
}

.department-item:hover,
.subsidiary-item:hover {
  background: #3b82f6cc;
  color: white;
  transform: translateY(-1px);
}

/* 核心业务样式 */
.business-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
}

.business-item {
  padding: 1.25rem;
  background: linear-gradient(135deg, #f8fafc, #f1f5f9);
  border: 2px solid #3b82f6cc;
  border-radius: 0.75rem;
  color: #3b82f6cc;
  font-weight: 600;
  text-align: center;
  transition: all 0.3s ease;
}

.business-item:hover {
  background: linear-gradient(135deg, #3b82f6cc, #60a5facc);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.25);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .content {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
    gap: 1.5rem;
  }

  .video-section,
  .intro-section,
  .culture-section,
  .message-section,
  .organization-section,
  .business-section {
    padding: 1rem;
  }

  .culture-grid {
    grid-template-columns: 1fr;
  }

  .leadership-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .departments-grid,
  .subsidiaries-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .business-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .section-header h2 {
    font-size: 1.1rem;
  }

  .section-icon {
    font-size: 1.25rem;
  }
}

@media (min-width: 768px) {
  .content {
    padding-bottom: 5rem; /* 桌面端保留底部导航栏空间 */
  }
}

@media (min-width: 1200px) {
  .culture-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .leadership-grid {
    grid-template-columns: repeat(4, 1fr);
  }

  .departments-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .subsidiaries-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .business-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
</style>
