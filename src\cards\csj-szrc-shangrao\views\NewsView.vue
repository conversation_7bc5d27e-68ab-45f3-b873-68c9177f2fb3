<script setup lang="ts">
import { useRouter } from 'vue-router'
import { ArrowLeft, School, User, Briefcase, Promotion, Calendar, Trophy } from '@element-plus/icons-vue'
import { ref, onMounted, reactive } from 'vue'

const router = useRouter()

const goBack = () => {
  router.push('/card/csj-szrc-shangrao')
}

onMounted(() => {
  document.title = '长三角数字人才上饶创新基地 - 基地动态'
})

// 定义类型
interface SubActivity {
  name: string;
  images: string[];
}

interface Activity {
  id: number;
  title: string;
  description: string;
  subActivities: SubActivity[];
}

// 活动数据
const activities = reactive<Activity[]>([
  {
    id: 1,
    title: '企业AI赋能',
    description: '已为广丰高新区管委会、上饶市湖南商会、宇瞳光学等20余家企事业单位提供人工智能客服及应用系列讲座。',
    subActivities: [
      {
        name: '广丰高新区管委会宣讲',
        images: [
          'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/RenCaiJiDi/GuangFengGWH1.jpg',
          'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/RenCaiJiDi/GuangFengGWH2.jpg',
          'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/RenCaiJiDi/GuangFengGWH3.jpg'
        ]
      },
      {
        name: '上饶市湖南商会宣讲',
        images: [
          'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/RenCaiJiDi/SRHuanNanShangHui1.jpg',
          'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/RenCaiJiDi/SRHuanNanShangHui2.jpg',
          'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/RenCaiJiDi/SRHuanNanShangHui3.jpg'
        ]
      },
      {
        name: '江西鸿邦建设工程有限公司宣讲',
        images: [
          'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/RenCaiJiDi/JXHongBangJianShe1.jpg',
          'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/RenCaiJiDi/JXHongBangJianShe2.jpg',
          'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/RenCaiJiDi/JXHongBangJianShe3.jpg'
        ]
      },
      {
        name: '上饶青年企业家协会宣讲',
        images: [
          'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/RenCaiJiDi/QingQiXie1.jpg',
          'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/RenCaiJiDi/QingQiXie2.jpg',
          'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/RenCaiJiDi/QingQiXie3.jpg'
        ]
      },
      {
        name: '万力时代宣讲',
        images: [
          'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/RenCaiJiDi/WanLiShiDai1.jpg',
          'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/RenCaiJiDi/WanLiShiDai2.jpg',
          'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/RenCaiJiDi/WanLiShiDai3.jpg'
        ]
      },
      {
        name: '兴物城建宣讲',
        images: [
          'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/RenCaiJiDi/XingWuChengJian.png'
        ]
      },
      {
        name: '宇瞳光学宣讲',
        images: [
          'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/RenCaiJiDi/YuTongGuangXue1.jpg',
          'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/RenCaiJiDi/YuTongGuangXue2.jpg',
          'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/RenCaiJiDi/YuTongGuangXue3.jpg'
        ]
      }
    ]
  },
  {
    id: 2,
    title: '高校精英育才',
    description: '与上饶师范学院合作，成功举办五期人工智能训练营。',
    subActivities: [
      {
        name: '第一期',
        images: [
          'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/RenCaiJiDi/XunLianYing/XunLianYiQi1.JPG',
          'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/RenCaiJiDi/XunLianYing/XunLianYiQi2.JPG',
          'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/RenCaiJiDi/XunLianYing/XunLianYiQi3.JPG'
        ]
      },
      {
        name: '第二期',
        images: [
          'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/RenCaiJiDi/XunLianYing/XunLianErQi1.JPG',
          'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/RenCaiJiDi/XunLianYing/XunLianErQi2.JPG',
          'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/RenCaiJiDi/XunLianYing/XunLianErQi3.JPG'
        ]
      },
      {
        name: '第三期',
        images: [
          'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/RenCaiJiDi/XunLianYing/XunLianSanQi1.JPG',
          'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/RenCaiJiDi/XunLianYing/XunLianSanQi2.JPG',
          'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/RenCaiJiDi/XunLianYing/XunLianSanQi3.JPG'
        ]
      },
      {
        name: '第四期',
        images: [
          'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/RenCaiJiDi/XunLianYing/XunLianSiQi1.JPG',
          'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/RenCaiJiDi/XunLianYing/XunLianSiQi2.JPG',
          'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/RenCaiJiDi/XunLianYing/XunLianSiQi3.JPG'
        ]
      },
      {
        name: '第五期',
        images: [
          'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/RenCaiJiDi/XunLianYing/XunLianWuQi1.JPG',
          'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/RenCaiJiDi/XunLianYing/XunLianWuQi2.JPG',
          'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/RenCaiJiDi/XunLianYing/XunLianWuQi3.JPG'
        ]
      },
      {
        name: '提高班',
        images: [
          'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/RenCaiJiDi/XunLianYing/TiGaoBan1.png',
          'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/RenCaiJiDi/XunLianYing/TiGaoBan2.png',
          'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/RenCaiJiDi/XunLianYing/TiGaoBan3.png'
        ]
      }
    ]
  },
  {
    id: 3,
    title: '适用性人才培养',
    description: '联合举办上饶市数字经济适用型人才培训（每年持续），"理论+实践"打通入职"最后一公里"。',
    subActivities: [
      {
        name: '2024上饶市数字经济适用型人才培训',
        images: [
          'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/RenCaiJiDi/ShiYongXingRenCai/PeiXunBan1.JPG',
          'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/RenCaiJiDi/ShiYongXingRenCai/PeiXunBan2.JPG',
          'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/RenCaiJiDi/ShiYongXingRenCai/PeiXunBan3.JPG'
        ]
      },
      {
        name: '2025上饶市数字经济适用性人才培训',
        images: [
          'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/RenCaiJiDi/ShiYongXingRenCai/PeiXunBan4.jpg',
          'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/RenCaiJiDi/ShiYongXingRenCai/PeiXunBan5.jpg',
          'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/RenCaiJiDi/ShiYongXingRenCai/PeiXunBan6.jpg',
          'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/RenCaiJiDi/ShiYongXingRenCai/PeiXunBan7.jpg'
        ]
      }
    ]
  },
  {
    id: 4,
    title: '企业高管培训班',
    description: '在市数据局指导下，成功举办两期上饶市企业高管人工智能应用专题培训班（每年持续）。',
    subActivities: [
      {
        name: '2024上饶市企业高管人工智能应用专题培训班',
        images: [
          'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/RenCaiJiDi/GaoGuanBan/2024GGB1.jpg',
          'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/RenCaiJiDi/GaoGuanBan/2024GGB2.jpg',
          'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/RenCaiJiDi/GaoGuanBan/2024GGB3.jpg'
        ]
      },
      {
        name: '2025上饶市企业高管人工智能应用专题培训班',
        images: [
          'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/RenCaiJiDi/GaoGuanBan/2025GGB1.jpg',
          'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/RenCaiJiDi/GaoGuanBan/2025GGB2.jpg',
          'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/RenCaiJiDi/GaoGuanBan/2025GGB3.jpg'
        ]
      }
    ]
  }
])

// 近期规划与重点工作
const plans = [
  {
    title: '教育示范',
    icon: School,
    content: '打造人工智能教育示范区，开展"AI进校园"，建科普基地，办高校AI应用竞赛。'
  },
  {
    title: '品牌活动',
    icon: Calendar,
    content: '举办全国性第二届人工智能与科创教育大会，举办数字经济高质量论坛/峰会。'
  },
  {
    title: '人才目标',
    icon: User,
    content: '全年计划招生培训500人，择优输送200名合格人才进入本地数字经济领域。'
  },
  {
    title: '持续赋能',
    icon: Promotion,
    content: '每季度为企业提供免费人才培训；联合开展公务员数字经济培训。'
  },
  {
    title: '生态构建',
    icon: Briefcase,
    content: '完善基地专家顾问及讲师团队；构建企业人才需求库；探索创新人才评价和流动机制；推动数字经济相关赛事落地。'
  }
]

// 预期成效展望
const expectations = [
  {
    title: '人才方面',
    content: '显著提升学员职业技能与就业竞争力，有效缓解区域人才缺口。'
  },
  {
    title: '产业方面',
    content: '促进数字经济产业发展与集聚，推动传统产业数字化转型，孵化新兴企业。'
  },
  {
    title: '经济方面',
    content: '带动区域经济增长，提升区域竞争力，增加优质就业岗位。'
  },
  {
    title: '社会方面',
    content: '推动长三角数字经济协同发展，提升全民数字素养，为区域一体化贡献力量。'
  }
]

// 控制当前查看的子活动
const currentActivity = ref<Activity | null>(null)
const currentSubActivity = ref<SubActivity | null>(null)
const showGallery = ref(false)
const currentImageIndex = ref(0)

const openGallery = (activity: Activity, subActivity: SubActivity, index = 0) => {
  currentActivity.value = activity
  currentSubActivity.value = subActivity
  currentImageIndex.value = index
  showGallery.value = true
}

const closeGallery = () => {
  showGallery.value = false
}

const nextImage = () => {
  if (currentSubActivity.value && currentSubActivity.value.images) {
    currentImageIndex.value = (currentImageIndex.value + 1) % currentSubActivity.value.images.length
  }
}

const prevImage = () => {
  if (currentSubActivity.value && currentSubActivity.value.images) {
    currentImageIndex.value = (currentImageIndex.value - 1 + currentSubActivity.value.images.length) % currentSubActivity.value.images.length
  }
}

// 控制活动详情的展开/收起
const expandedActivities = ref<number[]>([])

const toggleActivity = (activityId: number) => {
  const index = expandedActivities.value.indexOf(activityId)
  if (index === -1) {
    expandedActivities.value.push(activityId)
  } else {
    expandedActivities.value.splice(index, 1)
  }
}

const isActivityExpanded = (activityId: number) => {
  return expandedActivities.value.includes(activityId)
}

// 初始化时设置标题，但不展开任何活动
onMounted(() => {
  document.title = '长三角数字人才上饶创新基地 - 基地动态'
  // 不再自动展开第一个活动
})
</script>

<template>
  <div class="view-container">
    <div class="header">
      <el-button type="text" @click="goBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <h1>基地动态</h1>
    </div>

    <div class="content">
      <!-- 品牌培训活动部分 -->
      <section class="section">
        <div class="section-header">
          <h2>品牌培训活动<span class="status-badge">已开展</span></h2>
        </div>
        
        <div class="activities-container">
          <div v-for="activity in activities" :key="activity.id" class="activity-card">
            <div class="activity-header" @click="toggleActivity(activity.id)">
              <h3>{{ activity.title }}</h3>
              <div class="activity-controls">
                <span class="toggle-text">{{ isActivityExpanded(activity.id) ? '收起' : '展开' }}</span>
                <el-icon :class="['expand-icon', { 'expanded': isActivityExpanded(activity.id) }]">
                  <ArrowLeft />
                </el-icon>
              </div>
            </div>
            
            <div v-if="isActivityExpanded(activity.id)" class="activity-content">
              <p class="activity-description">{{ activity.description }}</p>
              
              <div class="sub-activities">
                <div v-for="(subActivity, index) in activity.subActivities" :key="index" class="sub-activity">
                  <h4 class="sub-activity-title">{{ subActivity.name }}</h4>
                  
                  <div class="image-grid">
                    <div 
                      v-for="(image, imgIndex) in subActivity.images" 
                      :key="imgIndex" 
                      class="image-item"
                      @click="openGallery(activity, subActivity, imgIndex)"
                    >
                      <img :src="image" :alt="subActivity.name + ' 图片 ' + (imgIndex + 1)" loading="lazy">
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      
      <!-- 近期规划与重点工作部分 -->
      <section class="section">
        <div class="section-header">
          <h2>近期规划与重点工作<span class="year-badge">2025年度</span></h2>
        </div>
        
        <div class="plans-container">
          <div v-for="(plan, index) in plans" :key="index" class="plan-card">
            <div class="plan-icon">
              <el-icon><component :is="plan.icon" /></el-icon>
            </div>
            <div class="plan-content">
              <h3>{{ plan.title }}</h3>
              <p>{{ plan.content }}</p>
            </div>
          </div>
        </div>
      </section>
      
      <!-- 预期成效展望部分 -->
      <section class="section">
        <div class="section-header">
          <h2>预期成效展望</h2>
        </div>
        
        <div class="expectations-container">
          <div v-for="(expectation, index) in expectations" :key="index" class="expectation-card">
            <div class="expectation-header">
              <el-icon><Trophy /></el-icon>
              <h3>{{ expectation.title }}</h3>
            </div>
            <p>{{ expectation.content }}</p>
          </div>
        </div>
      </section>
    </div>
    
    <!-- 图片查看器 -->
    <div v-if="showGallery" class="gallery-overlay" @click="closeGallery">
      <div class="gallery-container" @click.stop>
        <div class="gallery-header">
          <h3 v-if="currentSubActivity">{{ currentSubActivity.name }}</h3>
          <el-button type="text" @click="closeGallery" class="close-button">
            <el-icon><ArrowLeft /></el-icon>
          </el-button>
        </div>
        
        <div class="gallery-content">
          <div class="gallery-image-container">
            <img 
              v-if="currentSubActivity && currentSubActivity.images && currentSubActivity.images[currentImageIndex]" 
              :src="currentSubActivity.images[currentImageIndex]" 
              :alt="currentSubActivity.name + ' 图片'"
              class="gallery-image"
            >
          </div>
          
          <div class="gallery-navigation">
            <el-button v-if="currentSubActivity && currentSubActivity.images && currentSubActivity.images.length > 1" 
                      type="primary" 
                      circle 
                      @click.stop="prevImage" 
                      class="nav-button prev-button">
              <el-icon><ArrowLeft /></el-icon>
            </el-button>
            
            <div v-if="currentSubActivity && currentSubActivity.images" class="image-counter">
              {{ currentImageIndex + 1 }} / {{ currentSubActivity.images.length }}
            </div>
            
            <el-button v-if="currentSubActivity && currentSubActivity.images && currentSubActivity.images.length > 1" 
                      type="primary" 
                      circle 
                      @click.stop="nextImage" 
                      class="nav-button next-button">
              <el-icon class="rotated-icon"><ArrowLeft /></el-icon>
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f9ff;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  min-width: 100vw;
  box-sizing: border-box;
}

.header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, #0D47A1, #2196F3);
  color: white;
  height: 3rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 100;
  box-sizing: border-box;
}

.back-button {
  color: white;
  margin-right: 1rem;
  font-size: 1.25rem;
  padding: 0.5rem;
  position: absolute;
  left: 0.5rem;
  z-index: 1;
}

.header h1 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  width: 100%;
  text-align: center;
}

.content {
  flex: 1;
  padding-top: 4.5rem;
  padding-bottom: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
  padding-left: 1rem;
  padding-right: 1rem;
}

/* 通用部分样式 */
.section {
  margin-bottom: 2rem;
  background-color: white;
  border-radius: 1rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.section-header {
  padding: 1.25rem;
  background: linear-gradient(to right, rgba(13, 71, 161, 0.1), rgba(33, 150, 243, 0.05));
  border-bottom: 1px solid rgba(33, 150, 243, 0.1);
}

.section-header h2 {
  margin: 0;
  font-size: 1.25rem;
  color: #0D47A1;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.status-badge {
  font-size: 0.8rem;
  background-color: #4CAF50;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 1rem;
  margin-left: 0.75rem;
}

.year-badge {
  font-size: 0.8rem;
  background-color: #FF9800;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 1rem;
  margin-left: 0.75rem;
}

/* 活动卡片样式 */
.activities-container {
  padding: 1rem;
}

.activity-card {
  background-color: #fff;
  border-radius: 0.75rem;
  margin-bottom: 1rem;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

.activity-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.25rem;
  background-color: rgba(33, 150, 243, 0.05);
  cursor: pointer;
  border-bottom: 1px solid rgba(33, 150, 243, 0.1);
}

.activity-header h3 {
  margin: 0;
  font-size: 1.1rem;
  color: #1976D2;
}

.activity-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.toggle-text {
  font-size: 0.9rem;
  color: #1976D2;
}

.expand-icon {
  transition: transform 0.3s ease;
  transform: rotate(-90deg);
  font-size: 1.25rem;
  color: #1976D2;
}

.expand-icon.expanded {
  transform: rotate(-270deg);
}

.activity-content {
  padding: 1.25rem;
}

.activity-description {
  margin: 0 0 1.25rem;
  color: #555;
  line-height: 1.5;
}

.sub-activities {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.sub-activity-title {
  margin: 0 0 1rem;
  font-size: 1rem;
  color: #333;
  font-weight: 600;
  padding-bottom: 0.5rem;
  border-bottom: 1px dashed #e0e0e0;
}

.image-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 0.75rem;
}

.image-item {
  aspect-ratio: 16/9;
  overflow: hidden;
  border-radius: 0.5rem;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.image-item:hover {
  transform: scale(1.02);
}

.image-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 近期规划样式 */
.plans-container {
  padding: 1.25rem;
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.plan-card {
  display: flex;
  align-items: flex-start;
  background-color: #fff;
  padding: 1.25rem;
  border-radius: 0.75rem;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

.plan-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  background-color: rgba(33, 150, 243, 0.1);
  border-radius: 50%;
  margin-right: 1rem;
  flex-shrink: 0;
}

.plan-icon .el-icon {
  font-size: 1.5rem;
  color: #1976D2;
}

.plan-content {
  flex: 1;
}

.plan-content h3 {
  margin: 0 0 0.5rem;
  font-size: 1.1rem;
  color: #1976D2;
}

.plan-content p {
  margin: 0;
  color: #555;
  line-height: 1.5;
}

/* 预期成效展望样式 */
.expectations-container {
  padding: 1.25rem;
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.expectation-card {
  background-color: #fff;
  padding: 1.25rem;
  border-radius: 0.75rem;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

.expectation-header {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
}

.expectation-header .el-icon {
  color: #FF9800;
  font-size: 1.25rem;
  margin-right: 0.75rem;
}

.expectation-header h3 {
  margin: 0;
  font-size: 1.1rem;
  color: #333;
}

.expectation-card p {
  margin: 0;
  color: #555;
  line-height: 1.5;
}

/* 图片查看器样式 */
.gallery-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.85);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.gallery-container {
  width: 90%;
  max-width: 900px;
  max-height: 90vh;
  background-color: white;
  border-radius: 0.75rem;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.gallery-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background-color: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
}

.gallery-header h3 {
  margin: 0;
  font-size: 1.1rem;
  color: #333;
}

.close-button {
  transform: rotate(180deg);
  font-size: 1.25rem;
  color: #333;
}

.gallery-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.gallery-image-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  overflow: hidden;
}

.gallery-image {
  max-width: 100%;
  max-height: 60vh;
  object-fit: contain;
}

.gallery-navigation {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  gap: 1rem;
}

.nav-button {
  width: 2.5rem;
  height: 2.5rem;
}

.rotated-icon {
  transform: rotate(180deg);
}

.image-counter {
  font-size: 0.9rem;
  color: #666;
}

/* 电话号码相关样式 */
.phone-container {
  display: flex;
  align-items: center;
}

.phone-number {
  margin-right: 0.5rem;
}

.phone-actions {
  display: flex;
  gap: 0.5rem;
}

.action-button {
  padding: 0.25rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.action-button:hover {
  background-color: rgba(33, 150, 243, 0.1);
}

.copy-button {
  color: #1976D2;
}

.call-button {
  color: #4CAF50;
}

/* 响应式样式 */
@media (min-width: 768px) {
  .header h1 {
    font-size: 1.25rem;
  }
  
  .section-header h2 {
    font-size: 1.4rem;
  }
  
  .image-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
  }
  
  .plans-container {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.25rem;
  }
  
  .expectations-container {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.25rem;
  }
  
  .gallery-container {
    width: 80%;
  }
  
  .gallery-image {
    max-height: 70vh;
  }
}

@media (min-width: 1024px) {
  .content {
    padding-left: 2rem;
    padding-right: 2rem;
  }
  
  .plans-container {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .expectations-container {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .image-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }
  
  .gallery-container {
    width: 70%;
  }
}
</style>