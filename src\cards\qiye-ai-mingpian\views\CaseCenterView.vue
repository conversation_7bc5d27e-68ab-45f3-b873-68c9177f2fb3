<script setup lang="ts">
import { onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ArrowLeft } from '@element-plus/icons-vue'

const router = useRouter()

const goBack = () => {
  router.push('/card/qiye-ai-mingpian')
}

// 企业名片案例
const enterpriseCases = [
  {
    name: '后日资本',
    url: 'https://zl.sdtaa.com/card/houri-capital',
    logo: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/HouRiZiBen/Logo2.jpg',
    description: '利用AI名片，在多场景下实现高效、专业的品牌输出。'
  },
  {
    name: '师大儿保托育中心',
    url: 'https://zl.sdtaa.com/card/shida-erbao-tuoyu',
    logo: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ShiDaErBao/Logo.jpg',
    description: 'AI宣传员7x24小时接待家长，将咨询转化率提升30%。'
  },
  {
    name: '长三角数字人才创新实践基地',
    url: 'https://zl.sdtaa.com/card/csj-szrc-shangrao',
    logo: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/RenCaiJiDi/Logo.jpg',
    description: '通过AI名片，实现团队能力标准化，十倍提升对外沟通效率。'
  }
]

// 产品名片案例
const productCases = [
  {
    name: 'AI数智代言人',
    url: 'https://zl.sdtaa.com/card/ai-sz-agent',
    logo: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/AIShuZhiDaiYanRen/Logo.jpg',
    description: '产品AI名片嵌入销售全流程，提升沟通效率，降低销售人员能力要求，提升销售转化率'
  }
]

// 政府案例
const governmentCases = [
  {
    name: '上饶传媒集团',
    url: 'https://www.sdtaa.com/external-promotion/srcm',
    logo: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ShangRaoChuanMei/logo.jpg',
    description: '借助AI名片，打通内部业务壁垒，并开创"AI数智代言人"新增长曲线'
  },
  {
    name: '上饶市数字技术应用协会',
    url: 'https://www.sdtaa.com/contact',
    logo: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/STDAA/sdtaa-logo.jpg',
    description: '通过AI名片，实现团队能力标准化，十倍提升对外沟通效率。'
  },
  {
    name: '上饶市广丰区市场监督管理局',
    url: 'https://zl.sdtaa.com/card/gfq-scjgj',
    logo: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/GuangFengShiChang/logo.png',
    description: '运用AI推介官，将政策咨询效率提升80%，打造智慧政务新标杆。'
  }
]

// 打开案例链接
const openCaseUrl = (url: string) => {
  window.open(url, '_blank')
}

onMounted(() => {
  document.title = '企业AI宣传官 - 案例中心'
  const link = document.querySelector('link[rel="icon"]') as HTMLLinkElement || document.createElement('link')
  link.type = 'image/jpeg'
  link.rel = 'icon'
  link.href = 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/qiyeAImingpianLogo.jpg'
  document.head.appendChild(link)
})
</script>

<template>
  <div class="view-container">
    <div class="header">
      <el-button type="text" @click="goBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <h1>案例中心</h1>
    </div>

    <div class="content">
      <div class="case-center-container">
        <!-- 企业名片案例 -->
        <section class="case-section">
          <h2 class="section-title">企业AI宣传官案例</h2>
          <p class="section-desc">为各类企业打造的专属AI数字名片，提升品牌形象与客户沟通效率</p>
          
          <div class="case-grid">
            <div v-for="(item, index) in enterpriseCases" :key="index" class="case-card" @click="openCaseUrl(item.url)">
              <div class="case-logo">
                <img :src="item.logo" :alt="item.name + '的Logo'">
              </div>
              <div class="case-info">
                <h3 class="case-name">{{ item.name }}</h3>
                <p class="case-desc">{{ item.description }}</p>
                <div class="case-link">访问案例 →</div>
              </div>
            </div>
          </div>
        </section>
        
        <!-- 产品名片案例 -->
        <section class="case-section">
          <h2 class="section-title">产品AI宣传官案例</h2>
          <p class="section-desc">针对特定产品定制的AI数字名片，展示产品特性与价值</p>
          
          <div class="case-grid">
            <div v-for="(item, index) in productCases" :key="index" class="case-card" @click="openCaseUrl(item.url)">
              <div class="case-logo">
                <img :src="item.logo" :alt="item.name + '的Logo'">
              </div>
              <div class="case-info">
                <h3 class="case-name">{{ item.name }}</h3>
                <p class="case-desc">{{ item.description }}</p>
                <div class="case-link">访问案例 →</div>
              </div>
            </div>
          </div>
        </section>
        
        <!-- 政府案例 -->
        <section class="case-section">
          <h2 class="section-title">政府案例</h2>
          <p class="section-desc">助力政府部门数字化转型，提升公共服务效率与体验</p>
          
          <div class="case-grid">
            <div v-for="(item, index) in governmentCases" :key="index" class="case-card" @click="openCaseUrl(item.url)">
              <div class="case-logo">
                <img :src="item.logo" :alt="item.name + '的Logo'">
              </div>
              <div class="case-info">
                <h3 class="case-name">{{ item.name }}</h3>
                <p class="case-desc">{{ item.description }}</p>
                <div class="case-link">访问案例 →</div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  </div>
</template>

<style scoped>
.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8f9fa;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  min-width: 100vw;
  box-sizing: border-box;
}

.header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, #2980b9, #3498db);
  color: white;
  height: 3rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 100;
  box-sizing: border-box;
}

.back-button {
  color: white;
  margin-right: 1rem;
  font-size: 1.25rem;
  padding: 0.5rem;
  position: absolute;
  left: 0.5rem;
  z-index: 1;
}

.header h1 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  text-align: center;
}

.content {
  flex: 1;
  padding-top: 4.5rem;
  padding-bottom: 4.5rem; /* 为底部标签栏留出空间 */
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
  padding-left: 1rem;
  padding-right: 1rem;
}

.case-center-container {
  width: 100%;
}

/* 案例部分样式 */
.case-section {
  margin-bottom: 1.5rem;
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  padding: 1.5rem;
}

.section-title {
  font-size: 1.3rem;
  color: #2c3e50;
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-weight: 600;
  position: relative;
  padding-left: 1rem;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0.2rem;
  height: 1.2rem;
  width: 4px;
  background-color: #3498db;
  border-radius: 2px;
}

.section-desc {
  color: #606266;
  margin-top: 0;
  margin-bottom: 1.5rem;
  font-size: 0.95rem;
  padding-left: 1rem;
}

.case-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.2rem;
}

.case-card {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  cursor: pointer;
  display: flex;
  flex-direction: row;
  border: 1px solid #eaeaea;
}

.case-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.case-logo {
  width: 120px;
  height: 120px;
  overflow: hidden;
  background-color: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  padding: 0.5rem;
  box-sizing: border-box;
}

.case-logo img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 4px;
}

.case-info {
  padding: 1rem;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.case-name {
  font-size: 1.1rem;
  font-weight: 500;
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
}

.case-desc {
  font-size: 0.9rem;
  color: #606266;
  margin: 0 0 0.5rem 0;
  line-height: 1.5;
  flex: 1;
  display: -webkit-box;
  display: -ms-flexbox;
  display: box;
  -webkit-line-clamp: 3;
  -ms-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  -ms-box-orient: vertical;
  box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.case-link {
  color: #3498db;
  font-size: 0.9rem;
  font-weight: 500;
  text-align: right;
  margin-top: 0.5rem;
  transition: color 0.2s ease;
}

.case-card:hover .case-link {
  color: #2980b9;
}

@media (min-width: 768px) {
  .header {
    height: 3.5rem;
  }
  
  .header h1 {
    font-size: 1.2rem;
  }
  
  .content {
    padding-top: 5rem;
  }
  
  .section-title {
    font-size: 1.5rem;
  }
  
  .case-grid {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  }
  
  .case-logo {
    width: 140px;
    height: 140px;
    padding: 0.6rem;
  }
}

@media (max-width: 480px) {
  .case-grid {
    grid-template-columns: 1fr;
  }
  
  .case-logo {
    width: 100px;
    height: 100px;
    padding: 0.4rem;
  }
  
  .case-info {
    padding: 0.8rem;
  }
  
  .case-name {
    font-size: 1rem;
  }
  
  .case-desc {
    font-size: 0.85rem;
    -webkit-line-clamp: 2;
    -ms-line-clamp: 2;
    line-clamp: 2;
  }
}
</style> 