<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ArrowLeft, ChatDotRound, User, Phone } from '@element-plus/icons-vue'

const router = useRouter()

const goBack = () => {
  router.push('/card/zhi-lian')
}

const redirectToChat = () => {
  window.open('https://ai.sdtaa.com:3105/chat/share?shareId=b5mn4hnk1ed4ud3fkd645bqf', '_blank')
}

// 动画效果
const isAnimating = ref(false)

const animateButton = () => {
  isAnimating.value = true
  setTimeout(() => {
    isAnimating.value = false
  }, 1000)
}

onMounted(() => {
  document.title = 'AI小链 - 企业的智能宣传中枢'
})
</script>

<template>
  <div class="view-container">
    <div class="header">
      <el-button type="text" @click="goBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <h1>AI小链</h1>
    </div>

    <div class="content">
      <!-- 引导语区域 -->
      <div class="intro-section">
        <div class="ai-avatar">
          <el-icon class="avatar-icon"><ChatDotRound /></el-icon>
          <div class="pulse-circle"></div>
        </div>
        
        <h2 class="intro-title">想了解智链？问AI小链！</h2>
        <p class="intro-subtitle">为您提供专业解答和个性化方案</p>
        
        <el-button 
          type="primary" 
          class="chat-button" 
          @click="redirectToChat"
          @mouseenter="animateButton"
          :class="{ 'animate-pulse': isAnimating }"
        >
          <el-icon class="button-icon"><ChatDotRound /></el-icon>
          点击开始对话
        </el-button>
      </div>
      
      <!-- 联系方式区域 -->
      <div class="contact-section">
        <h3 class="contact-title">直接联系方式</h3>
        
        <div class="contact-item">
          <el-icon class="contact-icon"><User /></el-icon>
          <span class="contact-text">联系人：周丽娟</span>
        </div>
        
        <div class="contact-item">
          <el-icon class="contact-icon"><Phone /></el-icon>
          <span class="contact-text">电话：18479656459</span>
        </div>
      </div>
      
      <!-- 装饰元素 -->
      <div class="decoration-dots dot-1"></div>
      <div class="decoration-dots dot-2"></div>
      <div class="decoration-dots dot-3"></div>
    </div>
  </div>
</template>

<style scoped>
.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: var(--background);
  position: relative;
  overflow: hidden;
}

.header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, #4d91ff, #6ba6ff);
  color: white;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 3rem;
  z-index: 100;
  box-sizing: border-box;
}

.back-button {
  color: white;
  margin-right: 1rem;
  font-size: 1.25rem;
  padding: 0.5rem;
  position: absolute;
  left: 0.5rem;
  z-index: 1;
}

.header h1 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  width: 100%;
  text-align: center;
}

.content {
  flex: 1;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 5rem;
  position: relative;
}

/* 引导语区域 */
.intro-section {
  max-width: 500px;
  width: 100%;
  padding: 2rem;
  background: white;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(77, 145, 255, 0.15);
  text-align: center;
  margin-bottom: 2rem;
  position: relative;
  z-index: 2;
}

.ai-avatar {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #4d91ff, #3060b0);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  position: relative;
}

.avatar-icon {
  font-size: 40px;
  color: white;
}

.pulse-circle {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 3px solid #4d91ff;
  opacity: 0.7;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.3;
  }
  100% {
    transform: scale(1);
    opacity: 0.7;
  }
}

.intro-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #3060b0;
  margin: 0 0 0.5rem;
}

.intro-subtitle {
  font-size: 1rem;
  color: #666;
  margin: 0 0 2rem;
}

.chat-button {
  height: 3.5rem;
  font-size: 1.1rem;
  padding: 0 2rem;
  border-radius: 12px;
  background: linear-gradient(135deg, #4d91ff, #3060b0);
  border: none;
  transition: transform 0.3s, box-shadow 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  max-width: 300px;
  margin: 0 auto;
}

.chat-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 15px rgba(77, 145, 255, 0.3);
}

.button-icon {
  margin-right: 8px;
  font-size: 1.2rem;
}

.animate-pulse {
  animation: buttonPulse 1s;
}

@keyframes buttonPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* 联系方式区域 */
.contact-section {
  max-width: 500px;
  width: 100%;
  padding: 1.5rem 2rem;
  background: white;
  border-radius: 16px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 2;
}

.contact-title {
  font-size: 1.1rem;
  font-weight: 500;
  color: #333;
  margin: 0 0 1rem;
  text-align: center;
  position: relative;
}

.contact-title:after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 3px;
  background: linear-gradient(135deg, #4d91ff, #3060b0);
  border-radius: 3px;
}

.contact-item {
  display: flex;
  align-items: center;
  padding: 0.8rem 0;
  border-bottom: 1px solid #f0f0f0;
}

.contact-item:last-child {
  border-bottom: none;
}

.contact-icon {
  font-size: 1.2rem;
  color: #4d91ff;
  margin-right: 1rem;
}

.contact-text {
  font-size: 1rem;
  color: #333;
}

/* 装饰元素 */
.decoration-dots {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, #4d91ff, #3060b0);
  opacity: 0.1;
  z-index: 1;
}

.dot-1 {
  width: 200px;
  height: 200px;
  top: 10%;
  right: -50px;
}

.dot-2 {
  width: 150px;
  height: 150px;
  bottom: 20%;
  left: -30px;
}

.dot-3 {
  width: 100px;
  height: 100px;
  bottom: 30%;
  right: 20%;
}

/* 响应式调整 */
@media (max-width: 600px) {
  .intro-section, .contact-section {
    padding: 1.5rem;
  }
  
  .ai-avatar {
    width: 60px;
    height: 60px;
  }
  
  .avatar-icon {
    font-size: 30px;
  }
  
  .intro-title {
    font-size: 1.3rem;
  }
  
  .chat-button {
    height: 3rem;
  }
}
</style>