<script setup lang="ts">
import { useRouter } from 'vue-router'
import { onMounted } from 'vue'
import { ArrowLeft } from '@element-plus/icons-vue'

const router = useRouter()

const goBack = () => {
  router.push('/card/sr-media')
}

// 悬浮窗视频URL
const floatingVideoUrl = 'https://pic.sdtaa.com/ZhiLian/Video/Enterprise/ShangRaoChuanMei/XuanFuChuangShiPing.mp4'

// 强制播放悬浮视频，解决微信浏览器自动播放问题
const playFloatingVideo = () => {
  const floatingVideo = document.querySelector('.floating-video') as HTMLVideoElement

  if (floatingVideo) {
    floatingVideo.play().catch(err => {
      console.log('Floating video autoplay failed:', err)
    })
  }
}

onMounted(() => {
  // 页面加载后立即尝试播放
  playFloatingVideo()

  // 监听用户交互事件，确保视频能够播放
  const handleUserInteraction = () => {
    playFloatingVideo()
    // 移除事件监听器，避免重复触发
    document.removeEventListener('touchstart', handleUserInteraction)
    document.removeEventListener('click', handleUserInteraction)
  }

  document.addEventListener('touchstart', handleUserInteraction, { once: true })
  document.addEventListener('click', handleUserInteraction, { once: true })
})
</script>

<template>
  <div class="chat-container">
    <div class="header">
      <el-button type="text" @click="goBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <h1>与我对话</h1>
    </div>

    <!-- 悬浮视频 -->
    <div class="floating-video-container">
      <video
        class="floating-video"
        :src="floatingVideoUrl"
        autoplay
        muted
        loop
        playsinline
        webkit-playsinline
        x5-playsinline
        x5-video-player-type="h5"
        x5-video-player-fullscreen="false"
        preload="auto"
      ></video>
    </div>

    <div class="chat-content">
      <iframe
        src="https://ai.sdtaa.com:3105/chat/share?shareId=q8zo22tpoju7jzmbz5su2q85"
        style="width: 100%; height: 100%;"
        frameborder="0"
        allow="camera; microphone;"
      />
    </div>
  </div>
</template>

<style scoped>
.chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ffffff;
  box-sizing: border-box;
}

.header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, #3b82f6, #60a5fa);
  color: white;
  height: 3rem;
  width: 100%;
  z-index: 100;
  box-sizing: border-box;
  flex-shrink: 0;
}

.back-button {
  color: white;
  margin-right: 1rem;
  font-size: 1.25rem;
  padding: 0.5rem;
  position: absolute;
  left: 0.5rem;
  z-index: 1;
}

.header h1 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  width: 100%;
  text-align: center;
}

/* 悬浮视频容器 */
.floating-video-container {
  position: absolute;
  top: 3rem;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 100px;
  z-index: 200;
  border-radius: 50%;
  overflow: hidden;
}

/* 悬浮视频 */
.floating-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  border-radius: 50%;
  /* 微信浏览器兼容性 */
  -webkit-playsinline: true;
  -webkit-appearance: none;
  appearance: none;
  outline: none;
}

.chat-content {
  flex: 1;
  width: 100%;
  height: calc(100vh - 4.5rem);
  overflow: hidden;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .floating-video-container {
    width: 80px;
    height: 80px;
    top: 3rem;
  }
}

@media (min-width: 769px) {
  .floating-video-container {
    width: 120px;
    height: 120px;
    top: 3.5rem;
  }
}

@media (min-width: 1200px) {
  .floating-video-container {
    width: 140px;
    height: 140px;
    top: 4rem;
  }
}
</style>
