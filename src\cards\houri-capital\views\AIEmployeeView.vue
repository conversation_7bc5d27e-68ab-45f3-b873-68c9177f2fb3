<script setup lang="ts">
import { useRouter, onBeforeRouteLeave } from 'vue-router'
import { ArrowLeft, Reading, Timer, User, ArrowRight } from '@element-plus/icons-vue'
import { ref, onMounted } from 'vue'

const router = useRouter()

const goBack = () => {
  router.push('/card/houri-capital')
}

const chatWithAI = () => {
  window.location.href = 'https://ai.sdtaa.com:3105/chat/share?shareId=jp24j6vs061s76ldw85wms6d'
}

// 添加打字机效果
const welcomeText = ref('')
const fullText = '您好！我是后日资本的数智员工——后小资。想快速了解后日资本的业务、理念或任何您感兴趣的内容？无需等待，随时向我提问吧！'
const typingSpeed = 50
let currentIndex = 0
let typingTimer: number | null = null

const typeText = () => {
  if (currentIndex < fullText.length) {
    welcomeText.value += fullText.charAt(currentIndex)
    currentIndex++
    typingTimer = setTimeout(typeText, typingSpeed) as unknown as number
  }
}

onMounted(() => {
  document.title = '后日资本 - 智能员工后小资'
  setTimeout(() => {
    typeText()
  }, 500)
})

onBeforeRouteLeave(() => {
  if (typingTimer) {
    clearTimeout(typingTimer)
  }
})
</script>

<template>
  <div class="view-container">
    <div class="header">
      <el-button type="text" @click="goBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <h1>智能员工：后小资</h1>
    </div>

    <div class="content">
      <div class="ai-employee-container">
        <div class="ai-card">
          <div class="ai-image-container">
            <div class="ai-image"></div>
            <div class="image-overlay"></div>
          </div>
          <div class="ai-content">
            <h2>后小资 <span class="badge">AI 数智员工</span></h2>
            <div class="typing-container">
              <p class="welcome-text">{{ welcomeText }}<span class="cursor" v-if="welcomeText.length < fullText.length">|</span></p>
            </div>
            <div class="action-container">
              <el-button type="primary" class="chat-btn" @click="chatWithAI">
                立即对话后小资
                <el-icon><ArrowRight /></el-icon>
              </el-button>
            </div>
          </div>
        </div>
        
        <div class="features-section">
          <h3>为什么选择与后小资对话？</h3>
          <div class="features-grid">
            <div class="feature-item">
              <div class="feature-icon knowledge-icon">
                <el-icon><Reading /></el-icon>
              </div>
              <h4>专业知识</h4>
              <p>掌握后日资本全面业务知识，为您提供精准信息</p>
            </div>
            <div class="feature-item">
              <div class="feature-icon speed-icon">
                <el-icon><Timer /></el-icon>
              </div>
              <h4>即时响应</h4>
              <p>24小时在线，随时解答您的问题，无需等待</p>
            </div>
            <div class="feature-item">
              <div class="feature-icon personalized-icon">
                <el-icon><User /></el-icon>
              </div>
              <h4>个性化互动</h4>
              <p>根据您的需求提供定制化解答和服务</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8faf3;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  min-width: 100vw;
  box-sizing: border-box;
}

.header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, #7e8f4e, #9fb25f);
  color: white;
  height: 3rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 100;
  box-sizing: border-box;
}

.back-button {
  color: white;
  margin-right: 1rem;
  font-size: 1.25rem;
  padding: 0.5rem;
  position: absolute;
  left: 0.5rem;
  z-index: 1;
}

.header h1 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  width: 100%;
  text-align: center;
}

.content {
  flex: 1;
  padding-top: 4.5rem;
  padding-bottom: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
  padding-left: 1rem;
  padding-right: 1rem;
}

.ai-employee-container {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.ai-card {
  background: white;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
}

.ai-image-container {
  position: relative;
  height: 300px;
  overflow: hidden;
}

.ai-image {
  width: 100%;
  height: 100%;
  background: url('https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/HouRiZiBen/HouRiShuZiRen.png') center top/cover;
}

.image-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 80px;
  background: linear-gradient(to top, white, transparent);
}

.ai-content {
  padding: 1.5rem;
}

.ai-content h2 {
  color: #7e8f4e;
  margin: 0 0 1rem;
  font-size: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.badge {
  background: rgba(126, 143, 78, 0.15);
  color: #7e8f4e;
  font-size: 0.8rem;
  padding: 0.2rem 0.5rem;
  border-radius: 0.25rem;
  font-weight: 500;
}

.typing-container {
  min-height: 4.8rem;
  margin-bottom: 1.5rem;
}

.welcome-text {
  color: #555;
  line-height: 1.6;
  margin: 0;
}

.cursor {
  display: inline-block;
  width: 2px;
  height: 1em;
  background-color: #7e8f4e;
  animation: blink 1s infinite;
  vertical-align: middle;
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
}

.action-container {
  display: flex;
  justify-content: center;
}

.chat-btn {
  background: linear-gradient(135deg, #7e8f4e, #9fb25f);
  border: none;
  border-radius: 2rem;
  padding: 0.75rem 2rem;
  font-size: 1rem;
  font-weight: 500;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.chat-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(126, 143, 78, 0.3);
}

.features-section {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.features-section h3 {
  color: #7e8f4e;
  margin: 0 0 1.5rem;
  font-size: 1.3rem;
  text-align: center;
}

.features-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 1rem;
  border-radius: 0.75rem;
  background: rgba(248, 250, 243, 0.6);
  transition: all 0.3s ease;
}

.feature-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.05);
}

.feature-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: rgba(126, 143, 78, 0.15);
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.feature-icon .el-icon {
  font-size: 30px;
  color: #7e8f4e;
}

.knowledge-icon {
  background-color: rgba(126, 143, 78, 0.15);
}

.speed-icon {
  background-color: rgba(126, 143, 78, 0.15);
}

.personalized-icon {
  background-color: rgba(126, 143, 78, 0.15);
}

.feature-item h4 {
  color: #7e8f4e;
  margin: 0 0 0.5rem;
  font-size: 1.1rem;
}

.feature-item p {
  color: #666;
  margin: 0;
  line-height: 1.5;
  font-size: 0.95rem;
}

@media (min-width: 768px) {
  .content {
    padding-top: 5rem;
    padding-left: 2rem;
    padding-right: 2rem;
  }
  
  .ai-card {
    flex-direction: row;
    align-items: stretch;
  }
  
  .ai-image-container {
    width: 40%;
    height: auto;
    min-height: 300px;
  }
  
  .image-overlay {
    right: 0;
    bottom: 0;
    left: auto;
    top: 0;
    width: 60px;
    height: 100%;
    background: linear-gradient(to left, white, transparent);
  }
  
  .ai-content {
    width: 60%;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  
  .features-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
</style> 