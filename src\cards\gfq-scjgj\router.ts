import { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'gfqScjgjHome',
    component: () => import('./views/HomeView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/GuangFengShiChang/logo.png',
      title: '上饶市广丰区市场监督管理局'
    }
  },
  {
    path: '/introduction',
    name: 'gfqScjgjIntroduction',
    component: () => import('./views/IntroductionView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/GuangFengShiChang/logo.png',
      title: '简介 - 上饶市广丰区市场监督管理局'
    }
  },
  {
    path: '/policy',
    name: 'gfqScjgjPolicy',
    component: () => import('./views/PolicyView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/GuangFengShiChang/logo.png',
      title: '知识产权政策 - 上饶市广丰区市场监督管理局'
    }
  },
  {
    path: '/contact',
    name: 'gfqScjgjContact',
    component: () => import('./views/ContactView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/GuangFengShiChang/logo.png',
      title: '联系我们 - 上饶市广丰区市场监督管理局'
    }
  }
]

export default routes 