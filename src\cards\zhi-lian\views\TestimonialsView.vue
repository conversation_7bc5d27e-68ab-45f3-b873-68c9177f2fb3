<script setup lang="ts">
import { onMounted, ref, computed, onBeforeUnmount } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ArrowLeft } from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()

const goBack = () => {
  router.push('/card/zhi-lian')
}

// 定义类型接口
interface ServiceType {
  id: string;
  name: string;
}

interface IndustryType {
  id: string;
  name: string;
}

interface CaseItem {
  id: number;
  name: string;
  industry: string;
  service: string;
  challenge: string;
  solution: string;
  result: string;
  link: string;
  image: string;
  logo?: string; // 可选的logo字段
}

interface AIRepresentativeCases {
  government: string[];
  culture: string[];
  tourism: string[];
  [key: string]: string[]; // 添加索引签名
}

// 服务类型和行业类型数据
const serviceTypes: ServiceType[] = [
  { id: 'all', name: '全部' },
  { id: 'aiCard', name: '企业AI名片' },
  { id: 'aiMarketing', name: 'AI营销专家' },
  { id: 'aiRepresentative', name: 'AI数智代言人' },
  { id: 'zhixingAI', name: '知行AI' },
  { id: 'customerView', name: '客户全景智联' }
]

const industryTypes: IndustryType[] = [
  { id: 'all', name: '全部' },
  { id: 'education', name: '教育培训' },
  { id: 'government', name: '政府与公共服务' },
  { id: 'finance', name: '金融与投资' },
  { id: 'culture', name: '文化传媒' },
  { id: 'tech', name: 'IT与科技' },
  { id: 'business', name: '商业服务' },
  { id: 'healthcare', name: '医疗健康' },
  { id: 'tourism', name: '旅游与酒店' },
  { id: 'retail', name: '零售与电商' },
  { id: 'manufacturing', name: '生产制造' },
  { id: 'realestate', name: '建筑与房地产' },
  { id: 'others', name: '其他' }
]

// 当前选中的服务和行业
const currentService = ref('all')
const currentIndustry = ref('all')

// 案例数据
const cases: CaseItem[] = [
  {
    id: 1,
    name: '上饶市数字技术应用协会',
    industry: 'government',
    service: 'aiCard',
    challenge: '协会活动与服务信息对外传递效率不高，会员互动不足。',
    solution: '构建协会专属AI智能门户，便捷分享协会动态与服务，智能客服提升互动效率。',
    result: '增强协会影响力，提升会员服务体验与参与度。',
    link: 'https://www.sdtaa.com/contact',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/SDTAA-FM.jpeg',
    logo: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/SDTAA-Logo.jpg'
  },
  {
    id: 2,
    name: '师大儿保托育中心',
    industry: 'education',
    service: 'aiCard',
    challenge: '家长了解机构信息渠道分散，咨询回复不及时。',
    solution: '打造一体化AI智能门户，7x24h智能应答，动态展示园区环境与教学特色。',
    result: '提升家长咨询体验，便捷信息获取，塑造专业可信赖形象。',
    link: 'https://zl.sdtaa.com/card/shida-erbao-tuoyu',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/SDEBTYZX.png',
    logo: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/SDEBTYZX-Logo.png'
  },
  {
    id: 3,
    name: '后日资本',
    industry: 'finance',
    service: 'aiCard',
    challenge: '对外展示专业形象，快速传递核心业务与优势的需求。',
    solution: '定制专业AI名片，结构化展示公司业务、投资理念及成功案例。',
    result: '提升品牌专业度，高效对接政府与合作伙伴。',
    link: 'https://zl.sdtaa.com/card/houri-capital',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/HouriLogo-1.jpg',
    logo: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/HouriLogo-1.jpg'
  },
  {
    id: 4,
    name: '上饶传媒集团',
    industry: 'culture',
    service: 'aiCard',
    challenge: '整合集团资源，打造统一对外宣传窗口。',
    solution: '搭建集团AI智能名片，集中展示旗下媒体资源与业务板块。',
    result: '提升集团整体形象，方便外界快速了解集团全貌。',
    link: 'https://www.sdtaa.com/external-promotion/srcm',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/SRCMJT.png',
    logo: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/SRCMJT.png'
  },
  {
    id: 5,
    name: '广丰市场监督管理局(知识产权融资)',
    industry: 'government',
    service: 'aiCard',
    challenge: '知识产权融资抵押政策与流程宣传普及难，企业咨询不便。',
    solution: '打造知识产权融资服务AI名片，提供政策解读、流程指引、智能问答。',
    result: '提升政策宣传效率，便捷企业咨询，助力知识产权转化。',
    link: 'https://zl.sdtaa.com/card/gfq-scjgj',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/GFSCJDGLJ.png',
    logo: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/GFSCJDGLJ.png'
  },
  {
    id: 6,
    name: '江旅集团(大型活动应急宣传)',
    industry: 'tourism',
    service: 'aiRepresentative',
    challenge: '大型活动临近，急需创新形式的宣传视频，时间紧迫。',
    solution: '3天内完成AI数智代言人宣传视频制作与交付，满足活动展示需求。',
    result: '活动宣传效果惊艳，获得高度好评，彰显AI技术高效与创新。',
    link: '',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/JXLY.png',
    logo: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/JXLY.png'
  },
  {
    id: 7,
    name: '上饶市人工智能学习及应用平台',
    industry: 'government',
    service: 'zhixingAI',
    challenge: '提升政务人员AI素养与应用能力，缺乏系统化、场景化的学习资源。',
    solution: '定制化AI学习平台，包含政务场景课程、AI工具集、数智员工调用、AI老师答疑。',
    result: '实现AI知识从学习到应用的闭环，有效提升政务人员AI实战能力。',
    link: '',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/ZhiXingAI-FM.jpg',
    logo: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/ZhiXingAI-FM.jpg'
  }
]

// AI数智代言人其他案例图片
const aiRepresentativeCases: AIRepresentativeCases = {
  government: [
    'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/ZhengWu-1.png',
    'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/ZhengWu-2.png',
    'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/ZhengWu-3.png',
    'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/ZhengWu-4.png'
  ],
  culture: [
    'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/WenHua-1.png',
    'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/WenHua-2.png',
    'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/WenHua-3.png'
  ],
  tourism: [
    'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/LvYou-1.png'
  ]
}

// 根据筛选条件过滤案例
const filteredCases = computed(() => {
  return cases.filter(item => {
    const serviceMatch = currentService.value === 'all' || item.service === currentService.value
    const industryMatch = currentIndustry.value === 'all' || item.industry === currentIndustry.value
    return serviceMatch && industryMatch
  })
})

// 根据当前筛选的行业获取AI数智代言人案例图片
const filteredAiRepresentativeImages = computed(() => {
  if (currentService.value !== 'all' && currentService.value !== 'aiRepresentative') {
    return []
  }
  
  if (currentIndustry.value === 'all') {
    // 如果行业是"全部"，返回所有图片
    return Object.values(aiRepresentativeCases).flat()
  } else {
    // 返回特定行业的图片
    return aiRepresentativeCases[currentIndustry.value] || []
  }
})

// 切换筛选条件
const changeService = (service: string) => {
  currentService.value = service
}

const changeIndustry = (industry: string) => {
  currentIndustry.value = industry
}

// 查看AI名片
const viewAiCard = (caseItem: CaseItem) => {
  // 实现跳转到对应客户的AI名片逻辑
  if (caseItem.link && caseItem.link !== '#' && caseItem.link !== '') {
    window.open(caseItem.link, '_blank')
  } else if (caseItem.link === '#') {
    console.log('查看AI名片:', caseItem.name)
    // 这里可以实现内部跳转逻辑
  }
}

// 处理图片预览
const previewVisible = ref(false)
const previewImage = ref('')
const imageScale = ref(1)
const imageTranslateX = ref(0)
const imageTranslateY = ref(0)
const isDragging = ref(false)
const startX = ref(0)
const startY = ref(0)

// 自定义属性，用于缩放计算
interface CustomProperties {
  _initialPinchDistance?: number;
  _initialScale?: number;
}

// 监听返回键
const handlePopState = (event: PopStateEvent): void => {
  if (previewVisible.value) {
    event.preventDefault()
    closePreview()
    // 推一个新状态到历史记录，以防连续点击返回导致页面实际返回
    window.history.pushState(null, '', window.location.pathname)
  }
}

const showPreview = (image: string): void => {
  // 记录当前状态到历史记录，这样按返回键时会触发 popstate 事件
  window.history.pushState(null, '', window.location.pathname)
  
  // 重置图片缩放和位置
  imageScale.value = 1
  imageTranslateX.value = 0
  imageTranslateY.value = 0
  
  previewImage.value = image
  previewVisible.value = true
}

const closePreview = (): void => {
  previewVisible.value = false
}

// 处理图片缩放
const handlePinch = (event: TouchEvent): void => {
  if (!previewVisible.value) return
  
  // 阻止默认缩放行为
  event.preventDefault()
  
  // 如果有多个触摸点，则处理缩放
  if (event.touches.length === 2) {
    const touch1 = event.touches[0]
    const touch2 = event.touches[1]
    
    const distance = Math.sqrt(
      Math.pow(touch2.clientX - touch1.clientX, 2) + 
      Math.pow(touch2.clientY - touch1.clientY, 2)
    )
    
    const element = event.target as HTMLElement & CustomProperties
    
    // 使用临时存储的初始距离来计算缩放
    if (!element._initialPinchDistance) {
      element._initialPinchDistance = distance
      element._initialScale = imageScale.value
    } else {
      // 限制最小和最大缩放比例
      const initialScale = element._initialScale || 1
      const newScale = initialScale * (distance / element._initialPinchDistance)
      imageScale.value = Math.max(1, Math.min(5, newScale))
    }
  }
}

// 处理触摸结束事件
const handleTouchEnd = (event: TouchEvent | MouseEvent): void => {
  const element = event.target as HTMLElement & CustomProperties
  if (element) {
    element._initialPinchDistance = undefined
    element._initialScale = undefined
  }
  
  isDragging.value = false
}

// 处理开始拖动
const handleDragStart = (event: MouseEvent | TouchEvent): void => {
  if (imageScale.value <= 1) return
  
  isDragging.value = true
  
  if (event.type === 'touchstart') {
    const touchEvent = event as TouchEvent
    startX.value = touchEvent.touches[0].clientX - imageTranslateX.value
    startY.value = touchEvent.touches[0].clientY - imageTranslateY.value
  } else {
    const mouseEvent = event as MouseEvent
    startX.value = mouseEvent.clientX - imageTranslateX.value
    startY.value = mouseEvent.clientY - imageTranslateY.value
  }
}

// 处理拖动移动
const handleDragMove = (event: MouseEvent | TouchEvent): void => {
  if (!isDragging.value || imageScale.value <= 1) return
  
  event.preventDefault()
  
  let currentX: number, currentY: number
  
  if (event.type === 'touchmove') {
    const touchEvent = event as TouchEvent
    currentX = touchEvent.touches[0].clientX
    currentY = touchEvent.touches[0].clientY
  } else {
    const mouseEvent = event as MouseEvent
    currentX = mouseEvent.clientX
    currentY = mouseEvent.clientY
  }
  
  // 计算新的位置
  const newTranslateX = currentX - startX.value
  const newTranslateY = currentY - startY.value
  
  // 设置边界，防止图片被拖出太远
  const maxTranslateX = (imageScale.value - 1) * 150
  const maxTranslateY = (imageScale.value - 1) * 150
  
  imageTranslateX.value = Math.max(-maxTranslateX, Math.min(maxTranslateX, newTranslateX))
  imageTranslateY.value = Math.max(-maxTranslateY, Math.min(maxTranslateY, newTranslateY))
}

// 双击图片重置缩放
const handleDoubleClick = (): void => {
  if (imageScale.value > 1) {
    // 重置缩放和位置
    imageScale.value = 1
    imageTranslateX.value = 0
    imageTranslateY.value = 0
  } else {
    // 放大到2倍
    imageScale.value = 2
  }
}

// 处理图片点击事件
const handlePreviewClick = (event: MouseEvent): void => {
  // 如果点击的是图片容器但不是图片本身，关闭预览
  if (event.target !== event.currentTarget) return
  closePreview()
}

// 添加展开/收起状态控制
const expandedDetails = ref<number[]>([])

// 切换展开/收起状态
const toggleDetails = (caseId: number): void => {
  const index = expandedDetails.value.indexOf(caseId)
  if (index === -1) {
    expandedDetails.value.push(caseId)
  } else {
    expandedDetails.value.splice(index, 1)
  }
}

// 检查案例是否已展开
const isExpanded = (caseId: number): boolean => {
  return expandedDetails.value.includes(caseId)
}

// 为筛选导航栏添加展开/收起功能
const serviceShowMore = ref(false)
const industryShowMore = ref(false)

// 设置每行最大显示的选项数量（根据视觉需求可调整）
const maxItemsPerRow = ref(4)

// 计算每个导航栏应显示的选项
const visibleServiceTypes = computed(() => {
  if (serviceShowMore.value) {
    return serviceTypes
  } else {
    return serviceTypes.slice(0, maxItemsPerRow.value)
  }
})

const visibleIndustryTypes = computed(() => {
  if (industryShowMore.value) {
    return industryTypes
  } else {
    return industryTypes.slice(0, maxItemsPerRow.value)
  }
})

// 切换显示更多选项
const toggleServiceMore = (): void => {
  serviceShowMore.value = !serviceShowMore.value
}

const toggleIndustryMore = (): void => {
  industryShowMore.value = !industryShowMore.value
}

onMounted(() => {
  document.title = '实力见证 - 企业的智能宣传中枢'
  
  // 添加返回键监听
  window.addEventListener('popstate', handlePopState)
  
  // 从 URL 参数中获取筛选条件
  const filterParam = route.query.filter as string
  if (filterParam) {
    // 查找匹配的服务类型
    const service = serviceTypes.find(s => s.name === filterParam)
    if (service) {
      currentService.value = service.id
    }
    
    // 如果参数中包含行业信息，也设置行业筛选
    const industryParam = route.query.industry as string
    if (industryParam && industryTypes.some(i => i.id === industryParam)) {
      currentIndustry.value = industryParam
    }
  }
})

onBeforeUnmount(() => {
  // 移除返回键监听
  window.removeEventListener('popstate', handlePopState)
})
</script>

<template>
  <div class="view-container">
    <div class="header">
      <el-button type="text" @click="goBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <h1>实力见证</h1>
    </div>

    <div class="content">
      <!-- 筛选导航 -->
      <div class="filter-nav">
        <!-- 一级导航：按服务筛选 -->
        <div class="filter-section">
          <div class="filter-label">按服务筛选：</div>
          <div class="filter-options" :class="{ 'expanded': serviceShowMore }">
            <div 
              v-for="service in serviceShowMore ? serviceTypes : visibleServiceTypes" 
              :key="service.id"
              class="filter-item"
              :class="{ active: currentService === service.id }"
              @click="changeService(service.id)"
            >
              {{ service.name }}
            </div>
          </div>
          <div class="toggle-more" @click="toggleServiceMore" v-if="serviceTypes.length > maxItemsPerRow">
            {{ serviceShowMore ? '收起' : '更多' }} <span class="toggle-icon">{{ serviceShowMore ? '▲' : '▼' }}</span>
          </div>
        </div>
        
        <!-- 二级导航：按行业筛选 -->
        <div class="filter-section">
          <div class="filter-label">按行业筛选：</div>
          <div class="filter-options" :class="{ 'expanded': industryShowMore }">
            <div 
              v-for="industry in industryShowMore ? industryTypes : visibleIndustryTypes" 
              :key="industry.id"
              class="filter-item"
              :class="{ active: currentIndustry === industry.id }"
              @click="changeIndustry(industry.id)"
            >
              {{ industry.name }}
            </div>
          </div>
          <div class="toggle-more" @click="toggleIndustryMore" v-if="industryTypes.length > maxItemsPerRow">
            {{ industryShowMore ? '收起' : '更多' }} <span class="toggle-icon">{{ industryShowMore ? '▲' : '▼' }}</span>
          </div>
        </div>
      </div>

      <!-- 案例列表 -->
      <div class="case-list">
        <!-- 案例卡片 -->
        <div v-for="(caseItem, index) in filteredCases" :key="index" class="case-card">
          <div class="case-image" @click="showPreview(caseItem.image)">
            <img :src="caseItem.image" alt="案例图片">
            <div class="image-overlay">
              <el-icon class="preview-icon"><i class="el-icon-zoom-in"></i></el-icon>
            </div>
          </div>
          <div class="case-content">
            <div class="case-header">
              <div class="case-logo" v-if="caseItem.logo">
                <img :src="caseItem.logo" alt="企业logo">
              </div>
              <div class="case-logo default-logo" v-else>
                <span>{{ caseItem.name.charAt(0) }}</span>
              </div>
              <h3 class="case-title">{{ caseItem.name }}</h3>
            </div>
            <div class="case-tags">
              <span class="industry-tag">{{ industryTypes.find(i => i.id === caseItem.industry)?.name }}</span>
              <span class="service-tag">{{ serviceTypes.find(s => s.id === caseItem.service)?.name }}</span>
              <div class="expand-toggle" @click="toggleDetails(caseItem.id)">
                <span>{{ isExpanded(caseItem.id) ? '收起' : '查看详情' }}</span>
                <span class="toggle-icon">{{ isExpanded(caseItem.id) ? '▲' : '▼' }}</span>
              </div>
            </div>
            
            <!-- 可折叠的详情区域 -->
            <div class="case-details" v-show="isExpanded(caseItem.id)">
              <div class="detail-item">
                <div class="detail-label">挑战：</div>
                <div class="detail-text">{{ caseItem.challenge }}</div>
              </div>
              <div class="detail-item">
                <div class="detail-label">智链方案：</div>
                <div class="detail-text">{{ caseItem.solution }}</div>
              </div>
              <div class="detail-item">
                <div class="detail-label">成果：</div>
                <div class="detail-text">{{ caseItem.result }}</div>
              </div>
            </div>
            
            <div class="case-action" :class="{ 'with-margin': !isExpanded(caseItem.id) }">
              <el-button 
                :type="caseItem.link && caseItem.link !== '' ? 'primary' : 'info'" 
                :disabled="!caseItem.link || caseItem.link === ''"
                @click="viewAiCard(caseItem)"
              >
                查看TA的AI名片
              </el-button>
            </div>
          </div>
        </div>

        <!-- AI数智代言人图片展示 -->
        <div v-if="filteredAiRepresentativeImages.length > 0" class="ai-representative-section">
          <h2 class="section-title">AI数智代言人案例展示</h2>
          <div class="image-gallery">
            <div 
              v-for="(image, index) in filteredAiRepresentativeImages" 
              :key="'img-'+index" 
              class="gallery-item"
              @click="showPreview(image)"
            >
              <img :src="image" alt="AI数智代言人案例">
              <div class="image-overlay">
                <el-icon class="preview-icon"><i class="el-icon-zoom-in"></i></el-icon>
              </div>
            </div>
          </div>
        </div>

        <!-- 无结果提示 -->
        <div v-if="filteredCases.length === 0 && filteredAiRepresentativeImages.length === 0" class="no-results">
          <el-empty description="没有找到符合条件的案例" />
        </div>
      </div>
    </div>

    <!-- 图片预览 -->
    <div class="image-preview" v-if="previewVisible" @click="handlePreviewClick">
      <div 
        class="preview-container"
        :style="{
          transform: `scale(${imageScale}) translate(${imageTranslateX}px, ${imageTranslateY}px)`,
          transition: isDragging ? 'none' : 'transform 0.3s'
        }"
        @touchstart.passive="handleDragStart"
        @touchmove.prevent="handleDragMove"
        @touchend.passive="handleTouchEnd"
        @mousedown.passive="handleDragStart"
        @mousemove.prevent="handleDragMove"
        @mouseup.passive="handleTouchEnd"
        @touchcancel.passive="handleTouchEnd"
        @mouseleave.passive="handleTouchEnd"
        @dblclick.stop="handleDoubleClick"
      >
        <img 
          :src="previewImage" 
          alt="预览图片" 
          @touchstart.prevent="handlePinch"
          @touchmove.prevent="handlePinch"
          @touchend.passive="handleTouchEnd"
        >
      </div>
      <div class="preview-controls">
        <div class="close-preview" @click.stop="closePreview">
          <el-icon><i class="el-icon-close"></i></el-icon>
        </div>
        <div class="zoom-instructions">
          <span>双指缩放 • 拖动移动 • 双击放大/重置</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.view-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--background);
}

.header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, #1b4283, #2c6dd1);
  color: white;
  position: relative;
  height: 3rem;
}

.back-button {
  color: white;
  margin-right: 1rem;
  font-size: 1.25rem;
  padding: 0.5rem;
  position: absolute;
  left: 0.5rem;
  z-index: 1;
}

.header h1 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  width: 100%;
  text-align: center;
}

.content {
  flex: 1;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

/* 筛选导航样式 - 缩小尺寸 */
.filter-nav {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  padding: 0.8rem;
  margin-bottom: 1.2rem;
}

.filter-section {
  display: flex;
  align-items: center;
  margin-bottom: 0.6rem;
  position: relative;
  flex-wrap: wrap;
}

.filter-section:last-child {
  margin-bottom: 0;
}

.filter-label {
  font-weight: 500;
  color: #333;
  min-width: 90px;
  font-size: 0.9rem;
  margin-right: auto;
}

.filter-options {
  display: flex;
  flex-wrap: nowrap; /* 修改为不换行 */
  gap: 0.4rem;
  overflow-x: hidden; /* 隐藏超出部分 */
  max-width: calc(100% - 180px); /* 为标签和"更多"按钮留出空间 */
  order: 2;
}

.filter-item {
  padding: 0.3rem 0.6rem;
  border-radius: 4px;
  background-color: #f5f7fa;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 0.85rem;
  white-space: nowrap; /* 确保内容不换行 */
}

.filter-item:hover {
  background-color: #e6eaf2;
}

.filter-item.active {
  background-color: #1b4283;
  color: white;
}

/* 显示更多按钮样式 */
.toggle-more {
  cursor: pointer;
  color: #2c6dd1;
  font-size: 0.85rem;
  padding: 0.3rem 0.6rem;
  border-radius: 4px;
  background-color: #f0f5ff;
  transition: all 0.3s;
  margin-left: 0.4rem;
  white-space: nowrap;
  display: flex;
  align-items: center;
  z-index: 1;
  order: 3;
}

.toggle-more:hover {
  background-color: #e0e9ff;
}

.toggle-more .toggle-icon {
  margin-left: 0.2rem;
  font-size: 0.7rem;
}

/* 当展开显示更多选项时的样式 */
.filter-options.expanded {
  flex-wrap: wrap;
  max-height: none;
  overflow: visible;
  margin-top: 0.5rem;
  margin-bottom: 0.5rem; /* 为下一行选项留出空间 */
  transition: all 0.3s ease;
  flex-basis: 100%;
  order: 4;
}

/* 动画效果 */
.filter-options {
  transition: all 0.3s ease;
}

/* 案例列表样式 */
.case-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.case-card {
  display: flex;
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s, box-shadow 0.3s;
}

.case-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.case-image {
  width: 200px;
  height: 200px;
  overflow: hidden;
  position: relative;
  cursor: pointer;
}

.case-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
}

.case-image:hover .image-overlay,
.gallery-item:hover .image-overlay {
  opacity: 1;
}

.case-image:hover img,
.gallery-item:hover img {
  transform: scale(1.05);
}

.preview-icon {
  color: white;
  font-size: 1.5rem;
}

.case-content {
  flex: 1;
  padding: 1.2rem;
  display: flex;
  flex-direction: column;
}

/* 调整案例标题和logo的样式 */
.case-header {
  display: flex;
  align-items: center;
  margin-bottom: 0.6rem;
}

.case-logo {
  width: 24px;
  height: 24px;
  min-width: 24px; /* 防止logo被挤压 */
  border-radius: 3px;
  overflow: hidden;
  margin-right: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f5ff;
  border: 1px solid #e0e9ff;
}

.case-logo img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.default-logo {
  background-color: #1b4283;
  color: white;
  font-weight: bold;
  font-size: 0.8rem;
}

.case-title {
  margin: 0;
  font-size: 1.05rem;
  color: #1b4283;
  flex: 1;
  /* 确保标题文字在一行显示不下时自动换行 */
  word-break: break-word;
}

.case-tags {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.8rem;
  align-items: center;
}

.industry-tag, .service-tag {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
}

.industry-tag {
  background-color: #e6f7ff;
  color: #1890ff;
}

.service-tag {
  background-color: #f6ffed;
  color: #52c41a;
}

/* 展开/收起按钮样式，确保按钮不会占用太多空间 */
.expand-toggle {
  margin-left: auto;
  cursor: pointer;
  display: flex;
  align-items: center;
  color: #2c6dd1; /* 蓝色文字 */
  font-size: 0.75rem;
  padding: 0.2rem 0.4rem;
  border-radius: 4px;
  background-color: #f0f5ff;
  transition: all 0.3s;
  white-space: nowrap;
  flex-shrink: 0; /* 防止按钮被压缩 */
}

.expand-toggle:hover {
  background-color: #e0e9ff;
}

.toggle-icon {
  margin-left: 0.2rem;
  font-size: 0.7rem;
  color: #2c6dd1; /* 蓝色箭头 */
}

/* 详情区域样式 */
.case-details {
  border-top: 1px dashed #eee;
  padding-top: 0.8rem;
  margin-bottom: 0.8rem;
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.detail-item {
  margin-bottom: 0.6rem;
}

.detail-label {
  font-weight: 500;
  color: #555;
  margin-bottom: 0.2rem;
}

.detail-text {
  color: #666;
  font-size: 0.9rem;
  line-height: 1.5;
}

.case-action {
  text-align: right;
}

.case-action.with-margin {
  margin-top: 0;
}

/* AI数智代言人案例展示 */
.ai-representative-section {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin-top: 1.5rem;
}

.section-title {
  font-size: 1.2rem;
  color: #1b4283;
  margin: 0 0 1rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #eee;
}

.image-gallery {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
}

.gallery-item {
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s;
  cursor: pointer;
  position: relative;
}

.gallery-item img {
  width: 100%;
  height: 180px;
  object-fit: cover;
  transition: transform 0.3s;
}

/* 无结果提示 */
.no-results {
  margin: 3rem 0;
}

/* 图片预览 */
.image-preview {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.85);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  touch-action: none; /* 防止浏览器的默认触摸行为 */
  overflow: hidden;
}

.preview-container {
  position: relative;
  max-width: 100%;
  max-height: 100%;
  transform-origin: center;
  will-change: transform; /* 提高性能 */
}

.preview-container img {
  max-width: 100vw;
  max-height: 90vh;
  object-fit: contain;
  border-radius: 4px;
  -webkit-user-drag: none;
  user-select: none;
}

.preview-controls {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.close-preview {
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
}

.zoom-instructions {
  color: white;
  font-size: 0.8rem;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 5px 10px;
  border-radius: 20px;
  margin-top: 10px;
}

@media (max-width: 768px) {
  .case-card {
    flex-direction: column;
  }
  
  .case-image {
    width: 100%;
    height: 180px;
  }
  
  .filter-section {
    flex-direction: row;
    align-items: center;
    flex-wrap: wrap;
    margin-bottom: 1rem;
    gap: 0.5rem;
  }
  
  .filter-label {
    margin-bottom: 0;
    margin-right: auto;
    width: auto;
    font-size: 0.85rem;
  }
  
  .toggle-more {
    position: static;
    order: 3;
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
  }
  
  .filter-options {
    max-width: 100%; /* 移动端占满宽度 */
    overflow-x: auto; /* 允许横向滚动 */
    padding-bottom: 0.3rem; /* 为滚动条预留空间 */
    -webkit-overflow-scrolling: touch; /* 提升移动端滚动体验 */
    scrollbar-width: thin;
    order: 4;
    flex-basis: 100%;
    margin-top: 0.4rem;
  }
  
  .filter-options::-webkit-scrollbar {
    height: 4px;
  }
  
  .filter-options::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
  }
  
  .filter-options.expanded {
    margin-top: 0.4rem;
    margin-bottom: 0.4rem;
    flex-wrap: wrap;
    overflow-x: visible;
  }
  
  .zoom-instructions {
    display: none; /* 在小屏幕上隐藏说明文字，节省空间 */
  }
  
  .case-tags {
    flex-wrap: wrap;
  }
  
  .expand-toggle {
    margin-left: auto; /* 保持在右侧 */
    margin-top: 0; /* 不需要上边距 */
    width: auto; /* 不需要占据整行 */
    justify-content: center;
  }
}
</style>