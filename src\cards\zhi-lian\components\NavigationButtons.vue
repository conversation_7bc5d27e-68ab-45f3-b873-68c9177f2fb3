<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { 
  Cpu, 
  ChatDotRound, 
  Promotion, 
  Medal 
} from '@element-plus/icons-vue'

const router = useRouter()

const navigationItems = ref([
  {
    id: 'zhilian-ai',
    name: '智链AI',
    icon: Cpu,
    route: '/card/zhi-lian/zhilian-ai',
    color: 'linear-gradient(135deg, #3060b0, #4d91ff)'
  },
  {
    id: 'ai-assistant',
    name: 'AI小链',
    icon: ChatDotRound,
    route: '/card/zhi-lian/ai-assistant',
    color: 'linear-gradient(135deg, #4d91ff, #6ba6ff)'
  },
  {
    id: 'products',
    name: '产品介绍',
    icon: Promotion,
    route: '/card/zhi-lian/products',
    color: 'linear-gradient(135deg, #1e88e5, #42a5f5)'
  },
  {
    id: 'testimonials',
    name: '实力见证',
    icon: Medal,
    route: '/card/zhi-lian/testimonials',
    color: 'linear-gradient(135deg, #1b4283, #2c6dd1)'
  }
])

const navigateTo = (route: string) => {
  router.push(route)
}
</script>

<template>
  <div class="navigation-grid">
    <div 
      v-for="item in navigationItems" 
      :key="item.id" 
      class="nav-button"
      @click="navigateTo(item.route)"
    >
      <div class="icon-container" :style="{ background: item.color }">
        <component :is="item.icon" class="icon" />
      </div>
      <span class="button-text">{{ item.name }}</span>
    </div>
  </div>
</template>

<style scoped>
.navigation-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
}

.nav-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 1rem;
  padding: 1rem 0.8rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  cursor: pointer;
  overflow: hidden;
  position: relative;
  border: 1px solid rgba(255, 255, 255, 0.6);
}

.nav-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
  z-index: 0;
}

.nav-button:active {
  transform: scale(0.98);
}

.nav-button:hover {
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  margin-bottom: 0.5rem;
  transition: transform 0.3s ease;
  position: relative;
  z-index: 1;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
}

.icon {
  font-size: 1.1rem;
  color: white;
  transform: scale(0.7);
}

.button-text {
  font-size: 0.9rem;
  font-weight: 500;
  background: linear-gradient(135deg, #1b4283, #3060b0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transition: all 0.2s ease;
  position: relative;
  z-index: 1;
}

.nav-button:hover .icon-container {
  transform: scale(1.05);
}

@media (min-width: 768px) {
  .navigation-grid {
    gap: 2rem;
    grid-template-columns: repeat(4, 1fr);
    min-width: 800px;
    max-width: 1200px;
  }
  
  .nav-button {
    padding: 1.2rem 1rem;
  }
  
  .icon-container {
    width: 3rem;
    height: 3rem;
    margin-bottom: 0.6rem;
  }
  
  .icon {
    font-size: 1.3rem;
    transform: scale(0.7);
  }
  
  .button-text {
    font-size: 1rem;
    background: linear-gradient(135deg, #1b4283, #3060b0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}

@media (min-width: 1200px) {
  .navigation-grid {
    gap: 3rem;
  }
}
</style>