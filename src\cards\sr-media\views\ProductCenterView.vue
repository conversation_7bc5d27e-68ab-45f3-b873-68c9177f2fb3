<script setup lang="ts">
import { useRouter } from 'vue-router'
import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue'
import TabBar from '../components/TabBar.vue'

const router = useRouter()

const goBack = () => {
  router.push('/card/sr-media')
}

// 产品数据
const products = [
  {
    id: 'rongmeiti',
    name: '融媒体发展有限公司',
    description: '服务至上 合作共赢',
    logo: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ShangRaoChuanMei/zigongsi/rongmeitifazhan-fenxiangye.png',
    route: '/card/sr-media/product/rongmeiti'
  },
  {
    id: 'guanggao',
    name: '广告有限公司',
    description: '优质 专业 高效',
    logo: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ShangRaoChuanMei/zigongsi/guanggao-fengxiangtu.png',
    route: '/card/sr-media/product/guanggao'
  },
  {
    id: 'yingshi',
    name: '影视有限公司',
    description: '创新 专业 高效',
    logo: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ShangRaoChuanMei/zigongsi/yingshi-fengxiangye.png',
    route: '/card/sr-media/product/yingshi'
  },
  {
    id: 'shuzikeji',
    name: '数字科技有限公司',
    description: '开拓 创新 发展',
    logo: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ShangRaoChuanMei/zigongsi/shuzikeji-fengxiangtu.png',
    route: '/card/sr-media/product/shuzikeji'
  },
  {
    id: 'wenhuachanye',
    name: '文化产业有限公司',
    description: '打造具有地方特色的文化产业品牌',
    logo: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ShangRaoChuanMei/zigongsi/wenhuachanye-fenxiangtu.png',
    route: '/card/sr-media/product/wenhuachanye'
  },
  {
    id: 'yanyi',
    name: '演艺有限公司',
    description: '服务至上 品质第一',
    logo: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ShangRaoChuanMei/zigongsi/yanyi-fengxiangtu.png',
    route: '/card/sr-media/product/yanyi'
  }
]

// 跳转到产品详情页
const goToProduct = (product: any) => {
  router.push(product.route)
}
</script>

<template>
  <div class="view-container">
    <div class="header">
      <el-button type="text" @click="goBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <h1>产品中心</h1>
    </div>

    <div class="content">
      <div class="products-grid">
        <div
          v-for="product in products"
          :key="product.id"
          class="product-card"
          @click="goToProduct(product)"
        >
          <div class="product-logo">
            <img :src="product.logo" :alt="product.name" class="logo-image">
          </div>
          <div class="product-info">
            <h3 class="product-name">{{ product.name }}</h3>
            <p class="product-description">{{ product.description }}</p>
          </div>
          <div class="product-arrow">
            <el-icon><ArrowRight /></el-icon>
          </div>
        </div>
      </div>
    </div>
    
    <TabBar />
  </div>
</template>

<style scoped>
.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8fafc;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  min-width: 100vw;
  box-sizing: border-box;
}

.header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, #3b82f6, #60a5fa);
  color: white;
  height: 3rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 100;
  box-sizing: border-box;
}

.back-button {
  color: white;
  margin-right: 1rem;
  font-size: 1.25rem;
  padding: 0.5rem;
  position: absolute;
  left: 0.5rem;
  z-index: 1;
}

.header h1 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  width: 100%;
  text-align: center;
}

.content {
  flex: 1;
  padding-top: 4.5rem;
  padding-bottom: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
  padding-left: 1rem;
  padding-right: 1rem;
}

.products-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
  max-width: 800px;
  margin: 0 auto;
}

.product-card {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  display: flex;
  align-items: center;
  gap: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.product-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(59, 130, 246, 0.15);
  border-color: #3b82f6;
}

.product-logo {
  flex-shrink: 0;
  width: 80px;
  height: 80px;
  border-radius: 0.75rem;
  overflow: hidden;
  background: #f8fafc;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-info {
  flex: 1;
  min-width: 0;
}

.product-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
  line-height: 1.4;
}

.product-description {
  font-size: 0.9rem;
  color: #6b7280;
  margin: 0;
  line-height: 1.5;
}

.product-arrow {
  flex-shrink: 0;
  color: #9ca3af;
  font-size: 1.2rem;
  transition: all 0.3s ease;
}

.product-card:hover .product-arrow {
  color: #3b82f6;
  transform: translateX(4px);
}

@media (max-width: 768px) {
  .product-card {
    padding: 1rem;
  }

  .product-logo {
    width: 60px;
    height: 60px;
  }

  .product-name {
    font-size: 1rem;
  }

  .product-description {
    font-size: 0.85rem;
  }
}
</style>
