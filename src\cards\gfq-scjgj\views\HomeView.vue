<script setup lang="ts">
import { ref } from 'vue'
import NavigationButtons from '../components/NavigationButtons.vue'

const slogan = ref('有关知识产权的任何问题，知识产权AI宣传员智丰为您解答！')
</script>

<template>
  <div class="home-container">
    <div class="background-container">
      <img 
        src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/GuangFengShiChang/background.png"
        alt="广丰区市场监督管理局背景" 
        class="background-image"
      >
    </div>
    
    <div class="bottom-section">
      <div class="slogan-container">
        <div class="slogan-wrapper">
          <h1 class="slogan">{{ slogan }}</h1>
          <div class="official-line"></div>
        </div>
      </div>
      
      <div class="navigation-container">
        <NavigationButtons />
      </div>
    </div>
  </div>
</template>

<style scoped>
.home-container {
  position: relative;
  height: 100vh;
  max-height: 100vh;
  overflow: hidden;
  background-color: #f0f5fa;
}

.background-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.background-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.bottom-section {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(240, 248, 255, 0.7);
  z-index: 2;
  backdrop-filter: blur(5px);
}

.slogan-container {
  padding: 1rem 1rem 0.5rem 1rem;
  display: flex;
  justify-content: center;
}

.slogan-wrapper {
  position: relative;
  max-width: 280px;
  padding-bottom: 0.5rem;
}

.official-line {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(to right, transparent, #1e2eb7 30%, #1e2eb7 70%, transparent);
}

.slogan {
  font-size: 1.15rem;
  line-height: 1.5;
  background: linear-gradient(135deg, #151fa8, #1e2eb7);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
  text-align: center;
  font-weight: 500;
}

.navigation-container {
  padding: 1rem 1rem calc(env(safe-area-inset-bottom) + 2rem) 1rem;
}

@media (min-width: 768px) {
  .bottom-section {
    padding-top: 4rem;
  }
  
  .slogan-wrapper {
    max-width: 400px;
    padding-bottom: 0.75rem;
  }

  .slogan {
    font-size: 1.6rem;
    background: linear-gradient(135deg, #151fa8, #1e2eb7);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .slogan-container {
    padding: 1.5rem 1.5rem 0.75rem 1.5rem;
  }
  
  .navigation-container {
    padding: 0.75rem 1.5rem calc(env(safe-area-inset-bottom) + 2.5rem) 1.5rem;
  }
}
</style> 