<script setup lang="ts">
import { useRouter } from 'vue-router'
import { ArrowLeft, Notebook, Histogram, Money, Clock, Star } from '@element-plus/icons-vue'
import { onMounted, ref } from 'vue'

const router = useRouter()

const goBack = () => {
  router.push('/card/shida-erbao-tuoyu')
}

// 分周龄教学体系特点展示
const ageBasedFeatures = ref([
  {
    title: '精细化关注',
    description: '以周龄计算宝宝的成长阶段，精细化关注关键成长期。'
  },
  {
    title: '潜能开发',
    description: '从出生第一天开始，促进孩子潜能开发。'
  },
  {
    title: '进阶成长',
    description: '每周3个关键目标，共156周精细化进阶成长测评。'
  },
  {
    title: '多维度指导',
    description: '156周多维度细节指导，包含在线视频指导、教案卡、在线班主任指导。'
  },
  {
    title: '精细化教具',
    description: '468种精细化教具，与每周三个关键目标匹配。'
  },
  {
    title: '线上线下结合',
    description: '线上陪伴师+线下分周龄指导。'
  },
  {
    title: '个性化教学',
    description: '一个孩子一个进度，根据实际出生日期制定个性化教学计划和教具。'
  }
])

onMounted(() => {
  document.title = '课程介绍 - 师大儿保托育中心'
})
</script>

<template>
  <div class="view-container">
    <div class="header">
      <el-button type="text" @click="goBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <h1>课程介绍</h1>
    </div>

    <div class="content">
      <!-- 全日托课程部分 -->
      <div class="section course-section">
        <div class="section-header">
          <el-icon class="section-icon"><Notebook /></el-icon>
          <h2 class="section-title">全日托课程</h2>
        </div>
        <div class="course-content">
          <p class="course-description">
            基于分周龄教学体系，课程包括语言绘本，音乐律动，感统运动，社会认知，创意美劳，安全教育等。
          </p>
          <div class="course-subjects">
            <div class="subject-tag">语言绘本</div>
            <div class="subject-tag">音乐律动</div>
            <div class="subject-tag">感统运动</div>
            <div class="subject-tag">社会认知</div>
            <div class="subject-tag">创意美劳</div>
            <div class="subject-tag">安全教育</div>
          </div>
        </div>
      </div>
      
      <!-- 分周龄教学体系部分 -->
      <div class="section age-based-section">
        <div class="section-header">
          <el-icon class="section-icon"><Histogram /></el-icon>
          <h2 class="section-title">分周龄教学体系</h2>
        </div>
        <div class="age-based-content">
          <div class="feature-grid">
            <div 
              v-for="(feature, index) in ageBasedFeatures" 
              :key="index"
              class="feature-card"
            >
              <h3 class="feature-title">{{ feature.title }}</h3>
              <p class="feature-description">{{ feature.description }}</p>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 收费标准部分 -->
      <div class="section fees-section">
        <div class="section-header">
          <el-icon class="section-icon"><Money /></el-icon>
          <h2 class="section-title">收费标准</h2>
        </div>
        <div class="fees-content">
          <div class="fee-item">
            <div class="fee-label">托费</div>
            <div class="fee-value">2280元/月</div>
            <div class="fee-note">包含伙食费，提供一餐两点：午餐、上午水果点心、下午点心</div>
          </div>
          <div class="fee-item">
            <div class="fee-label">延时托</div>
            <div class="fee-value">300元/月</div>
            <div class="fee-note">17:00-18:00</div>
          </div>
          <div class="fee-item">
            <div class="fee-label">连报优惠</div>
            <div class="fee-value">详询中心</div>
            <div class="fee-note">具体优惠信息请联系中心咨询</div>
          </div>
        </div>
      </div>
      
      <!-- 接送时间部分 -->
      <div class="section schedule-section">
        <div class="section-header">
          <el-icon class="section-icon"><Clock /></el-icon>
          <h2 class="section-title">接送时间</h2>
        </div>
        <div class="schedule-content">
          <div class="schedule-item">
            <div class="schedule-label">入托时间</div>
            <div class="schedule-value">上午 8:00</div>
          </div>
          <div class="schedule-item">
            <div class="schedule-label">接送时间</div>
            <div class="schedule-value">16:10 - 17:00</div>
          </div>
          <div class="schedule-item">
            <div class="schedule-label">延时托接送时间</div>
            <div class="schedule-value">至 18:00</div>
          </div>
        </div>
      </div>
      
      <!-- 试课部分 -->
      <div class="section trial-section">
        <div class="section-header">
          <el-icon class="section-icon"><Star /></el-icon>
          <h2 class="section-title">试课</h2>
        </div>
        <div class="trial-content">
          <p class="trial-description">
            提供试课，需提前转发朋友圈并预约时间。
          </p>
          <div class="trial-button-container">
            <button class="trial-button" @click="router.push('/card/shida-erbao-tuoyu/contact')">
              立即预约
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8faf3;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  min-width: 100vw;
  box-sizing: border-box;
}

.header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, #5dae57, #7fc379);
  color: white;
  height: 3rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 100;
  box-sizing: border-box;
}

.back-button {
  color: white;
  margin-right: 1rem;
  font-size: 1.25rem;
  padding: 0.5rem;
  position: absolute;
  left: 0.5rem;
  z-index: 1;
}

.header h1 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  width: 100%;
  text-align: center;
}

.content {
  flex: 1;
  padding-top: 4.5rem;
  padding-bottom: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
  padding-left: 1rem;
  padding-right: 1rem;
}

/* 通用部分样式 */
.section {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
}

.section-icon {
  font-size: 1.5rem;
  color: #5dae57;
  margin-right: 0.8rem;
}

.section-title {
  color: #5dae57;
  font-size: 1.3rem;
  margin: 0;
  position: relative;
}

/* 全日托课程部分样式 */
.course-description {
  font-size: 1rem;
  line-height: 1.6;
  color: #444;
  margin-top: 0;
  margin-bottom: 1.5rem;
}

.course-subjects {
  display: flex;
  flex-wrap: wrap;
  gap: 0.8rem;
}

.subject-tag {
  background: rgba(93, 174, 87, 0.1);
  color: #5dae57;
  padding: 0.5rem 1rem;
  border-radius: 2rem;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  border: 1px solid rgba(93, 174, 87, 0.2);
}

.subject-tag:hover {
  background: rgba(93, 174, 87, 0.2);
  transform: translateY(-2px);
}

/* 分周龄教学体系部分样式 */
.feature-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.feature-card {
  background: rgba(93, 174, 87, 0.05);
  border-radius: 0.8rem;
  padding: 1.2rem;
  transition: all 0.3s ease;
  border-left: 3px solid #5dae57;
}

.feature-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(93, 174, 87, 0.1);
}

.feature-title {
  color: #5dae57;
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
}

.feature-description {
  color: #555;
  margin: 0;
  line-height: 1.5;
  font-size: 0.95rem;
}

/* 收费标准部分样式 */
.fees-content {
  display: flex;
  flex-direction: column;
  gap: 1.2rem;
}

.fee-item {
  display: flex;
  flex-direction: column;
  padding: 1rem;
  background: rgba(93, 174, 87, 0.05);
  border-radius: 0.8rem;
}

.fee-label {
  font-weight: 500;
  color: #5dae57;
  margin-bottom: 0.3rem;
  font-size: 1.1rem;
}

.fee-value {
  font-size: 1.3rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
}

.fee-note {
  font-size: 0.9rem;
  color: #666;
}

/* 接送时间部分样式 */
.schedule-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.schedule-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: rgba(93, 174, 87, 0.05);
  border-radius: 0.8rem;
}

.schedule-label {
  font-weight: 500;
  color: #5dae57;
  font-size: 1rem;
}

.schedule-value {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
}

/* 试课部分样式 */
.trial-description {
  font-size: 1rem;
  line-height: 1.6;
  color: #444;
  margin-top: 0;
  margin-bottom: 1.5rem;
}

.trial-button-container {
  display: flex;
  justify-content: center;
}

.trial-button {
  background: linear-gradient(135deg, #5dae57, #7fc379);
  color: white;
  border: none;
  border-radius: 2rem;
  padding: 0.8rem 2rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(93, 174, 87, 0.2);
}

.trial-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(93, 174, 87, 0.3);
}

@media (min-width: 768px) {
  .content {
    padding-top: 5rem;
    padding-left: 2rem;
    padding-right: 2rem;
  }
  
  .feature-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .fees-content {
    flex-direction: row;
    flex-wrap: wrap;
  }
  
  .fee-item {
    flex: 1;
    min-width: 250px;
  }
}

@media (min-width: 1024px) {
  .feature-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
</style> 