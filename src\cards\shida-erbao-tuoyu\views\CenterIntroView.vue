<script setup lang="ts">
import { useRouter } from 'vue-router'
import { ArrowLeft, Location, Calendar, User, Phone, Briefcase, FirstAidKit } from '@element-plus/icons-vue'
import { onMounted, ref } from 'vue'

const router = useRouter()

const goBack = () => {
  router.push('/card/shida-erbao-tuoyu')
}

// 视频播放控制
const isVideoPlaying = ref(false)
const videoRef = ref<HTMLVideoElement | null>(null)

const onVideoPlay = () => {
  isVideoPlaying.value = true
}

const onVideoPause = () => {
  isVideoPlaying.value = false
}

const onVideoEnded = () => {
  isVideoPlaying.value = false
}

onMounted(() => {
  document.title = '中心介绍 - 师大儿保托育中心'
  
  // 添加视频事件监听
  if (videoRef.value) {
    videoRef.value.addEventListener('play', onVideoPlay)
    videoRef.value.addEventListener('pause', onVideoPause)
    videoRef.value.addEventListener('ended', onVideoEnded)
  }
})
</script>

<template>
  <div class="view-container">
    <div class="header">
      <el-button type="text" @click="goBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <h1>中心介绍</h1>
    </div>

    <div class="content">
      <!-- 宣传视频部分 -->
      <div class="video-section">
        <div class="video-container">
          <video 
            ref="videoRef"
            src="https://pic.sdtaa.com/ZhiLian/Video/Enterprise/ShiDaErBao/XuanChuanShiPin.mp4"
            poster="https://pic.sdtaa.com/JiaoYu/Picture/JiaoYuJiGou/SDEBTYZX.png"
            preload="metadata"
            controls
            @ended="onVideoEnded"
            class="video-player"
          ></video>
          <div class="video-label" v-if="!isVideoPlaying">
            <span>宣传视频</span>
          </div>
        </div>
      </div>
      
      <!-- 使命与愿景部分 -->
      <div class="section mission-section">
        <h2 class="section-title">使命与愿景</h2>
        <div class="mission-content">
          <p class="mission-text">创建高标准公办医育一体化托育示范基地，为更多婴幼儿家庭提供高品质的托育早教、科学育儿、安全照护、健康保健等服务。</p>
        </div>
      </div>
      
      <!-- 业务产品核心优势部分 -->
      <div class="section advantages-section">
        <h2 class="section-title">业务产品核心优势</h2>
        <div class="advantages-grid">
          <div class="advantage-card">
            <div class="advantage-card-header">
              <span class="advantage-icon-simple">医</span>
              <h3>医育融合</h3>
            </div>
            <p>信州区儿童防病保健中心联合北京师范大学科技集团合力打造，提供医疗保健特色项目。</p>
          </div>
          
          <div class="advantage-card">
            <div class="advantage-card-header">
              <span class="advantage-icon-simple">科</span>
              <h3>科学育儿理念</h3>
            </div>
            <p>以早教之母区慕洁教授万婴追踪常模数据为内容研发依据，以《托育机构保育指导大纲(试行)》为指导目标。</p>
          </div>
          
          <div class="advantage-card">
            <div class="advantage-card-header">
              <span class="advantage-icon-simple">周</span>
              <h3>分周龄教学体系</h3>
            </div>
            <p>根据婴幼儿科学成长规律，提供精细化、个性化的托育服务。</p>
          </div>
          
          <div class="advantage-card">
            <div class="advantage-card-header">
              <span class="advantage-icon-simple">全</span>
              <h3>全方位服务</h3>
            </div>
            <p>涵盖科学早教、健康养育、潜能开发、安全照护、思维训练、智力拓展、健康体检、婴幼儿测评、生长发育评估、营养膳食、医疗保健等。</p>
          </div>
          
          <div class="advantage-card">
            <div class="advantage-card-header">
              <span class="advantage-icon-simple">师</span>
              <h3>师资力量</h3>
            </div>
            <p>课程体系和服务标准由北京师范大学科技集团托育领域专家联合打造。</p>
          </div>
        </div>
      </div>
      
      <!-- 园区介绍部分 -->
      <div class="section campus-section">
        <h2 class="section-title">园区介绍</h2>
        <div class="campus-info">
          <div class="info-item">
            <el-icon class="info-icon"><Location /></el-icon>
            <div class="info-content">
              <h3>位置</h3>
              <p>江西省上饶市信州区滨江西路19号新儿保中心二楼</p>
            </div>
          </div>
          
          <div class="info-item">
            <div class="custom-icon area-icon"></div>
            <div class="info-content">
              <h3>面积</h3>
              <p>总建筑面积1400平方米，户外活动场地约300平方米</p>
            </div>
          </div>
          
          <div class="info-item">
            <el-icon class="info-icon"><User /></el-icon>
            <div class="info-content">
              <h3>托位</h3>
              <p>150个</p>
            </div>
          </div>
          
          <div class="info-item">
            <el-icon class="info-icon"><Calendar /></el-icon>
            <div class="info-content">
              <h3>服务对象</h3>
              <p>6-36个月学龄前儿童</p>
            </div>
          </div>
          
          <div class="info-item">
            <el-icon class="info-icon"><Phone /></el-icon>
            <div class="info-content">
              <h3>联系方式</h3>
              <p>15779925839</p>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 服务内容部分 -->
      <div class="section service-section">
        <h2 class="section-title">服务内容</h2>
        <div class="service-categories">
          <div class="service-category">
            <div class="category-header">
              <el-icon class="category-icon"><Briefcase /></el-icon>
              <h3>托育项目</h3>
            </div>
            <div class="service-tags">
              <span class="service-tag">科学早教</span>
              <span class="service-tag">健康养育</span>
              <span class="service-tag">潜能开发</span>
              <span class="service-tag">安全照护</span>
              <span class="service-tag">思维训练</span>
              <span class="service-tag">智力拓展</span>
            </div>
          </div>
          
          <div class="service-category">
            <div class="category-header">
              <el-icon class="category-icon"><FirstAidKit /></el-icon>
              <h3>保健项目</h3>
            </div>
            <div class="service-tags">
              <span class="service-tag">健康体检</span>
              <span class="service-tag">婴幼儿测评</span>
              <span class="service-tag">生长发育评估</span>
              <span class="service-tag">营养膳食</span>
              <span class="service-tag">医疗保健</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8faf3;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  min-width: 100vw;
  box-sizing: border-box;
}

.header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, #5dae57, #7fc379);
  color: white;
  height: 3rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 100;
  box-sizing: border-box;
}

.back-button {
  color: white;
  margin-right: 1rem;
  font-size: 1.25rem;
  padding: 0.5rem;
  position: absolute;
  left: 0.5rem;
  z-index: 1;
}

.header h1 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  width: 100%;
  text-align: center;
}

.content {
  flex: 1;
  padding-top: 4.5rem;
  padding-bottom: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
  padding-left: 1rem;
  padding-right: 1rem;
}

/* 视频部分样式 */
.video-section {
  margin-bottom: 2rem;
}

.video-container {
  position: relative;
  width: 100%;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  background-color: #000;
  aspect-ratio: 16 / 9;
}

.video-player {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-label {
  position: absolute;
  top: 15px;
  left: 15px;
  background-color: rgba(255, 255, 255, 0.8);
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 0.9rem;
  color: #5dae57;
  font-weight: 500;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transition: opacity 0.3s ease;
  z-index: 2;
}

/* 通用部分样式 */
.section {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
}

.section-title {
  color: #5dae57;
  font-size: 1.4rem;
  margin-top: 0;
  margin-bottom: 1.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid rgba(93, 174, 87, 0.2);
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 80px;
  height: 2px;
  background: #5dae57;
}

/* 使命部分样式 */
.mission-text {
  font-size: 1.1rem;
  line-height: 1.7;
  color: #444;
}

/* 优势部分样式 - 新的简约设计 */
.advantages-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.advantage-card {
  background: white;
  border-radius: 0.8rem;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border-left: 3px solid #5dae57;
}

.advantage-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(93, 174, 87, 0.12);
}

.advantage-card-header {
  display: flex;
  align-items: center;
  margin-bottom: 0.8rem;
}

.advantage-icon-simple {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  background: linear-gradient(135deg, #5dae57, #7fc379);
  color: white;
  font-weight: bold;
  margin-right: 0.8rem;
  font-size: 1rem;
}

.advantage-card-header h3 {
  color: #5dae57;
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
}

.advantage-card p {
  color: #555;
  margin: 0;
  line-height: 1.5;
  font-size: 0.95rem;
}

/* 园区信息部分样式 */
.campus-info {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.2rem;
}

.info-item {
  display: flex;
  align-items: flex-start;
}

.info-icon, .custom-icon {
  color: #5dae57;
  font-size: 1.5rem;
  margin-right: 1rem;
  flex-shrink: 0;
}

.custom-icon {
  width: 1.5rem;
  height: 1.5rem;
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

.area-icon {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%235dae57"><path d="M17 15h2V7c0-1.1-.9-2-2-2H9v2h8v8zM7 17V1H5v4H1v2h4v10c0 1.1.9 2 2 2h10v4h2v-4h4v-2H7z"/></svg>');
}

.info-content h3 {
  color: #5dae57;
  margin-top: 0;
  margin-bottom: 0.3rem;
  font-size: 1rem;
}

.info-content p {
  color: #555;
  margin: 0;
  line-height: 1.4;
}

/* 服务内容部分样式 */
.service-categories {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.service-category {
  background: rgba(93, 174, 87, 0.05);
  border-radius: 0.8rem;
  padding: 1.2rem;
}

.category-header {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.category-icon {
  color: #5dae57;
  font-size: 1.5rem;
  margin-right: 0.8rem;
}

.category-header h3 {
  color: #5dae57;
  margin: 0;
  font-size: 1.1rem;
}

.service-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.8rem;
}

.service-tag {
  background: rgba(93, 174, 87, 0.15);
  color: #5dae57;
  padding: 0.4rem 0.8rem;
  border-radius: 2rem;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.service-tag:hover {
  background: rgba(93, 174, 87, 0.25);
  transform: translateY(-2px);
}

@media (min-width: 768px) {
  .content {
    padding-top: 5rem;
    padding-left: 2rem;
    padding-right: 2rem;
  }
  
  .advantages-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .campus-info {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .service-categories {
    flex-direction: row;
  }
  
  .service-category {
    flex: 1;
  }
}

@media (min-width: 1024px) {
  .campus-info {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .advantages-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
</style> 