<script setup lang="ts">
import { useRouter } from 'vue-router'
import { ref, onMounted } from 'vue'
import { 
  ArrowLeft, 
  Close,
  Connection,
  Promotion,
  Avatar,
  ChatLineRound,
  Reading,
  School,
  Link,
  View
} from '@element-plus/icons-vue'

// 定义产品详情内容部分的接口
interface ContentSection {
  subtitle?: string;
  text?: string;
  list?: string[];
}

// 定义产品详情的接口
interface ProductDetails {
  title: string;
  videoUrl?: string;
  content: ContentSection[];
}

// 定义产品的接口
interface Product {
  id: string;
  name: string;
  shortDesc: string;
  logo: string;
  link: string;
  hasDetails: boolean;
  icon: any; // Element Plus图标组件类型
  details?: ProductDetails;
}

const router = useRouter()

const goBack = () => {
  router.push('/card/wisestar-tech')
}

// 定义产品数据
const products = ref<Product[]>([
  {
    id: 'qiye-ai-mingpian',
    name: '智链 · 企业AI宣传官',
    shortDesc: '打造7x24小时在线的智能门面。',
    logo: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/qiyeAImingpianLogo.jpg',
    link: 'https://zl.sdtaa.com/card/qiye-ai-mingpian',
    hasDetails: false,
    icon: Connection
  },
  {
    id: 'ai-sz-guide',
    name: '智链 · AI数智推介官',
    shortDesc: '政府智慧推介与服务的新范式。',
    logo: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/AIShuZhiTuiJieGuan/LOGO.jpeg',
    link: 'https://zl.sdtaa.com/card/ai-sz-guide',
    hasDetails: false,
    icon: Promotion
  },
  {
    id: 'ai-sz-agent',
    name: '智链 · AI数智代言人',
    shortDesc: '打造永不塌房的AI品牌大使。',
    logo: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/AIShuZhiDaiYanRen/Logo.jpg',
    link: 'https://zl.sdtaa.com/card/ai-sz-agent',
    hasDetails: false,
    icon: Avatar
  },
  {
    id: 'ai-marketing-expert',
    name: '智链 · AI营销专家',
    shortDesc: '赋能团队高效地找到精准客户。',
    logo: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/STDAA/zl.jpg',
    link: 'https://zl.sdtaa.com/card/zhi-lian',
    hasDetails: true,
    icon: ChatLineRound,
    details: {
      title: '帮助企业更好地找到客户',
      content: [
        {
          subtitle: '核心价值：',
          text: '我们致力于通过AI技术，赋能您的企业营销工作，即使非专业营销人员也能高效开展专业营销任务。'
        },
        {
          subtitle: '解决场景：',
          text: '当您的企业拥有了专业的展示窗口（如企业AI名片）后，如何更有效地策划营销活动、创作优质内容、触达潜在客户，成为了新的挑战。'
        },
        {
          subtitle: '我们提供三层AI赋能支持：',
          list: [
            '战略层： 辅助进行市场分析与企业定位，提供营销策略建议。',
            '战术层： 协助优化营销工作流程，提供多渠道内容策略与方法指导。',
            '执行层： 提供内容创作、活动策划等方面的AI智能体辅助工具。'
          ]
        },
        {
          subtitle: '最终目标：',
          text: '全方位降低营销专业门槛，提升团队整体营销能力与获客效率。'
        }
      ]
    }
  },
  {
    id: 'knowdo-ai',
    name: '知行AI (KnowDo AI)',
    shortDesc: '让AI从知识到行动，一步到位。',
    logo: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/STDAA/zxai.jpg',
    link: 'https://knowdo.sdtaa.com/auth',
    hasDetails: true,
    icon: Reading,
    details: {
      title: '让AI真正用起来',
      videoUrl: 'https://pic.sdtaa.com/ZhiLian/Video/Enterprise/ZhiLian/ZXAI1.mp4',
      content: [
        {
          subtitle: '核心定位：',
          text: '一个专为企业及公共部门设计的，实现"学、练、用"深度融合的AI赋能平台。'
        },
        {
          subtitle: '解决痛点：',
          text: '有效弥合AI知识学习与实际工作应用之间的巨大鸿沟，解决"学了不会用"的普遍难题。'
        },
        {
          subtitle: '平台核心特色：',
          list: [
            '体系化课程： 提供从AI通识到高频工作场景应用的完整学习路径。',
            '智能化学习支持： 配备AI伴学助手和AI老师，提供7x24小时个性化辅导与互动答疑。',
            '场景化沉浸体验： 提供"课程+工具+模板+智能体"的一站式资源，拿来即用。'
          ]
        },
        {
          subtitle: '最终价值：',
          text: '是企业融入AI时代，系统性提升组织AI应用能力，将AI转化为生产力的关键第一步。'
        }
      ]
    }
  },
  {
    id: 'shangrao-edu-map',
    name: '上饶市教育地图',
    shortDesc: '县域AI教育智慧服务平台。',
    logo: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/JiaoYuDiTu/LOGO.jpg',
    link: 'https://yu.sdtaa.com/',
    hasDetails: true,
    icon: School,
    details: {
      title: '重构县域教育新生态',
      content: [
        {
          subtitle: '核心定位：',
          text: '一个基于AI技术的教育服务平台，旨在解决家长不知如何科学育儿、教培机构招生难的核心痛点。'
        },
        {
          subtitle: '我们如何连接教育三方：',
          list: [
            '面向家长： 我们提供专业的AI政策解读、学生能力评估、个性化资源推荐，以及"饶小育AI智能助手"解答各类育儿困惑。',
            '面向教培机构： 我们通过平台汇聚的精准用户，为机构提供潜在客户资源，并提供AI销售客服等工具提升转化效率。'
          ]
        },
        {
          subtitle: '核心驱动力：',
          text: '我们以免费的AI科普培训课程作为切入点，吸引并服务广大家长和学生，为合作的教育机构输送精准流量，构建高效便捷的区域教育新生态。'
        }
      ]
    }
  }
])

// 控制详情弹窗
const showDetails = ref(false)
const currentProduct = ref<Product | null>(null)

const openDetails = (product: Product) => {
  currentProduct.value = product
  showDetails.value = true
}

const closeDetails = () => {
  showDetails.value = false
}

// 打开外部链接
const openExternalLink = (url: string) => {
  window.open(url, '_blank')
}

onMounted(() => {
  document.title = '杭州智衍星辰科技 - 产品中心'
  const link = document.querySelector("link[rel~='icon']") as HTMLLinkElement
  if (link) {
    link.href = 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiYanXingChen/Logo.jpg'
  } else {
    const newLink = document.createElement('link')
    newLink.rel = 'icon'
    newLink.href = 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiYanXingChen/Logo.jpg'
    document.head.appendChild(newLink)
  }
})
</script>

<template>
  <div class="view-container">
    <div class="header">
      <el-button type="text" @click="goBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <h1>产品中心</h1>
    </div>

    <div class="content">
      <div class="products-intro">
        <h2 class="section-title">我们的产品</h2>
        <p class="section-desc">智能化解决方案，为您的业务赋能</p>
      </div>
      
      <div class="products-grid">
        <div v-for="product in products" :key="product.id" class="product-card">
          <div class="product-content">
            <div class="product-logo">
              <img :src="product.logo" :alt="product.name" class="logo-image">
            </div>
            <div class="product-info">
              <h3 class="product-name">{{ product.name }}</h3>
              <p class="product-desc">{{ product.shortDesc }}</p>
            </div>
          </div>
          <div class="product-actions">
            <el-button 
              v-if="product.hasDetails" 
              type="primary" 
              class="action-btn details-btn"
              @click="openDetails(product)"
            >
              <el-icon><View /></el-icon>
              查看详情
            </el-button>
            <el-button 
              v-if="product.link" 
              type="primary" 
              class="action-btn link-btn"
              @click="openExternalLink(product.link)"
            >
              <el-icon><Link /></el-icon>
              访问产品
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 产品详情弹窗 -->
    <div class="product-details-modal" v-if="showDetails && currentProduct && currentProduct.details" :class="{ 'show': showDetails }">
      <div class="modal-backdrop" @click="closeDetails"></div>
      <div class="modal-content">
        <div class="modal-header">
          <h3 class="modal-title">{{ currentProduct.details.title }}</h3>
          <el-button type="text" @click="closeDetails" class="close-btn">
            <el-icon><Close /></el-icon>
          </el-button>
        </div>

        <div class="modal-body">
          <!-- 视频部分 -->
          <div v-if="currentProduct.details.videoUrl" class="video-container">
            <video controls class="product-video">
              <source :src="currentProduct.details.videoUrl" type="video/mp4">
              您的浏览器不支持视频播放
            </video>
          </div>

          <!-- 内容部分 -->
          <div class="details-content">
            <div v-for="(section, index) in currentProduct.details.content" :key="index" class="detail-section">
              <h4 v-if="section.subtitle" class="detail-subtitle">{{ section.subtitle }}</h4>
              <p v-if="section.text" class="detail-text">{{ section.text }}</p>
              <ul v-if="section.list" class="detail-list">
                <li v-for="(item, i) in section.list" :key="i">{{ item }}</li>
              </ul>
            </div>
          </div>

          <!-- 链接按钮 -->
          <div v-if="currentProduct.link" class="modal-actions">
            <el-button type="primary" class="visit-btn" @click="openExternalLink(currentProduct.link)">
              <el-icon><Link /></el-icon>
              访问产品
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f9ff;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  min-width: 100vw;
  box-sizing: border-box;
}

.header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, #0D47A1, #2196F3);
  color: white;
  height: 3rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 100;
  box-sizing: border-box;
}

.back-button {
  color: white;
  margin-right: 1rem;
  font-size: 1.25rem;
  padding: 0.5rem;
  position: absolute;
  left: 0.5rem;
  z-index: 1;
}

.header h1 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  width: 100%;
  text-align: center;
}

.content {
  flex: 1;
  padding-top: 4.5rem;
  padding-bottom: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
  padding-left: 1rem;
  padding-right: 1rem;
}

.products-intro {
  text-align: center;
  margin-bottom: 2rem;
}

.section-title {
  font-size: 1.8rem;
  color: #0D47A1;
  margin: 0 0 0.5rem 0;
  font-weight: 600;
}

.section-desc {
  font-size: 1.1rem;
  color: #1976D2;
  margin: 0;
  opacity: 0.8;
}

.products-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

.product-card {
  background: white;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  display: flex;
  flex-direction: column;
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(25, 118, 210, 0.15);
}

.product-content {
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1.25rem;
  flex: 1;
}

.product-logo {
  width: 80px;
  height: 80px;
  border-radius: 0.75rem;
  overflow: hidden;
  flex-shrink: 0;
  border: 1px solid rgba(25, 118, 210, 0.1);
}

.logo-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-info {
  flex: 1;
  position: relative;
}

.product-name {
  font-size: 1.25rem;
  color: #0D47A1;
  margin: 0 0 0.5rem 0;
  font-weight: 600;
}

.product-desc {
  font-size: 0.95rem;
  color: #555;
  margin: 0;
  line-height: 1.5;
}

.product-actions {
  display: flex;
  gap: 0.75rem;
  padding: 0 1.5rem 1.5rem 1.5rem;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  border-radius: 0.5rem;
  padding: 0.6rem 1rem;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.details-btn {
  background: rgba(25, 118, 210, 0.1);
  color: #1976D2;
  border: 1px solid rgba(25, 118, 210, 0.2);
}

.details-btn:hover {
  background: rgba(25, 118, 210, 0.15);
  border-color: rgba(25, 118, 210, 0.3);
}

.link-btn {
  background: linear-gradient(135deg, #0D47A1, #2196F3);
  border: none;
  color: white;
}

.link-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
}

/* 产品详情弹窗样式 */
.product-details-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.product-details-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(3px);
}

.modal-content {
  position: relative;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  background: white;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  animation: modal-in 0.3s ease forwards;
}

@keyframes modal-in {
  from {
    transform: translateY(30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.modal-header {
  padding: 1.25rem 1.5rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-title {
  margin: 0;
  font-size: 1.4rem;
  color: #0D47A1;
  font-weight: 600;
}

.close-btn {
  font-size: 1.5rem;
  color: #666;
  padding: 0.5rem;
}

.modal-body {
  padding: 1.5rem;
  overflow-y: auto;
  flex: 1;
}

.video-container {
  margin-bottom: 1.5rem;
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.product-video {
  width: 100%;
  display: block;
}

.details-content {
  padding: 0.5rem 0;
}

.detail-section {
  margin-bottom: 1.5rem;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.detail-subtitle {
  font-size: 1.1rem;
  color: #0D47A1;
  margin: 0 0 0.75rem 0;
  font-weight: 600;
}

.detail-text {
  font-size: 1rem;
  color: #333;
  line-height: 1.6;
  margin: 0 0 1rem 0;
}

.detail-list {
  padding-left: 1.5rem;
  margin: 0.5rem 0 1rem 0;
}

.detail-list li {
  margin-bottom: 0.5rem;
  line-height: 1.5;
  color: #333;
}

.modal-actions {
  margin-top: 1.5rem;
  display: flex;
  justify-content: center;
}

.visit-btn {
  background: linear-gradient(135deg, #0D47A1, #2196F3);
  border: none;
  color: white;
  padding: 0.75rem 2rem;
  border-radius: 2rem;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1rem;
  font-weight: 500;
  box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
  transition: all 0.3s ease;
}

.visit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(33, 150, 243, 0.4);
}

/* 媒体查询 - 平板和桌面端 */
@media (min-width: 768px) {
  .content {
    padding-left: 2rem;
    padding-right: 2rem;
  }
  
  .products-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .product-logo {
    width: 100px;
    height: 100px;
  }
  
  .product-name {
    font-size: 1.4rem;
  }
  
  .product-desc {
    font-size: 1rem;
  }
  
  .action-btn {
    font-size: 1rem;
    padding: 0.75rem 1.25rem;
  }
  
  .modal-content {
    width: 80%;
  }
}

/* 媒体查询 - 大屏幕 */
@media (min-width: 1024px) {
  .products-grid {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .modal-content {
    width: 70%;
  }
}
</style> 