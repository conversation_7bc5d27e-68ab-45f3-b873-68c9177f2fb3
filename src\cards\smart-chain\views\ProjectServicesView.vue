<script setup lang="ts">
import { useRouter } from 'vue-router'
import {
  ArrowLeft,
  ArrowRight,
  Link as LinkIcon
} from '@element-plus/icons-vue'
import { onMounted, ref } from 'vue'
import TabBar from '../components/TabBar.vue'

const router = useRouter()

const goBack = () => {
  router.push('/card/smart-chain')
}

// 弹窗控制
const dialogVisible = ref(false)
const currentProduct = ref<any>(null)

// 产品数据
const products = ref([
  {
    id: 'qiye-ai-mingpian',
    name: '企业AI宣传官',
    description: '打造7x24小时在线的智能门面。',
    logo: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/qiyeAImingpianLogo.jpg',
    link: 'https://zl.sdtaa.com/card/qiye-ai-mingpian',
    hasDetail: false
  },
  {
    id: 'ai-sz-guide',
    name: 'AI数智推介官',
    description: '政府智慧推介与服务的新范式。',
    logo: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/AIShuZhiTuiJieGuan/LOGO.jpeg',
    link: 'https://zl.sdtaa.com/card/ai-sz-guide',
    hasDetail: false
  },
  {
    id: 'product-ai-sales',
    name: '产品AI推销官',
    description: '产品AI推销官，让您的产品自己会说话！',
    logo: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ChanPinAITuiXiaoGuan/LOGO.png',
    link: 'https://zl.sdtaa.com/card/product-ai-sales',
    hasDetail: false
  },
  {
    id: 'ai-sz-agent',
    name: 'AI数智代言人',
    description: '打造永不塌房的AI品牌大使。',
    logo: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/AIShuZhiDaiYanRen/Logo.jpg',
    link: 'https://zl.sdtaa.com/card/ai-sz-agent',
    hasDetail: false
  },
  {
    id: 'ai-marketing',
    name: '智链·AI营销专家',
    description: '赋能团队高效地找到精准客户。',
    logo: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/STDAA/zl.jpg',
    link: null,
    hasDetail: true,
    detail: {
      title: '帮助企业更好地找到客户',
      coreValue: '我们致力于通过AI技术，赋能您的企业营销工作，即使非专业营销人员也能高效开展专业营销任务。',
      scenario: '当您的企业拥有了专业的展示窗口（如企业AI名片）后，如何更有效地策划营销活动、创作优质内容、触达潜在客户，成为了新的挑战。',
      features: [
        { level: '战略层', description: '辅助进行市场分析与企业定位，提供营销策略建议。' },
        { level: '战术层', description: '协助优化营销工作流程，提供多渠道内容策略与方法指导。' },
        { level: '执行层', description: '提供内容创作、活动策划等方面的AI智能体辅助工具。' }
      ],
      goal: '全方位降低营销专业门槛，提升团队整体营销能力与获客效率。'
    }
  }
])

// 处理卡片点击
const handleCardClick = (product: any) => {
  if (product.hasDetail) {
    currentProduct.value = product
    dialogVisible.value = true
  } else if (product.link) {
    window.open(product.link, '_blank')
  }
}

// 访问外部链接
const openLink = (url: string) => {
  window.open(url, '_blank')
}

onMounted(() => {
  document.title = '智链 - 项目服务'
})
</script>

<template>
  <div class="view-container">
    <div class="header">
      <el-button type="text" @click="goBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <h1>项目服务</h1>
    </div>

    <div class="content">
      <div class="services-grid">
        <div
          v-for="product in products"
          :key="product.id"
          class="service-card"
          @click="handleCardClick(product)"
        >
          <div class="card-content">
            <div class="logo-section">
              <img :src="product.logo" :alt="product.name" class="service-logo" />
            </div>
            <div class="info-section">
              <h3 class="service-name">{{ product.name }}</h3>
              <p class="service-description">{{ product.description }}</p>
              <div class="action-section">
                <div class="action-button" v-if="product.link || product.hasDetail">
                  <span class="action-text">
                    {{ product.hasDetail ? '查看详情' : '访问' }}
                  </span>
                  <el-icon class="action-icon"><ArrowRight /></el-icon>
                </div>
                <div class="action-button disabled" v-else>
                  <span class="action-text">即将推出</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 产品详情弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="currentProduct?.name"
      width="800px"
      class="product-dialog"
      center
      :close-on-click-modal="true"
      :close-on-press-escape="true"
    >
      <div v-if="currentProduct?.detail" class="dialog-content">
        <h2 class="detail-title">{{ currentProduct.detail.title }}</h2>

        <!-- 视频部分 -->
        <div v-if="currentProduct.detail.video" class="video-section">
          <video
            :src="currentProduct.detail.video"
            controls
            class="product-video"
            preload="metadata"
          >
            您的浏览器不支持视频播放。
          </video>
        </div>

        <!-- 访问链接 -->
        <div v-if="currentProduct.detail.accessLink" class="access-section">
          <el-button
            type="primary"
            @click="openLink(currentProduct.detail.accessLink)"
            class="access-button"
          >
            <el-icon><LinkIcon /></el-icon>
            立即访问
          </el-button>
        </div>

        <!-- 核心价值/定位 -->
        <div v-if="currentProduct.detail.coreValue" class="section">
          <h3>核心价值</h3>
          <p>{{ currentProduct.detail.coreValue }}</p>
        </div>

        <div v-if="currentProduct.detail.positioning" class="section">
          <h3>核心定位</h3>
          <p>{{ currentProduct.detail.positioning }}</p>
        </div>

        <!-- 解决场景/痛点 -->
        <div v-if="currentProduct.detail.scenario" class="section">
          <h3>解决场景</h3>
          <p>{{ currentProduct.detail.scenario }}</p>
        </div>

        <div v-if="currentProduct.detail.painPoint" class="section">
          <h3>解决痛点</h3>
          <p>{{ currentProduct.detail.painPoint }}</p>
        </div>

        <!-- 功能特色 -->
        <div v-if="currentProduct.detail.features" class="section">
          <h3>{{ currentProduct.id === 'ai-marketing' ? '我们提供三层AI赋能支持' : '平台核心特色' }}</h3>
          <div class="features-list">
            <div
              v-for="(feature, index) in currentProduct.detail.features"
              :key="index"
              class="feature-item"
            >
              <div class="feature-header">
                <span class="feature-name">{{ feature.level || feature.name }}：</span>
              </div>
              <p class="feature-desc">{{ feature.description }}</p>
            </div>
          </div>
        </div>

        <!-- 最终目标/价值 -->
        <div v-if="currentProduct.detail.goal" class="section">
          <h3>最终目标</h3>
          <p>{{ currentProduct.detail.goal }}</p>
        </div>

        <div v-if="currentProduct.detail.value" class="section">
          <h3>最终价值</h3>
          <p>{{ currentProduct.detail.value }}</p>
        </div>
      </div>

      <template #footer>
        <el-button @click="dialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>

    <TabBar />
  </div>
</template>

<style scoped>
.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8fafc;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  min-width: 100vw;
  box-sizing: border-box;
}

.header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, #1976d2, #42a5f5);
  color: white;
  height: 3rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 100;
  box-sizing: border-box;
}

.back-button {
  color: white;
  margin-right: 1rem;
  font-size: 1.25rem;
  padding: 0.5rem;
  position: absolute;
  left: 0.5rem;
  z-index: 1;
}

.header h1 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  width: 100%;
  text-align: center;
}

.content {
  flex: 1;
  padding-top: 4.5rem;
  padding-bottom: 5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
  padding-left: 1rem;
  padding-right: 1rem;
}

.services-grid {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.service-card {
  background: white;
  border-radius: 1rem;
  padding: 1.25rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(25, 118, 210, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
}

.service-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(25, 118, 210, 0.15);
  border-color: rgba(25, 118, 210, 0.2);
}

.card-content {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.logo-section {
  flex-shrink: 0;
}

.service-logo {
  width: 3rem;
  height: 3rem;
  border-radius: 0.5rem;
  object-fit: cover;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.info-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 3rem;
}

.service-name {
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  line-height: 1.3;
}

.service-description {
  margin: 0 0 1rem 0;
  font-size: 0.95rem;
  line-height: 1.4;
  color: #666;
  flex: 1;
}

.action-section {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 1.5rem;
  font-size: 0.85rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.action-button:not(.disabled) {
  background: linear-gradient(135deg, #1976d2, #42a5f5);
  color: white;
}

.action-button:not(.disabled):hover {
  background: linear-gradient(135deg, #1565c0, #1976d2);
  transform: translateX(-2px);
}

.action-button.disabled {
  background: #f5f5f5;
  color: #999;
  cursor: not-allowed;
}

.action-text {
  font-size: 0.85rem;
}

.action-icon {
  font-size: 0.9rem;
  transition: transform 0.3s ease;
}

.action-button:not(.disabled):hover .action-icon {
  transform: translateX(2px);
}

/* 弹窗样式 */
:deep(.product-dialog) {
  border-radius: 1rem;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  margin: 0 !important;
}

:deep(.product-dialog .el-dialog) {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  margin: 0 !important;
  max-height: 80vh;
  overflow: hidden;
  border-radius: 1rem;
}

:deep(.product-dialog .el-dialog__header) {
  background: linear-gradient(135deg, #1976d2, #42a5f5);
  color: white;
  padding: 1rem 1.5rem;
  border-radius: 1rem 1rem 0 0;
  flex-shrink: 0;
}

:deep(.product-dialog .el-dialog__title) {
  color: white;
  font-weight: 600;
}

:deep(.product-dialog .el-dialog__headerbtn .el-dialog__close) {
  color: white;
}

:deep(.product-dialog .el-dialog__body) {
  padding: 1.5rem;
  max-height: calc(80vh - 120px);
  overflow-y: auto;
  flex: 1;
}

:deep(.product-dialog .el-dialog__footer) {
  padding: 1rem 1.5rem;
  border-top: 1px solid #e3f2fd;
  flex-shrink: 0;
}

.dialog-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.detail-title {
  margin: 0;
  font-size: 1.4rem;
  font-weight: 600;
  color: #333;
  text-align: center;
  padding-bottom: 1rem;
  border-bottom: 2px solid #e3f2fd;
}

.video-section {
  display: flex;
  justify-content: center;
}

.product-video {
  width: 100%;
  max-width: 600px;
  height: auto;
  border-radius: 0.75rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.access-section {
  display: flex;
  justify-content: center;
}

.access-button {
  background: linear-gradient(135deg, #1976d2, #42a5f5);
  border: none;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  border-radius: 2rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 4px 15px rgba(25, 118, 210, 0.3);
  transition: all 0.3s ease;
}

.access-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(25, 118, 210, 0.4);
}

.section {
  background: #f8fbff;
  border-radius: 0.75rem;
  padding: 1.25rem;
  border-left: 4px solid #1976d2;
}

.section h3 {
  margin: 0 0 0.75rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1976d2;
}

.section p {
  margin: 0;
  font-size: 0.95rem;
  line-height: 1.5;
  color: #555;
}

.features-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.feature-item {
  background: white;
  border-radius: 0.5rem;
  padding: 1rem;
  border: 1px solid rgba(25, 118, 210, 0.1);
}

.feature-header {
  margin-bottom: 0.5rem;
}

.feature-name {
  font-weight: 600;
  color: #1976d2;
  font-size: 0.95rem;
}

.feature-desc {
  margin: 0;
  font-size: 0.9rem;
  line-height: 1.4;
  color: #666;
}

/* 移动端弹窗适配 */
@media (max-width: 767px) {
  :deep(.product-dialog) {
    width: 95vw !important;
    max-width: 95vw !important;
  }

  :deep(.product-dialog .el-dialog) {
    width: 95vw !important;
    max-width: 95vw !important;
    max-height: 85vh;
  }

  :deep(.product-dialog .el-dialog__body) {
    max-height: calc(85vh - 120px);
    padding: 1rem;
  }

  :deep(.product-dialog .el-dialog__header) {
    padding: 1rem;
  }

  :deep(.product-dialog .el-dialog__footer) {
    padding: 1rem;
  }
}

/* 响应式设计 */
@media (min-width: 768px) {
  .content {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .services-grid {
    gap: 1.5rem;
  }

  .service-card {
    padding: 1.5rem;
  }

  .service-logo {
    width: 4rem;
    height: 4rem;
  }

  .service-name {
    font-size: 1.2rem;
  }

  .service-description {
    font-size: 1rem;
  }

  .action-button {
    padding: 0.6rem 1.2rem;
    font-size: 0.9rem;
  }

  .detail-title {
    font-size: 1.6rem;
  }

  .section {
    padding: 1.5rem;
  }

  .section h3 {
    font-size: 1.2rem;
  }

  .section p {
    font-size: 1rem;
  }
}

@media (min-width: 1024px) {
  .services-grid {
    gap: 2rem;
  }

  .service-card {
    padding: 2rem;
  }

  .card-content {
    gap: 1.5rem;
  }

  .service-logo {
    width: 4.5rem;
    height: 4.5rem;
  }
}
</style>
