<script setup lang="ts">
</script>

<template>
  <div class="digital-human">
    <div class="image-container">
      <img 
        src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/ZLSZR.png"
        alt="数字人形象" 
        class="digital-human-image"
      >
    </div>
  </div>
</template>

<style scoped>
.digital-human {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.image-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.digital-human-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

@media (min-width: 768px) {
  .digital-human-image {
    object-position: center 10%;
  }
}
</style>