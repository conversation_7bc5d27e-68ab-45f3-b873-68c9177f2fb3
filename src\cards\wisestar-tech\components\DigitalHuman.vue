<script setup lang="ts">
import { ref, onMounted } from 'vue'

const backgroundImage = ref('https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/ZLSZR.png')
const isMobile = ref(true)

onMounted(() => {
  // 检查是否为桌面端
  const checkDevice = () => {
    isMobile.value = window.innerWidth < 768
    backgroundImage.value = isMobile.value 
      ? 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/ZLSZR.png'
      : 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/ZLSZR-PC.png'
  }
  
  // 初始检查
  checkDevice()
  
  // 监听窗口大小变化
  window.addEventListener('resize', checkDevice)
  
  // 组件卸载时移除事件监听
  return () => {
    window.removeEventListener('resize', checkDevice)
  }
})
</script>

<template>
  <div class="digital-human">
    <img :src="backgroundImage" alt="数字人形象" class="background-image">
  </div>
</template>

<style scoped>
.digital-human {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
}

.background-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

@media (min-width: 768px) {
  .background-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
  }
  
  .digital-human {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style> 