import { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'shidaErbaoHome',
    component: () => import('./views/HomeView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ShiDaErBao/Logo.jpg',
      title: '师大儿保托育中心'
    }
  },
  {
    path: '/center-intro',
    name: 'shidaErbaoCenterIntro',
    component: () => import('./views/CenterIntroView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ShiDaErBao/Logo.jpg',
      title: '中心介绍 - 师大儿保托育中心'
    }
  },
  {
    path: '/courses',
    name: 'shidaErbaoCourses',
    component: () => import('./views/CoursesView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ShiDaErBao/Logo.jpg',
      title: '课程介绍 - 师大儿保托育中心'
    }
  },
  {
    path: '/contact',
    name: 'shidaErbaoContact',
    component: () => import('./views/ContactView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ShiDaErBao/Logo.jpg',
      title: '联系我们 - 师大儿保托育中心'
    }
  }
]

export default routes 