<script setup lang="ts">
import { useRouter } from 'vue-router'
import { ArrowLeft, ArrowRight, Phone, Message, Close } from '@element-plus/icons-vue'
import { onMounted, ref } from 'vue'


const router = useRouter()

const goBack = () => {
  router.push('/card/qiye-ai-mingpian')
}

// 联系弹窗
const contactDialogVisible = ref(false)

const openContact = () => {
  contactDialogVisible.value = true
}

const closeContactDialog = () => {
  contactDialogVisible.value = false
}

const chatWithAI = () => {
  // 打开AI宣传员聊天链接
  window.open('https://ai.sdtaa.com:3105/chat/share?shareId=dhjfvgns2hp8e9zg3m3huvxe', '_blank')
}

// 跳转到案例中心
const goToCaseCenter = () => {
  router.push('/card/qiye-ai-mingpian/case-center')
}

// 核心价值数据
const coreValues = [
  {
    title: '提升销售转化率 📈',
    content: '通过专业的第一印象与7x24小时智能互动，捕捉每一个潜在意向。AI宣传员将基础咨询转化为意向挖掘，有效提升从了解到成交的转化效率。'
  },
  {
    title: '降低沟通成本 💰',
    content: '将销售团队从"复读机"式的重复性基础问答中解放出来。让AI处理80%的标准化咨询，让人力聚焦于最高价值的客户攻坚与签单环节。'
  },
  {
    title: '赋能销售团队 👥',
    content: '将金牌销售的知识与经验沉淀为AI知识库，确保每一次对外沟通都保持最高专业水准。新人也能即刻拥有专家级知识储备，团队整体战斗力倍增。'
  }
]

// 痛点解决方案
const painSolutions = [
  {
    pain: '转化率低？',
    solution: 'AI精准互动，深挖意向，不错过任何商机。'
  },
  {
    pain: '成本高昂？',
    solution: 'AI 7x24小时自动应答，解放核心人力。'
  },
  {
    pain: '能力不均？',
    solution: 'AI标准化金牌话术，团队战力瞬间拉满。'
  }
]

// 核心模块
const coreModules = [
  {
    title: 'AI宣传员 (核心引擎)',
    content: '一位懂业务、会思考的"高级销售顾问"，能实现"千人千面"的初步线索筛选与意向引导。'
  },
  {
    title: 'AI宣传视频 (形象利器)',
    content: '运用AI数智人技术快速生成专业宣传视频，让您的第一印象脱颖而出。'
  },
  {
    title: 'AI网页门户 (线上展厅)',
    content: '一个结构化、高颜值的线上信息中心，打造永不落幕的线上展厅。'
  }
]

// 比较优势
const comparisons = [
  {
    title: '相比"公司官网"，我们：',
    advantages: ['更便携', '可互动', '强引导', '随时分享']
  },
  {
    title: '相比"发送文件(PPT/PDF)"，我们：',
    advantages: ['一个入口', '云端同步', '永不过期', '体验流畅']
  },
  {
    title: '相比"其他电子名片"，我们：',
    advantages: ['AI大脑', '主动营销', '智能问答', '深度交互']
  }
]

// 服务方案
const servicePlans = [
  {
    title: '基础版',
    features: [
      'AI智能宣传员',
      'AI网页门户',
      '微信小程序载体',
      '个人名片编辑与分享',
      '团队后台管理'
    ],
    price: '¥ 6000 /年'
  },
  {
    title: '标准版 (含视频)',
    features: [
      '包含基础版全部功能，并增加：',
      'AI数智人宣传视频（1个）'
    ],
    price: '¥ 8000/年'
  },
  {
    title: '高级版 (企业定制)',
    features: [
      '针对更复杂的业务需求，我们提供深度定制服务，打造您的专属解决方案，包括但不限于：',
      '高级语音交互',
      '实时数字人交互',
      '线下一体化大屏部署',
      '电影级宣传视频',
      '品牌化网页深度定制'
    ],
    price: '按需定制，请洽谈'
  }
]

// 主要服务对象
const serviceTargets = [
  {
    type: '企业客户 (企业AI宣传官)',
    description: '特别是科技、咨询、金融、教育、知产等产品/服务体系复杂的ToB企业。'
  },
  {
    type: '政府与公共机构 (AI数智推介官)',
    description: '包括负责招商引资、文旅推广、政策宣传、人才服务的各级单位。'
  }
]

onMounted(() => {
  document.title = '企业AI宣传官 - 产品介绍'
  const link = document.querySelector('link[rel="icon"]') as HTMLLinkElement || document.createElement('link')
  link.type = 'image/jpeg'
  link.rel = 'icon'
  link.href = 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/qiyeAImingpianLogo.jpg'
  document.head.appendChild(link)
})
</script>

<template>
  <div class="view-container">
    <div class="header">
      <el-button type="text" @click="goBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <h1>产品介绍</h1>
    </div>

    <div class="content">
      <div class="product-intro-container">
        <!-- 板块一：开启AI营销新纪元 -->
        <section class="section hero-section">
          <h2>开启AI营销新纪元</h2>
          <p class="main-title">为您的企业，配备一位永不离线的AI销售专家</p>
          <p class="tagline">企业AI宣传官：将每一次对外接触，都转化为一次精准的营销互动。</p>
        </section>

        <!-- 核心宣传视频 -->
        <section class="section video-section">
          <div class="video-container">
            <video controls>
              <source src="https://pic.sdtaa.com/ZhiLian/Video/Enterprise/ZhiLian/QiYeAIMingPian.mp4" type="video/mp4">
              您的浏览器不支持视频播放。
            </video>
          </div>
          <p class="video-caption">▶ 点击播放，见证AI如何重塑企业沟通</p>
        </section>
        
        <!-- 板块二：三大核心价值 -->
        <section class="section core-values-section">
          <h3>我们为您创造的三大核心价值</h3>
          
          <div class="values-grid">
            <div class="value-card" v-for="(value, index) in coreValues" :key="index">
              <h4>价值{{ index + 1 }}：{{ value.title }}</h4>
              <p>{{ value.content }}</p>
            </div>
          </div>
        </section>

        <!-- 板块三：痛点解决 -->
        <section class="section pain-solutions-section">
          <h3>这些痛点，我们逐一击破</h3>
          
          <div class="pain-solutions">
            <div class="pain-solution-item" v-for="(item, index) in painSolutions" :key="index">
              <div class="pain">{{ item.pain }}</div>
              <div class="arrow">➡️</div>
              <div class="solution">{{ item.solution }}</div>
            </div>
          </div>
        </section>

        <!-- 板块四：核心模块 -->
        <section class="section core-modules-section">
          <h3>三大核心模块，构成您的专属AI营销引擎</h3>
          
          <div class="modules-grid">
            <div class="module-card" v-for="(module, index) in coreModules" :key="index">
              <div class="module-icon">模块{{ index + 1 }}</div>
              <h4>{{ module.title }}</h4>
              <p>{{ module.content }}</p>
            </div>
          </div>
        </section>

        <!-- 板块五：比较优势 -->
        <section class="section comparison-section">
          <h3>为何我们是更好的选择？</h3>
          
          <div class="comparisons">
            <div class="comparison-item" v-for="(comparison, index) in comparisons" :key="index">
              <h4>{{ comparison.title }}</h4>
              <div class="advantages-cloud">
                <span class="advantage-tag" v-for="(advantage, i) in comparison.advantages" :key="i">
                  {{ advantage }}
                </span>
              </div>
            </div>
          </div>
          
          <div class="summary">
            <p>我们不是替代品，而是通过AI，将所有传统方式的优点融为一体的一站式智能营销终端。</p>
          </div>
        </section>

        <!-- 板块六：服务方案 -->
        <section class="section plans-section">
          <h3>我们的服务方案</h3>
          
          <div class="plans-grid">
            <div class="plan-card" v-for="(plan, index) in servicePlans" :key="index">
              <h4>{{ plan.title }}</h4>
              <ul class="plan-features">
                <li v-for="(feature, i) in plan.features" :key="i">{{ feature }}</li>
              </ul>
              <div class="plan-price">价格：{{ plan.price }}</div>
            </div>
          </div>
        </section>

        <!-- 板块七：客户信赖 -->
        <section class="section clients-section">
          <h3>深受各行各业信赖</h3>
          <p>我们致力于通过AI技术为各行各业的组织赋能，已获得众多行业领先者的认可。</p>
          
          <div class="service-targets">
            <div class="target-item" v-for="(target, index) in serviceTargets" :key="index">
              <h4>{{ target.type }}</h4>
              <p>{{ target.description }}</p>
            </div>
          </div>
          
          <div class="logo-wall">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/logoQiang.jpg" alt="合作伙伴Logo墙">
          </div>
          
          <div class="case-link">
            <a href="javascript:void(0);" @click="goToCaseCenter" class="link-with-arrow">
              查看更多客户成功案例
              <el-icon><ArrowRight /></el-icon>
            </a>
          </div>
        </section>

        <!-- 板块八：开启流程 -->
        <section class="section process-section">
          <h3>两步开启，后顾无忧</h3>
          
          <div class="process-flow">
            <div class="process-step">
              <div class="step-number">1</div>
              <div class="step-content">
                <h4>您提供现有资料</h4>
                <p>只需将您现有的宣传册、PPT、产品文档等打包给我们</p>
              </div>
            </div>
            
            <div class="process-arrow">
              <el-icon><ArrowRight /></el-icon>
            </div>
            
            <div class="process-step">
              <div class="step-number">2</div>
              <div class="step-content">
                <h4>我们为您打造一切</h4>
                <p>从AI训练、视频生成到门户设计，我们全流程服务，您只需验收</p>
              </div>
            </div>
          </div>
          
          <div class="process-promise">
            <div class="highlight-promise">
              <p>"把<span class="emphasis">麻烦</span>留给我们，把<span class="emphasis">简单</span>留给您。"</p>
            </div>
          </div>
        </section>

        <!-- 板块九：行动号召 -->
        <section class="section cta-section">
          <h3>立即行动，体验未来的营销方式</h3>
          
          <div class="cta-buttons">
            <el-button type="primary" size="large" @click="openContact" class="cta-button primary">
              <el-icon><Phone /></el-icon>
              预约专属方案演示
            </el-button>
            
            <el-button size="large" @click="chatWithAI" class="cta-button secondary">
              <el-icon><Message /></el-icon>
              与我的AI宣传员聊聊
            </el-button>
          </div>
        </section>
      </div>
    </div>

    <!-- 联系弹窗 -->
    <div class="contact-dialog-overlay" v-if="contactDialogVisible" @click="closeContactDialog">
      <div class="contact-dialog" @click.stop>
        <div class="dialog-header">
          <h4>预约专属方案演示</h4>
          <el-button type="text" @click="closeContactDialog" class="close-button">
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
        <div class="dialog-content">
          <p class="dialog-text">扫描下方二维码，添加客服微信，获取更多产品信息和专属优惠</p>
          <div class="qrcode-container">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Project/contact.png" alt="联系方式二维码">
          </div>
          <p class="dialog-tips">微信咨询: <strong>扫码立即咨询</strong></p>
          <p class="dialog-benefits">· 专属顾问1对1服务</p>
          <p class="dialog-benefits">· 免费获取行业解决方案</p>
          <p class="dialog-benefits">· 享受新客户专属优惠</p>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8f9fa;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  min-width: 100vw;
  box-sizing: border-box;
}

.header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, #2980b9, #3498db);
  color: white;
  height: 3rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 100;
  box-sizing: border-box;
}

.back-button {
  color: white;
  margin-right: 1rem;
  font-size: 1.25rem;
  padding: 0.5rem;
  position: absolute;
  left: 0.5rem;
  z-index: 1;
}

.header h1 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  width: 100%;
  text-align: center;
}

.content {
  flex: 1;
  padding-top: 4.5rem;
  padding-bottom: 4.5rem; /* 为底部标签栏留出空间 */
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
  padding-left: 1rem;
  padding-right: 1rem;
}

.product-intro-container {
  width: 100%;
}

.section {
  margin-bottom: 2.5rem;
  padding: 1.5rem;
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

/* 英雄区域样式 */
.hero-section {
  text-align: center;
  background: linear-gradient(135deg, #3498db, #2c3e50);
  color: white;
  padding: 2rem 1.5rem;
}

.hero-section h2 {
  font-size: 2.2rem;
  margin-top: 0;
  margin-bottom: 1.5rem;
  font-weight: 700;
}

.main-title {
  font-size: 1.6rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.tagline {
  font-size: 1.2rem;
  line-height: 1.5;
  margin: 0;
  opacity: 0.9;
}

/* 视频区域样式 */
.video-section {
  text-align: center;
}

.video-container {
  position: relative;
  width: 100%;
  padding-top: 56.25%; /* 16:9 宽高比 */
  overflow: hidden;
  border-radius: 8px;
  margin-bottom: 1rem;
}

.video-container video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.video-caption {
  font-size: 1rem;
  color: #2980b9;
  font-weight: 500;
  margin-top: 1rem;
}

/* 通用标题样式 */
h3 {
  font-size: 1.5rem;
  margin-top: 0;
  margin-bottom: 1.5rem;
  color: #2c3e50;
  text-align: center;
  font-weight: 600;
}

h4 {
  font-size: 1.2rem;
  margin-top: 0;
  margin-bottom: 1rem;
  color: #2980b9;
}

/* 核心价值样式 */
.values-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

.value-card {
  padding: 1.5rem;
  background-color: #f8f9fa;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  border-left: 4px solid #3498db;
}

.value-card p {
  margin: 0;
  line-height: 1.6;
}

/* 痛点解决方案样式 */
.pain-solutions {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.pain-solution-item {
  display: flex;
  align-items: center;
  padding: 1.25rem;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.pain {
  font-weight: 600;
  color: #e74c3c;
  flex: 1;
  min-width: 100px;
}

.arrow {
  margin: 0 1rem;
  font-size: 1.25rem;
}

.solution {
  flex: 3;
  color: #2c3e50;
}

/* 核心模块样式 */
.modules-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

.module-card {
  padding: 1.5rem;
  background-color: #f8f9fa;
  border-radius: 10px;
  border-top: 4px solid #3498db;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.module-icon {
  display: inline-block;
  background-color: #3498db;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: 1rem;
}

.module-card p {
  margin: 0;
  line-height: 1.6;
}

/* 比较优势样式 */
.comparisons {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.comparison-item {
  padding: 1.5rem;
  background-color: #f8f9fa;
  border-radius: 10px;
}

.advantages-cloud {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  margin-top: 1rem;
}

.advantage-tag {
  background-color: #e1f0fa;
  color: #2980b9;
  border-radius: 20px;
  padding: 0.5rem 1rem;
  font-size: 0.95rem;
  font-weight: 500;
}

.summary {
  margin-top: 2rem;
  padding: 1rem;
  background-color: #f1f8ff;
  border-left: 4px solid #3498db;
  border-radius: 0 4px 4px 0;
  text-align: center;
}

.summary p {
  margin: 0;
  font-weight: 500;
  font-size: 1.1rem;
}

/* 服务方案样式 */
.plans-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

.plan-card {
  padding: 1.75rem;
  background-color: #f8f9fa;
  border-radius: 10px;
  border: 1px solid #e0e0e0;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
}

.plan-features {
  list-style-type: none;
  padding-left: 0;
  margin: 1rem 0;
  flex: 1;
}

.plan-features li {
  margin-bottom: 0.75rem;
  padding-left: 1.25rem;
  position: relative;
}

.plan-features li::before {
  content: "•";
  color: #3498db;
  position: absolute;
  left: 0;
  top: 0;
  font-size: 1.2rem;
  line-height: 1;
}

.plan-price {
  margin-top: auto;
  font-weight: 600;
  font-size: 1.1rem;
  color: #e74c3c;
  border-top: 1px dashed #e0e0e0;
  padding-top: 1rem;
}

/* 客户信赖样式 */
.service-targets {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.25rem;
  margin-bottom: 2rem;
}

.target-item {
  padding: 1.25rem;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 3px solid #3498db;
}

.target-item p {
  margin: 0;
}

.logo-wall {
  width: 100%;
  margin: 1.5rem 0;
  overflow: hidden;
  border-radius: 8px;
}

.logo-wall img {
  width: 100%;
  height: auto;
  display: block;
}

.case-link {
  text-align: center;
  margin-top: 1.5rem;
}

.link-with-arrow {
  display: inline-flex;
  align-items: center;
  color: #3498db;
  font-weight: 500;
  text-decoration: none;
  transition: color 0.3s ease;
}

.link-with-arrow:hover {
  color: #2980b9;
}

.link-with-arrow .el-icon {
  margin-left: 0.5rem;
}

/* 流程样式 */
.process-flow {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 2rem 0;
}

.process-step {
  flex: 1;
  display: flex;
  align-items: flex-start;
  background-color: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
}

.step-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: #3498db;
  color: white;
  border-radius: 50%;
  font-size: 1.25rem;
  font-weight: bold;
  margin-right: 1rem;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
}

.step-content h4 {
  margin-top: 0;
}

.process-arrow {
  margin: 0 1rem;
  color: #3498db;
  font-size: 1.5rem;
}

.process-promise {
  text-align: center;
  margin-top: 2rem;
  font-size: 1.2rem;
}

.highlight-promise {
  display: inline-block;
  background: linear-gradient(135deg, rgba(52, 152, 219, 0.1), rgba(41, 128, 185, 0.15));
  padding: 1rem 2rem;
  border-radius: 8px;
  border-left: 4px solid #3498db;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transform: translateY(0);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.highlight-promise:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.08);
}

.highlight-promise p {
  font-style: italic;
  color: #2c3e50;
  font-weight: 500;
  margin: 0;
  font-size: 1.3rem;
}

.emphasis {
  font-weight: 700;
  color: #3498db;
}

/* CTA按钮 */
.cta-buttons {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 1.5rem;
}

.cta-button {
  width: 100%;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cta-button .el-icon {
  margin-right: 0.5rem;
}

.primary {
  background-color: #3498db;
  border-color: #3498db;
}

.secondary {
  color: #3498db;
  border-color: #3498db;
}

/* 覆盖Element Plus默认按钮间距 */
:deep(.el-button + .el-button) {
  margin-left: 0;
}

/* 响应式调整 */
@media (min-width: 768px) {
  .header {
    height: 3.5rem;
  }
  
  .header h1 {
    font-size: 1.2rem;
  }
  
  .content {
    padding-top: 5rem;
  }
  
  .hero-section h2 {
    font-size: 2.5rem;
  }
  
  .tagline {
    font-size: 1.4rem;
  }
  
  .cta-buttons {
    flex-direction: row;
    justify-content: center;
  }
  
  .cta-button {
    width: auto;
  }
  
  .process-flow {
    max-width: 80%;
    margin: 2rem auto;
  }
  
  .contact-dialog {
    max-width: 400px;
  }
  
  .qrcode-container {
    width: 200px;
    height: 200px;
  }
  
  /* 平板和桌面布局 */
  .values-grid,
  .modules-grid,
  .plans-grid {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .service-targets {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .pain-solution-item {
    flex-direction: row;
  }
}

@media (min-width: 576px) and (max-width: 767px) {
  .values-grid,
  .modules-grid,
  .plans-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .service-targets {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 767px) {
  .process-flow {
    flex-direction: column;
    gap: 1rem;
  }
  
  .process-arrow {
    transform: rotate(90deg);
    margin: 0.5rem 0;
  }
  
  /* 手机端标题样式调整 */
  h3 {
    font-size: 1.25rem;
    margin-bottom: 1.2rem;
  }
  
  .hero-section h2 {
    font-size: 1.6rem;
  }
  
  .tagline {
    font-size: 1rem;
  }
  
  .highlight-promise p {
    font-size: 1.1rem;
  }
}

@media (max-width: 480px) {
  .contact-dialog {
    max-width: 320px;
  }
  
  .qrcode-container {
    width: 160px;
    height: 160px;
  }
  
  .dialog-content {
    padding: 1.25rem 1rem;
  }
  
  /* 更小屏幕设备的标题进一步调整 */
  h3 {
    font-size: 1.15rem;
    margin-bottom: 1rem;
  }
  
  .hero-section h2 {
    font-size: 1.5rem;
  }
  
  .main-title {
    font-size: 1.2rem;
  }
  
  .pain-solution-item {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .arrow {
    transform: rotate(90deg);
    margin: 0.25rem 0;
  }
}

/* 联系弹窗样式 */
.contact-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 1rem;
}

.contact-dialog {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  width: 100%;
  max-width: 360px;
  overflow: hidden;
  animation: dialog-fade-in 0.3s;
}

@keyframes dialog-fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.25rem;
  border-bottom: 1px solid #eaeaea;
}

.dialog-header h4 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.1rem;
}

.close-button {
  padding: 0.5rem;
  color: #909399;
}

.dialog-content {
  padding: 1.5rem;
  text-align: center;
}

.dialog-text {
  margin-top: 0;
  margin-bottom: 1.25rem;
  color: #606266;
  font-size: 0.95rem;
  line-height: 1.5;
}

.qrcode-container {
  width: 180px;
  height: 180px;
  margin: 0 auto 1.25rem;
  padding: 0.5rem;
  border: 1px solid #eaeaea;
  border-radius: 4px;
}

.qrcode-container img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.dialog-tips {
  font-size: 1rem;
  color: #2c3e50;
  margin-bottom: 1.25rem;
}

.dialog-benefits {
  text-align: left;
  color: #606266;
  font-size: 0.9rem;
  margin: 0.5rem 0;
}
</style>