<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { useRouter } from 'vue-router'
import { 
  ArrowLeft, 
  VideoPlay, 
  ArrowRight, 
  ArrowLeft as PrevIcon, 
  User,
  DataAnalysis,
  Sunny,
  SetUp,
  Lock,
  Service,
  Opportunity,
  Money,
  Connection
} from '@element-plus/icons-vue'
import ImageViewer from '../../../components/ImageViewer.vue'

const router = useRouter()

const goBack = () => {
  router.push('/card/houri-capital')
}

const activeTab = ref('about')
const showVideo = ref(false)
const videoUrl = ref('')
const activeTeamIndex = ref(0)
const activeCaseIndex = ref(0)

// 图片查看器相关状态
const imageViewerVisible = ref(false)
const currentImage = ref('')

// 团队轮播计时器
let teamCarouselTimer: number | null = null

const teamMembers = ref([
  {
    id: 1,
    name: '韩朋昌',
    title: '政企协同中心主任',
    avatar: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/HouRiZiBen/people-1.png',
    description: '■国家发改委投资协会产融创新基地 前主任<br>■北京恒友时代国际创投 董事长<br>■多地政府产投规划顾问 | 招商引资战略顾问<br><br>聚焦领域<br>智能制造 | 高端装备 | 低空经济 | AI及机器人产业场景应用转化<br><br>实务赋能模块<br>上市辅导 | 顶层设计 | 市值管理投融资并购 | 产业集群战略规划'
  },
  {
    id: 2,
    name: '赵妍',
    title: '首席运营官 COO',
    avatar: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/HouRiZiBen/people-2.png',
    description: '■亚太城市研究会房地产分会前秘书长<br>■香港太平洋投资集团 前副总裁<br>■多家拟上市企业资本运作首席顾问<br><br>聚焦领域<br>AI+产业生态转型·全域场景应用赋能 | 拟上市公司投融资路径战略规划<br><br>实务赋能模块<br>上市辅导 | 顶层设计 | 市值管理 | AI+产业场景市场化落地'
  },
  {
    id: 3,
    name: '张少剑',
    title: '投行部总裁',
    avatar: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/HouRiZiBen/people-3.png',
    description: '■人民代表报前外联部副主任<br>■英泰格瑞投资基金前副总经理(地产融资与销售)<br>■浙商创投北京分公司 | 国家中小企业基金·浙普基金前副总经理<br>■浙商创投·西溪谷管委会产业运营公司产业发展部前总经理<br><br>聚焦领域<br>产业资本全周期运作·政府-企业协同生态构建 | 产融结合战略规划<br><br>实务赋能模块<br>上市辅导 | 市值管理 | 并购重组 | 产业资本化路径设计'
  },
  {
    id: 4,
    name: '朱建国',
    title: '首席人力官 CHO',
    avatar: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/HouRiZiBen/people-4.png',
    description: '■工商管理硕士 | 企业管理咨询与职业发展领域深耕者<br>■北大纵横管理咨询集团 合伙人<br>■北京中基未来教育科技院 副院长<br>■浙江省人力资源协会专家 | 浙江大学城市学院创业导师<br><br>聚焦领域<br>企业管理体系升级 | 产教融合与人才战略规划<br><br>实务赋能模块<br>企业战略咨询 | 组织制度设计 | 人才培育体系搭建 | 高校创新创业教育 | 职业发展专著出版<br>代表著作<br>《制度才是真正的老板》《你也可以找到好工作》《形象管理》'
  },
  {
    id: 5,
    name: '潘斌',
    title: '首席市场官 CMO',
    avatar: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/HouRiZiBen/people-5.png',
    description: '■区域产业生态协同专家顾问 | 后日资本新材料科技成果转化中心负责人<br>■产业供应链金融专家 | 国家理财规划师·中级经济师<br>■国际贸易金融专业背景 | 产融创新实战深耕者<br><br>聚焦领域<br>智能制造·科技新材料 | 跨境贸易·一带一路供应链·产业出海<br><br>实务赋能模块<br>区域招商引资战略卜跨境贸易顶层设计 | 中外高新技术交流'
  },
  {
    id: 6,
    name: '武四化',
    title: '首席法务官',
    avatar: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/HouRiZiBen/people-6.png',
    description: '执业律师、高级会计师<br>注册会计师、注册税务师<br>浙江省法学会金融法学研究会董事<br>财政部政府采购评审专家<br>某市会计专家库首届专家<br>某市国有资产重大评估项目评审专家'
  },
  {
    id: 7,
    name: '李晓峰',
    title: '首席财务官 CFO',
    avatar: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/HouRiZiBen/people-7.png',
    description: '■财税领域深耕18年<br>曾任大型民营上市企业、和君咨询及头部会计师事务所财税专家<br>■企业财税战略架构师 | 产融合规风控顾问<br><br>企业财税合规重构·并购重组财务重述 | 企业全生命周期财控体系搭建 | 股权/交易架构设计 | 财务核算与税务共享系统建设 | 投融资视角业财风控模型 | 经营者内控体系护航'
  },
  {
    id: 8,
    name: '王雪',
    title: '董事长助理',
    avatar: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/HouRiZiBen/people-8.png',
    description: '■历任头部教育机构、产业创新服务综合体产品总监<br>■产业供应链协同管理先行者l未来组织模型设计师<br><br>聚焦领域<br>产业供应链三链协同效能·企业融媒体生态运维 | 数智化人才战略与组织升级<br><br>实务赋能模块<br>企业IP/KOL孵化体系 | 资本路演教育系统搭建 | 数智化平台全链路管理未来企业协同共生组织模型 | 产融场景信息提炼与价值重构<br><br>创新成果<br>联合研发 | 雾透WTS | 系统:企业数智化人才教育新基建解决方案'
  }
])

const cases = ref([
  {
    id: 1,
    title: '无锡中关村软件园项目',
    content: '代表发改委中国投资协会，全国投放产融创新基地与北科建集团顺利完成对无锡中关村软件园无锡落地、启动揭牌。'
  },
  {
    id: 2,
    title: '一带一路供应链金融创新',
    content: '代表一带一路供应链金融联盟国家会议中心做发布会，投放一带一路供应链金融创新3.0解决方案，助力产业供应链上下游中小企业快速融资案例80+，与全国50家商业银行建立合作关系，结合三农供应链金融向"一带一路"中小企业普惠金融扶持战略整体投放资金规模500亿+。'
  },
  {
    id: 3,
    title: '政府路演中心建设',
    content: '协助多家政府招商投资促进局完成路演中心的硬软件建设，上海张江高科创业联盟、上海国际人才城、上海国际创业周、上海创智集团、深圳南山创业之星路演中心、杭州场景科技谷、台州政府金融大厦、招商投资促进局等政府、机构完成路演工场搭建工作。'
  },
  {
    id: 4,
    title: '创新创业大赛路演指导',
    content: '为北京中关村、上海张江高科、深圳南山三地创新创业基地的万人创业创新大赛项目、国际人才城、国际创业周等做路演总教练。'
  },
  {
    id: 5,
    title: '上市公司并购与融资',
    content: '协助中创环保等多家上市公司构筑产业供应链研究院，协同完成A股上市公司27亿并购事宜，参与建设投资者长三角IR关系管理舱，与多家机构投资者结成合作伙伴关系，供应链金融融资总规模达20亿+，二级市场配资规模总计达50亿+。'
  },
  {
    id: 6,
    title: '集团定增与转型',
    content: '参与上海证大集团内部定增1.6亿融资计划、融资执行、融资完毕全流程；中创尊汇集团等大型集团型公司产业转型顶层战略设计，协助关联子公司股权路演融资规模2.3亿。'
  },
  {
    id: 7,
    title: '股权融资项目',
    content: '亲自深度梳理复盘项目个数1万+，升级商业计划书5千+，协助股权融资项目个数100+，总股权融资规模总计20亿+。'
  },
  {
    id: 8,
    title: '产城融合赋能服务',
    content: '参与中关村实创地产集团、北科建集团、上海创智地产集团、众安集团、泛华国际、中创尊汇集团等多家集团公司产城融合赋能服务转型升级顶层设计。'
  },
  {
    id: 9,
    title: '并购重组与基金成立',
    content: '成功主导威高股份并购爱普医疗参与的定增及并购的投融项目有聚光科技(300203)、深天地(A000023)、中国船舶(600150)、中国重工(601989)、御银股份(002177)、怡亚通(002183)、爱普医疗(833469)、哇棒传媒(430346)、麦凯制造(834735)、林格贝(831979)、狼和医疗(836795)、华图股份(831952);参与完成TCL100亿母基金的成立;组织炫泰文化、盎锐科技、星铂软件、厦门倍凡收购云南昆明工业学校等多个项目的股权融资及重组上市。'
  }
])

const playVideo = (url: string) => {
  videoUrl.value = url
  showVideo.value = true
}

const closeVideo = () => {
  showVideo.value = false
  videoUrl.value = ''
}

const nextTeamMember = () => {
  activeTeamIndex.value = (activeTeamIndex.value + 1) % teamMembers.value.length
}

const prevTeamMember = () => {
  activeTeamIndex.value = (activeTeamIndex.value - 1 + teamMembers.value.length) % teamMembers.value.length
}

const nextCase = () => {
  activeCaseIndex.value = (activeCaseIndex.value + 1) % cases.value.length
}

const prevCase = () => {
  activeCaseIndex.value = (activeCaseIndex.value - 1 + cases.value.length) % cases.value.length
}

const chatWithAI = () => {
  window.location.href = 'https://ai.sdtaa.com:3105/chat/share?shareId=jp24j6vs061s76ldw85wms6d'
}

// 打开图片查看器
const openImageViewer = (imageUrl: string) => {
  currentImage.value = imageUrl
  imageViewerVisible.value = true
}

// 启动团队轮播
const startTeamCarousel = () => {
  // 清除可能存在的旧计时器
  stopTeamCarousel()
  
  // 设置新的计时器，每3秒切换一次
  teamCarouselTimer = setInterval(() => {
    nextTeamMember()
  }, 3000)
}

// 停止团队轮播
const stopTeamCarousel = () => {
  if (teamCarouselTimer) {
    clearInterval(teamCarouselTimer)
    teamCarouselTimer = null
  }
}

// 手动切换时暂停自动轮播，5秒后恢复
const manualTeamChange = (direction: 'prev' | 'next') => {
  // 停止自动轮播
  stopTeamCarousel()
  
  // 执行手动切换
  if (direction === 'prev') {
    prevTeamMember()
  } else {
    nextTeamMember()
  }
  
  // 5秒后恢复自动轮播
  setTimeout(() => {
    startTeamCarousel()
  }, 5000)
}

onMounted(() => {
  document.title = '后日资本 - 产业赋能'
  window.addEventListener('popstate', handlePopState)
  
  // 启动团队轮播
  startTeamCarousel()
})

onBeforeUnmount(() => {
  window.removeEventListener('popstate', handlePopState)
  
  // 清除计时器
  stopTeamCarousel()
})

// 处理浏览器返回按钮
const handlePopState = (event: PopStateEvent) => {
  if (imageViewerVisible.value) {
    imageViewerVisible.value = false
    document.body.style.overflow = ''
    // 阻止默认的返回行为
    event.preventDefault()
  }
}
</script>

<template>
  <div class="view-container">
    <!-- 顶部导航栏 -->
    <div class="header">
      <el-button type="text" @click="goBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <h1>后日资本：产业赋能</h1>
    </div>

    <!-- 内容区域 -->
    <div class="content">
      <!-- 导航选项卡 -->
      <div class="nav-tabs">
        <div 
          class="tab-item" 
          :class="{ active: activeTab === 'about' }" 
          @click="activeTab = 'about'"
        >
          <span>我们是谁</span>
        </div>
        <div 
          class="tab-item" 
          :class="{ active: activeTab === 'what' }" 
          @click="activeTab = 'what'"
        >
          <span>我们做什么</span>
        </div>
        <div 
          class="tab-item" 
          :class="{ active: activeTab === 'focus' }" 
          @click="activeTab = 'focus'"
        >
          <span>我们聚焦什么</span>
        </div>
        <div 
          class="tab-item" 
          :class="{ active: activeTab === 'cases' }" 
          @click="activeTab = 'cases'"
        >
          <span>价值印证</span>
        </div>
      </div>

      <!-- 我们是谁 -->
      <div v-show="activeTab === 'about'" class="tab-content">
        <!-- 视频区域 -->
        <div class="video-section">
          <div v-if="!showVideo" class="video-banner" @click="playVideo('https://pic.sdtaa.com/ZhiLian/Video/Enterprise/HouRiZiBen/xcsp1.mp4')">
            <div class="play-icon">
              <el-icon><VideoPlay /></el-icon>
            </div>
            <div class="video-text">宣传视频</div>
          </div>
          <div v-else class="video-player-container">
            <video 
              ref="videoPlayer"
              controls 
              autoplay 
              :src="videoUrl" 
              class="inline-video-player"
              @ended="closeVideo"
            ></video>
            <el-button class="close-video-btn" @click="closeVideo" circle>×</el-button>
          </div>
        </div>

        <!-- 公司介绍 -->
        <div class="section-title">
          <div class="number">1</div>
          <h2>我们是谁：您值得信赖的产业合伙人</h2>
        </div>

        <p class="section-text">
          我们是后日资本，一家根植中国、面向全球的产业投资管理机构。自2014年由资深产投专家、区域产业生态协同顶层架构师王慧女士发起成立以来，我们始终专注于产业研究及投融资领域，致力于成为连接产业与资本的桥梁。
        </p>

        <!-- CEO介绍 -->
        <div class="ceo-profile">
          <div class="ceo-avatar">
            <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/HouRiZiBen/CEO-1.png" alt="王慧" @click="openImageViewer('https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/HouRiZiBen/CEO-1.png')" />
          </div>
          <div class="ceo-info">
            <h3>王慧</h3>
            <h4>后日资本创始人、董事长</h4>
            <p>王慧女士任后日资本董事长、雾透社发起人、中关村智用人工智能研究院AI应用协同中心主任，同时担任多所知名大学客座教授及浙江省供应链协会专家委员。</p>
            <ul class="ceo-achievements">
              <li>国际CMC管理咨询师、"一带一路"供应链金融专家</li>
              <li>资本路演教练及区域产融规划专家</li>
              <li>区域产业生态顶层架构师</li>
              <li>科技金融、数据金融与区域经济领域专家</li>
            </ul>
            <p class="ceo-experience">
              作为跨金融、科技与产业经济的复合型专家，在科技金融、数据金融与区域经济领域深耕二十余年。首创"AI+产业+资本"三维生态赋能体系，通过技术普惠与生态协同两大战略，推动人工智能技术与区域产业经济深度融合。作为区域产业生态顶层架构师，擅长以供应链金融为支点，构建产融创新循环经济模型，实现产业转型升级与资本价值共振。
            </p>
          </div>
        </div>

        <!-- 团队介绍 -->
        <div class="team-intro">
          <p>
            我们的核心团队汇聚了来自国内外知名产投机构的资深专家，平均拥有10年以上从业经验。我们立足杭州总部基地，并在北京、上海、深圳设立协同中心，构建起覆盖全国主要经济区域的服务网络，能够深刻理解不同地区的产业特点和发展需求。
          </p>
        </div>

        <!-- 团队成员 -->
        <div class="team-section">
          <h3>核心团队成员</h3>
          
          <div class="team-carousel">
            <div class="team-nav">
              <el-button class="nav-btn prev" @click="manualTeamChange('prev')" circle>
                <el-icon><PrevIcon /></el-icon>
              </el-button>
              <span class="carousel-indicator">{{ activeTeamIndex + 1 }}/{{ teamMembers.length }}</span>
              <el-button class="nav-btn next" @click="manualTeamChange('next')" circle>
                <el-icon><ArrowRight /></el-icon>
              </el-button>
              <span class="nav-tip">每3秒自动切换</span>
            </div>
            
            <div class="team-member">
              <div class="member-avatar">
                <img :src="teamMembers[activeTeamIndex].avatar" :alt="teamMembers[activeTeamIndex].name" @click="openImageViewer(teamMembers[activeTeamIndex].avatar)" />
              </div>
              <div class="member-info">
                <h4>{{ teamMembers[activeTeamIndex].name }}</h4>
                <h5>{{ teamMembers[activeTeamIndex].title }}</h5>
                <div class="member-description" v-html="teamMembers[activeTeamIndex].description"></div>
              </div>
            </div>
          </div>

          <div class="divider"></div>

          <div class="value-statement-container">
            <div class="value-statement-icon">
              <el-icon><Opportunity /></el-icon>
            </div>
            <div class="value-statement">
              <h3>我们的价值理念</h3>
              <p>我们以"价值管理"为核心驱动，秉承"降本节能、提质增效、价值最优"的管理理念，通过专业的投资能力和深入的产业洞察，为区域经济和企业发展提供高质量的赋能服务。</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 我们做什么 -->
      <div v-show="activeTab === 'what'" class="tab-content">
        <div class="section-title">
          <div class="number">2</div>
          <h2>我们做什么：为您链接资源与价值</h2>
        </div>

        <div class="what-we-do">
          <p>
            后日资本的核心使命是助力区域政府构建产业资本协同生态，激发区域经济活力。我们坚持"AI驱动·生态协同·价值共生"的指导方针，通过创新模式，将产业、科技、金融、人才、智力、资本六大要素有机整合，打造多位一体的后产业资本协同赋能生态。
          </p>

          <div class="feature-box">
            <div class="feature-icon trust-icon">
              <el-icon><Lock /></el-icon>
            </div>
            <div class="feature-content">
              <h3>五信工程</h3>
              <p>
                我们深信合作基石在于信任。因此，我们践行"信念、信心、信任、信誉、信用"的"五信工程"，以靶向式、定制化、陪跑式服务与您深度协同，确保方案与区域实际情况高度匹配。
              </p>
            </div>
          </div>

          <div class="feature-box">
            <div class="feature-icon service-icon">
              <el-icon><Service /></el-icon>
            </div>
            <div class="feature-content">
              <h3>全链条服务</h3>
              <p>
                我们提供覆盖产业直投、战略投资、资产管理及金融服务等全链条服务。借助我们在资本运作和科技金融创新方面的丰富经验（团队成员曾任职国际500强、知名基金公司及上市公司，参与过大型并购重组、基金设立等，如A股上市公司27亿并购事宜），我们能为您高效链接资本网络，满足区域产业发展不同阶段的资金需求。
              </p>
            </div>
          </div>

          <div class="feature-box">
            <div class="feature-icon value-icon">
              <el-icon><Money /></el-icon>
            </div>
            <div class="feature-content">
              <h3>切实价值</h3>
              <p>
                我们带来的价值是切实可见的。例如，通过部署AI招商系统，首批企业对接效率可提升5倍。在产业基金退出时，我们致力于确保区域留存的协同价值≥投资额的200%，实现区域产业与资本的长期共赢。通过灵活的合作模式（如首年以协同收益分成替代部分固定服务费）降低您的初期合作成本，提高合作性价比。
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- 我们聚焦什么 -->
      <div v-show="activeTab === 'focus'" class="tab-content">
        <div class="section-title">
          <div class="number">3</div>
          <h2>我们聚焦什么：高潜力产业赛道</h2>
        </div>

        <p class="focus-intro">
          面向数字经济、绿色转型和产业升级的时代机遇，后日资本精准聚焦三大战略赛道：
        </p>

        <div class="focus-tracks">
          <div class="track-card">
            <div class="track-icon digital-icon">
              <el-icon><DataAnalysis /></el-icon>
            </div>
            <h3>数智经济</h3>
            <p>紧随新一轮科技革命浪潮，关注人工智能、大数据、物联网等数字技术与实体经济的深度融合。</p>
          </div>

          <div class="track-card">
            <div class="track-icon green-icon">
              <el-icon><Sunny /></el-icon>
            </div>
            <h3>绿色能源</h3>
            <p>响应国家"双碳"战略，深耕风、光、储、氢等新能源领域及相关产业链。</p>
          </div>

          <div class="track-card">
            <div class="track-icon material-icon">
              <el-icon><SetUp /></el-icon>
            </div>
            <h3>轻新材料</h3>
            <p>瞄准新材料的研发与应用，助力产业链的关键环节实现突破与创新。</p>
          </div>
        </div>

        <div class="focus-strategy-container">
          <div class="focus-strategy-icon">
            <el-icon><Connection /></el-icon>
          </div>
          <div class="focus-strategy-content">
            <h3>我们的方法论与目标</h3>
            <p>
              以"AI×产业协同"为核心方法论，我们致力于在这些高潜力赛道中，助力区域政府构建千亿级新兴产业集群，加速区域产业循环生态的基础工程建设。赋能区域经济，激活产业生态，为区域产业创新发展"凝魂聚气、强基固本"。我们的目标是成为数智化产业协同生态的领航者，让资本成为科技与产业的加速器，助力更多区域打造具备全球竞争力的产业高地。
            </p>
          </div>
        </div>
      </div>

      <!-- 价值印证 -->
      <div v-show="activeTab === 'cases'" class="tab-content">
        <div class="section-title">
          <div class="number">4</div>
          <h2>价值印证：精选赋能案例</h2>
        </div>

        <p class="cases-intro">
          我们的能力和价值已在多个项目中得到印证，为您带来切实的成果：
        </p>

        <div class="cases-carousel">
          <div class="case-nav">
            <el-button class="nav-btn prev" @click="prevCase" circle>
              <el-icon><PrevIcon /></el-icon>
            </el-button>
            <span class="carousel-indicator">{{ activeCaseIndex + 1 }}/{{ cases.length }}</span>
            <el-button class="nav-btn next" @click="nextCase" circle>
              <el-icon><ArrowRight /></el-icon>
            </el-button>
          </div>
          
          <div class="case-card">
            <div class="case-number">{{ activeCaseIndex + 1 }}</div>
            <h3>{{ cases[activeCaseIndex].title }}</h3>
            <p>{{ cases[activeCaseIndex].content }}</p>
          </div>
        </div>
      </div>

      <!-- 对话后小资按钮 -->
      <div class="chat-button" @click="chatWithAI">
        <el-icon><User /></el-icon>
        <span>对话后小资</span>
      </div>
    </div>

    <!-- 使用图片查看器组件 -->
    <ImageViewer 
      v-model:visible="imageViewerVisible" 
      :image-url="currentImage"
    />
  </div>
</template>

<style scoped>
.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8faf3;
  color: #333;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  min-width: 100vw;
  box-sizing: border-box;
}

.header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, #7e8f4e, #9fb25f);
  color: white;
  height: 3rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 100;
  box-sizing: border-box;
}

.back-button {
  color: white;
  margin-right: 1rem;
  font-size: 1.25rem;
  padding: 0.5rem;
  position: absolute;
  left: 0.5rem;
  z-index: 1;
}

.header h1 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  width: 100%;
  text-align: center;
}

.content {
  flex: 1;
  padding-top: 4.5rem;
  padding-bottom: 4rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
  padding-left: 1rem;
  padding-right: 1rem;
}

/* 导航选项卡 */
.nav-tabs {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  position: sticky;
  top: 3.5rem;
  z-index: 90;
  padding: 0.25rem;
  flex-wrap: nowrap;
  width: 100%;
  overflow: hidden;
  box-sizing: border-box;
}

.tab-item {
  padding: 0.7rem 0.1rem;
  font-weight: 500;
  color: #666;
  cursor: pointer;
  flex: 1;
  text-align: center;
  transition: all 0.3s ease;
  white-space: nowrap;
  border-radius: 0.4rem;
  margin: 0 0.05rem;
  position: relative;
  font-size: 0.95rem;
  min-width: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
}

.tab-item span {
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
  padding: 0 0.1rem;
}

@media (max-width: 359px) {
  .tab-item {
    font-size: 0.8rem;
    padding: 0.6rem 0.05rem;
  }
}

@media (min-width: 360px) and (max-width: 400px) {
  .tab-item {
    font-size: 0.85rem;
    padding: 0.65rem 0.1rem;
  }
}

@media (min-width: 401px) {
  .tab-item {
    font-size: 0.95rem;
    padding: 0.7rem 0.15rem;
  }
}

.tab-item:hover {
  background-color: rgba(126, 143, 78, 0.1);
  color: #7e8f4e;
}

.tab-item.active {
  color: white;
  background: linear-gradient(135deg, #7e8f4e, #9fb25f);
  box-shadow: 0 2px 8px rgba(126, 143, 78, 0.3);
}

.tab-item::after {
  content: '';
  position: absolute;
  bottom: -0.2rem;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid #9fb25f;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.tab-item.active::after {
  opacity: 1;
}

.tab-content {
  padding: 1rem 0 3rem 0;
}

/* 视频播放器 */
.video-section {
  margin-bottom: 2rem;
}

.video-player-container {
  position: relative;
  width: 100%;
  border-radius: 1rem;
  overflow: hidden;
}

.inline-video-player {
  width: 100%;
  border-radius: 1rem;
  display: block;
}

.close-video-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(255, 255, 255, 0.8);
  color: #333;
  font-size: 1.2rem;
  font-weight: bold;
  border: none;
  z-index: 3;
}

/* 视频banner */
.video-banner {
  height: 180px;
  background: url('https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/HouriLogo-1.jpg') center/cover;
  border-radius: 1rem;
  margin-bottom: 2rem;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  overflow: hidden;
}

.video-banner::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
}

.play-icon {
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 2;
}

.play-icon .el-icon {
  font-size: 30px;
  color: white;
}

.video-text {
  position: absolute;
  top: 20px;
  left: 20px;
  color: white;
  font-size: 1rem;
  font-weight: 500;
  z-index: 2;
  background: rgba(0, 0, 0, 0.5);
  padding: 5px 15px;
  border-radius: 4px;
}

/* 章节标题 */
.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
}

.number {
  width: 36px;
  height: 36px;
  background: linear-gradient(135deg, #7e8f4e, #9fb25f);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: 1rem;
  flex-shrink: 0;
}

.section-title h2 {
  margin: 0;
  font-size: 1.3rem;
  color: #7e8f4e;
  font-weight: 600;
}

.section-text {
  margin-bottom: 2rem;
  line-height: 1.6;
}

/* CEO介绍 */
.ceo-profile {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.ceo-avatar {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  overflow: hidden;
  margin-bottom: 1.5rem;
  border: 3px solid #bcc785;
  cursor: pointer;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.ceo-avatar:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 20px rgba(126, 143, 78, 0.3);
}

.ceo-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.ceo-info {
  text-align: center;
}

.ceo-info h3 {
  margin: 0 0 0.5rem;
  color: #7e8f4e;
  font-size: 1.4rem;
}

.ceo-info h4 {
  margin: 0 0 1rem;
  color: #666;
  font-weight: 500;
}

.ceo-achievements {
  text-align: left;
  padding-left: 1.5rem;
  margin-bottom: 1.5rem;
}

.ceo-achievements li {
  margin-bottom: 0.5rem;
  line-height: 1.5;
}

.ceo-experience {
  text-align: left;
  line-height: 1.6;
}

/* 团队介绍 */
.team-intro {
  margin-bottom: 2rem;
}

.team-intro p {
  line-height: 1.6;
}

.team-section h3 {
  color: #7e8f4e;
  margin-bottom: 1.5rem;
  text-align: center;
}

/* 团队成员轮播 */
.team-carousel {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.team-nav {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
  position: relative;
}

.carousel-indicator {
  margin: 0 1rem;
  color: #7e8f4e;
  font-weight: 500;
}

.nav-btn {
  color: #7e8f4e;
  border-color: #bcc785;
}

.nav-tip {
  color: #7e8f4e;
  font-size: 0.75rem;
  background-color: rgba(126, 143, 78, 0.1);
  padding: 0.25rem 0.7rem;
  border-radius: 1rem;
  white-space: nowrap;
  position: absolute;
  right: -10px;
}

.team-member {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.member-avatar {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  overflow: hidden;
  margin-bottom: 1rem;
  border: 2px solid #bcc785;
  cursor: pointer;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.member-avatar:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 20px rgba(126, 143, 78, 0.3);
}

.member-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.member-info {
  text-align: center;
}

.member-info h4 {
  margin: 0 0 0.3rem;
  color: #7e8f4e;
  font-size: 1.2rem;
}

.member-info h5 {
  margin: 0 0 1rem;
  color: #666;
  font-weight: 500;
}

.member-description {
  text-align: left;
  line-height: 1.6;
}

.divider {
  height: 1px;
  background: #ddd;
  margin: 2rem 0;
}

/* 价值理念部分 */
.value-statement-container {
  background: linear-gradient(135deg, rgba(126, 143, 78, 0.1), rgba(159, 178, 95, 0.15));
  border-radius: 1rem;
  padding: 1.5rem;
  margin-top: 1.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.03);
  border: 1px solid rgba(126, 143, 78, 0.2);
}

.value-statement-icon {
  width: 60px;
  height: 60px;
  background-color: rgba(126, 143, 78, 0.2);
  border-radius: 50%;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.value-statement-icon .el-icon {
  font-size: 30px;
  color: #7e8f4e;
}

.value-statement {
  text-align: center;
}

.value-statement h3 {
  color: #7e8f4e;
  margin: 0 0 0.75rem;
  font-size: 1.2rem;
  font-weight: 600;
}

.value-statement p {
  color: #555;
  line-height: 1.7;
  margin: 0;
  font-size: 1rem;
}

@media (min-width: 768px) {
  .value-statement-container {
    flex-direction: row;
    text-align: left;
    padding: 2rem;
  }
  
  .value-statement-icon {
    margin-right: 2rem;
    margin-bottom: 0;
    width: 80px;
    height: 80px;
    flex-shrink: 0;
  }
  
  .value-statement {
    text-align: left;
    z-index: 1;
  }
  
  .value-statement h3 {
    font-size: 1.4rem;
  }
}

/* 我们做什么 */
.what-we-do p {
  line-height: 1.6;
  margin-bottom: 2rem;
}

.feature-box {
  display: flex;
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.feature-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  flex-shrink: 0;
  margin-right: 1.5rem;
  background-color: rgba(188, 199, 133, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
}

.feature-icon .el-icon {
  font-size: 30px;
  color: #7e8f4e;
}

.trust-icon {
  background-color: rgba(188, 199, 133, 0.2);
}

.service-icon {
  background-color: rgba(188, 199, 133, 0.2);
}

.value-icon {
  background-color: rgba(188, 199, 133, 0.2);
}

.feature-content h3 {
  margin: 0 0 0.5rem;
  color: #7e8f4e;
}

.feature-content p {
  margin: 0;
}

/* 我们聚焦什么 */
.focus-intro {
  margin-bottom: 2rem;
  line-height: 1.6;
}

.focus-tracks {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.track-card {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  text-align: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.track-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  margin: 0 auto 1rem;
  background-color: rgba(188, 199, 133, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
}

.track-icon .el-icon {
  font-size: 40px;
  color: #7e8f4e;
}

.track-card h3 {
  margin: 0 0 1rem;
  color: #7e8f4e;
}

.track-card p {
  margin: 0;
  line-height: 1.6;
}

.focus-strategy-container {
  background: linear-gradient(135deg, rgba(126, 143, 78, 0.1), rgba(159, 178, 95, 0.15));
  border-radius: 1rem;
  padding: 1.5rem;
  margin-top: 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.03);
  border: 1px solid rgba(126, 143, 78, 0.2);
}

.focus-strategy-icon {
  width: 60px;
  height: 60px;
  background-color: rgba(126, 143, 78, 0.2);
  border-radius: 50%;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.focus-strategy-icon .el-icon {
  font-size: 30px;
  color: #7e8f4e;
}

.focus-strategy-content {
  text-align: center;
  z-index: 1;
}

.focus-strategy-content h3 {
  color: #7e8f4e;
  margin: 0 0 0.75rem;
  font-size: 1.2rem;
  font-weight: 600;
}

.focus-strategy-content p {
  color: #555;
  line-height: 1.7;
  margin: 0;
  font-size: 1rem;
}

@media (min-width: 768px) {
  .focus-tracks {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .focus-strategy-container {
    flex-direction: row;
    text-align: left;
    padding: 2rem;
  }
  
  .focus-strategy-icon {
    margin-right: 2rem;
    margin-bottom: 0;
    width: 80px;
    height: 80px;
    flex-shrink: 0;
  }
  
  .focus-strategy-content {
    text-align: left;
  }
  
  .focus-strategy-content h3 {
    font-size: 1.4rem;
  }
}

/* 价值印证 */
.cases-intro {
  margin-bottom: 2rem;
  line-height: 1.6;
}

.cases-carousel {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.case-nav {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
}

.case-card {
  position: relative;
  padding-left: 3rem;
}

.case-number {
  position: absolute;
  left: 0;
  top: 0;
  width: 2rem;
  height: 2rem;
  background: linear-gradient(135deg, #7e8f4e, #9fb25f);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.case-card h3 {
  margin: 0 0 1rem;
  color: #7e8f4e;
}

.case-card p {
  margin: 0;
  line-height: 1.6;
}

/* 对话按钮 */
.chat-button {
  position: fixed;
  bottom: 1.8rem;
  right: 1.8rem;
  background: linear-gradient(135deg, #7e8f4e, #9fb25f);
  color: white;
  border-radius: 1.8rem;
  padding: 0.7rem 1.3rem;
  display: flex;
  align-items: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  z-index: 90;
  font-size: 1rem;
}

.chat-button .el-icon {
  margin-right: 0.7rem;
  font-size: 1.2rem;
}

/* 媒体查询 */
@media (min-width: 768px) {
  .content {
    padding-top: 5rem;
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .section-title h2 {
    font-size: 1.5rem;
  }

  .ceo-profile {
    flex-direction: row;
    align-items: flex-start;
  }

  .ceo-avatar {
    margin-right: 2rem;
    margin-bottom: 0;
    flex-shrink: 0;
  }

  .ceo-info {
    text-align: left;
  }

  .team-member {
    flex-direction: row;
    align-items: flex-start;
  }

  .member-avatar {
    margin-right: 2rem;
    margin-bottom: 0;
  }

  .member-info {
    text-align: left;
  }

  .focus-tracks {
    grid-template-columns: repeat(3, 1fr);
  }

  .video-banner {
    height: 420px;
    margin-bottom: 3rem;
  }
  
  .play-icon {
    width: 100px;
    height: 100px;
  }
  
  .play-icon .el-icon {
    font-size: 50px;
  }
  
  .video-text {
    top: 30px;
    left: 30px;
    font-size: 1.2rem;
    padding: 8px 20px;
  }

  .video-player-container {
    aspect-ratio: 16/9;
    max-height: 600px;
    margin-bottom: 3rem;
  }
  
  .inline-video-player {
    height: 100%;
    object-fit: cover;
  }

  .chat-button {
    bottom: 2.2rem;
    right: 2.2rem;
    padding: 0.8rem 1.5rem;
    font-size: 1.1rem;
  }
  
  .chat-button .el-icon {
    font-size: 1.3rem;
  }
}

/* 大屏幕设备进一步优化 */
@media (min-width: 1200px) {
  .video-banner {
    height: 500px;
  }
  
  .video-player-container {
    max-height: 700px;
  }
}
</style>