<script setup lang="ts">
import { useRouter, useRoute } from 'vue-router'
import { computed } from 'vue'
import { ArrowLeft } from '@element-plus/icons-vue'
import TabBar from '../components/TabBar.vue'

const router = useRouter()
const route = useRoute()

const goBack = () => {
  router.push('/card/sr-media/product-center')
}

// 产品详情数据
const productDetails: Record<string, any> = {
  rongmeiti: {
    name: '融媒体发展有限公司',
    description: '服务至上 合作共赢',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ShangRaoChuanMei/zigongsi/rongmeitifazhan.png'
  },
  guanggao: {
    name: '广告有限公司',
    description: '优质 专业 高效',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ShangRaoChuanMei/zigongsi/guanggao.png'
  },
  yingshi: {
    name: '影视有限公司',
    description: '创新 专业 高效',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ShangRaoChuanMei/zigongsi/yingshi.png'
  },
  shuzikeji: {
    name: '数字科技有限公司',
    description: '开拓 创新 发展',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ShangRaoChuanMei/zigongsi/shuzikeji.png'
  },
  wenhuachanye: {
    name: '文化产业有限公司',
    description: '打造具有地方特色的文化产业品牌',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ShangRaoChuanMei/zigongsi/wenhuachanye.png'
  },
  yanyi: {
    name: '演艺有限公司',
    description: '服务至上 品质第一',
    image: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ShangRaoChuanMei/zigongsi/yanyi.png'
  }
}

// 获取当前产品信息
const currentProduct = computed(() => {
  const productId = route.params.id as string
  return productDetails[productId] || null
})

// 处理图片加载错误
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.style.display = 'none'
  console.error('图片加载失败:', img.src)
}
</script>

<template>
  <div class="view-container">
    <div class="header">
      <el-button type="text" @click="goBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <h1>{{ currentProduct?.name || '产品详情' }}</h1>
    </div>

    <div class="content" v-if="currentProduct">
      <div class="product-detail-card">
        <div class="product-header">
          <h2 class="product-title">{{ currentProduct.name }}</h2>
          <p class="product-subtitle">{{ currentProduct.description }}</p>
        </div>
        
        <div class="product-image-container">
          <img 
            :src="currentProduct.image" 
            :alt="currentProduct.name"
            class="product-image"
            @error="handleImageError"
          >
        </div>
      </div>
    </div>

    <div class="content" v-else>
      <div class="error-content">
        <h2>产品未找到</h2>
        <p>抱歉，您访问的产品页面不存在。</p>
        <el-button type="primary" @click="goBack">返回产品中心</el-button>
      </div>
    </div>
    
    <TabBar />
  </div>
</template>

<style scoped>
.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8fafc;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  min-width: 100vw;
  box-sizing: border-box;
}

.header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, #3b82f6, #60a5fa);
  color: white;
  height: 3rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 100;
  box-sizing: border-box;
}

.back-button {
  color: white;
  margin-right: 1rem;
  font-size: 1.25rem;
  padding: 0.5rem;
  position: absolute;
  left: 0.5rem;
  z-index: 1;
}

.header h1 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  width: 100%;
  text-align: center;
}

.content {
  flex: 1;
  padding-top: 4.5rem;
  padding-bottom: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
  padding-left: 1rem;
  padding-right: 1rem;
}

.product-detail-card {
  background: white;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
}

.product-header {
  padding: 2rem;
  text-align: center;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
}

.product-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.product-subtitle {
  font-size: 1rem;
  color: #6b7280;
  margin: 0;
}

.product-image-container {
  padding: 1rem;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.product-image {
  max-width: 100%;
  max-height: 80vh;
  object-fit: contain;
  border-radius: 0.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.error-content {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  text-align: center;
}

.error-content h2 {
  color: #ef4444;
  margin-bottom: 1rem;
}

.error-content p {
  color: #6b7280;
  margin-bottom: 2rem;
}

@media (max-width: 768px) {
  .product-header {
    padding: 1.5rem;
  }
  
  .product-title {
    font-size: 1.25rem;
  }
  
  .product-subtitle {
    font-size: 0.9rem;
  }
  
  .product-image-container {
    min-height: 300px;
  }
}
</style>
</script>
