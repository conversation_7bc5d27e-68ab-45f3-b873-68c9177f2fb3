<script setup lang="ts">
import { ref, onMounted } from 'vue'

// 移动端和桌面端背景图片
const mobileBackgroundImage = 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ShangRaoChuanMei/srcm-szr-sy2.jpg'
const desktopBackgroundImage = 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ShangRaoChuanMei/srcm-szr-sy2.jpg'

const isMobile = ref(false)

const checkDevice = () => {
  isMobile.value = window.innerWidth < 768
}

onMounted(() => {
  checkDevice()
  window.addEventListener('resize', checkDevice)
})
</script>

<template>
  <div class="digital-human">
    <div 
      class="background-image mobile-bg"
      :style="{ backgroundImage: `url(${mobileBackgroundImage})` }"
    ></div>
    <div 
      class="background-image desktop-bg"
      :style="{ backgroundImage: `url(${desktopBackgroundImage})` }"
    ></div>
  </div>
</template>

<style scoped>
.digital-human {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.background-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.mobile-bg {
  display: block;
}

.desktop-bg {
  display: none;
}

@media (min-width: 768px) {
  .mobile-bg {
    display: none;
  }
  
  .desktop-bg {
    display: block;
  }
}
</style>
