<script setup lang="ts">
import { useRouter } from 'vue-router'
import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue'
import { onMounted } from 'vue'

const router = useRouter()

const goBack = () => {
  router.push('/card/product-ai-sales')
}

// 案例数据
const cases = [
  {
    name: '企业AI宣传员',
    description: '打造7x24小时在线的智能门面。',
    logo: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/qiyeAImingpianLogo.jpg',
    url: 'https://zl.sdtaa.com/card/qiye-ai-mingpian'
  },
  {
    name: 'AI数智推介官',
    description: '政府智慧推介与服务的新范式。',
    logo: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/AIShuZhiTuiJieGuan/LOGO.jpeg',
    url: 'https://zl.sdtaa.com/card/ai-sz-guide'
  },
  {
    name: 'AI数智代言人',
    description: '打造永不塌房的AI品牌大使。',
    logo: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/AIShuZhiDaiYanRen/Logo.jpg',
    url: 'https://zl.sdtaa.com/card/ai-sz-agent'
  }
]

// 打开案例链接
const openCaseUrl = (url: string) => {
  window.open(url, '_blank')
}

onMounted(() => {
  document.title = '产品AI推销官 - 案例中心'
})
</script>

<template>
  <div class="view-container">
    <div class="header">
      <el-button type="text" @click="goBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <h1>案例中心</h1>
    </div>

    <div class="content">
      <div class="case-center-container">
        <section class="case-section">
          <h2 class="section-title">成功案例展示</h2>
          <p class="section-desc">探索我们为不同场景打造的AI产品推销官解决方案</p>

          <div class="case-grid">
            <div
              v-for="(item, index) in cases"
              :key="index"
              class="case-card"
              @click="openCaseUrl(item.url)"
            >
              <div class="case-logo">
                <img :src="item.logo" :alt="item.name + ' Logo'" />
              </div>
              <div class="case-info">
                <h3 class="case-name">{{ item.name }}</h3>
                <p class="case-desc">{{ item.description }}</p>
                <div class="case-link">
                  <span>访问案例</span>
                  <el-icon class="arrow-icon"><ArrowRight /></el-icon>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  </div>
</template>

<style scoped>
.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e3f2fd 100%);
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  min-width: 100vw;
  box-sizing: border-box;
}

.header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, #1976d2, #42a5f5);
  color: white;
  height: 3rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 100;
  box-sizing: border-box;
  backdrop-filter: blur(10px);
}

.back-button {
  color: white;
  margin-right: 1rem;
  font-size: 1.25rem;
  padding: 0.5rem;
  position: absolute;
  left: 0.5rem;
  z-index: 1;
}

.header h1 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  width: 100%;
  text-align: center;
}

.content {
  flex: 1;
  padding-top: 4.5rem;
  padding-bottom: 2rem; /* 移除TabBar预留空间 */
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
  padding-left: 1rem;
  padding-right: 1rem;
}

.case-center-container {
  padding: 1rem 0;
}

.case-section {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 1.5rem;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  text-align: center;
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, #1976d2, #42a5f5);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-desc {
  text-align: center;
  color: #666;
  margin-bottom: 2rem;
  font-size: 1rem;
  line-height: 1.6;
}

.case-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

.case-card {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid rgba(25, 118, 210, 0.1);
}

.case-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  border-color: rgba(25, 118, 210, 0.3);
}

.case-logo {
  flex-shrink: 0;
  width: 4rem;
  height: 4rem;
  margin-right: 1.5rem;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.case-logo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.case-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.case-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1976d2;
  margin: 0 0 0.5rem 0;
  line-height: 1.4;
}

.case-desc {
  color: #666;
  font-size: 0.95rem;
  line-height: 1.5;
  margin: 0 0 1rem 0;
}

.case-link {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  color: #1976d2;
  font-size: 0.9rem;
  font-weight: 500;
  transition: color 0.3s ease;
}

.case-link:hover {
  color: #42a5f5;
}

.arrow-icon {
  margin-left: 0.5rem;
  font-size: 1rem;
  transition: transform 0.3s ease;
}

.case-card:hover .arrow-icon {
  transform: translateX(3px);
}

/* 响应式设计 */
@media (min-width: 768px) {
  .content {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .section-title {
    font-size: 1.8rem;
  }

  .section-desc {
    font-size: 1.1rem;
  }

  .case-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .case-card {
    padding: 2rem;
  }

  .case-logo {
    width: 5rem;
    height: 5rem;
    margin-right: 2rem;
  }

  .case-name {
    font-size: 1.3rem;
  }

  .case-desc {
    font-size: 1rem;
    margin-bottom: 1.5rem;
  }

  .case-link {
    font-size: 1rem;
  }
}

@media (min-width: 1200px) {
  .case-section {
    padding: 3rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .case-grid {
    gap: 2.5rem;
  }

  .case-card {
    padding: 2.5rem;
  }

  .case-logo {
    width: 6rem;
    height: 6rem;
    margin-right: 2.5rem;
  }

  .case-name {
    font-size: 1.4rem;
  }

  .case-desc {
    font-size: 1.1rem;
  }
}
</style>
