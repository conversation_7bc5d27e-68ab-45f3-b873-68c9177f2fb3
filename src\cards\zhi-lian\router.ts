import { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'zhilianHome',
    component: () => import('./views/HomeView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Project/logo.png',
      title: '智链 - 企业的智能宣传中枢'
    }
  },
  {
    path: '/zhilian-ai',
    name: 'zhiLianAI',
    component: () => import('./views/ZhilianAIView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Project/logo.png',
      title: '智链AI - 企业的智能宣传中枢'
    }
  },
  {
    path: '/ai-assistant',
    name: 'zhilianAIAssistant',
    component: () => import('./views/AIAssistantView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Project/logo.png',
      title: 'AI小链 - 企业的智能宣传中枢'
    }
  },
  {
    path: '/products',
    name: 'zhilianProducts',
    component: () => import('./views/ProductsView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Project/logo.png',
      title: '产品介绍 - 智链AI企业宣传中枢'
    }
  },
  {
    path: '/testimonials',
    name: 'zhilianTestimonials',
    component: () => import('./views/TestimonialsView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Project/logo.png',
      title: '实力见证 - 企业的智能宣传中枢'
    }
  }
]

export default routes 