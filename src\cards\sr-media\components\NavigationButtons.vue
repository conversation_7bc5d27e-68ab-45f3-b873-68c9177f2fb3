<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { 
  OfficeBuilding, 
  ChatDotRound,
  Service,
  Phone
} from '@element-plus/icons-vue'

const router = useRouter()

const navigationItems = ref([
  {
    id: 'group-intro',
    name: '集团简介',
    description: '',
    icon: OfficeBuilding,
    route: '/card/sr-media/group-intro',
    isExternal: false,
    color: 'linear-gradient(135deg, #3b82f6cc, #60a5facc)'
  },
  {
    id: 'chat',
    name: '与我对话',
    description: '',
    icon: ChatDotRound,
    route: '/card/sr-media/chat',
    isExternal: false,
    color: 'linear-gradient(135deg, #3b82f6cc, #60a5facc)'
  },
  {
    id: 'products',
    name: '产品服务',
    description: '',
    icon: Service,
    route: '/card/sr-media/products',
    isExternal: false,
    color: 'linear-gradient(135deg, #3b82f6cc, #60a5facc)',
    hideOnMobile: true
  },
  {
    id: 'contact',
    name: '联系我们',
    description: '',
    icon: Phone,
    route: '/card/sr-media/contact',
    isExternal: false,
    color: 'linear-gradient(135deg, #3b82f6cc, #60a5facc)'
  }
])

const handleNavigation = (item: any) => {
  if (item.isExternal) {
    // 打开外部链接
    window.open(item.route, '_blank')
  } else {
    // 内部路由跳转
    router.push(item.route)
  }
}
</script>

<template>
  <div class="navigation-grid">
    <div
      v-for="item in navigationItems"
      :key="item.id"
      class="nav-button"
      :class="{ 'mobile-hidden': item.hideOnMobile }"
      :data-id="item.id"
      @click="handleNavigation(item)"
    >
      <div class="icon-container" :style="{ background: item.color }">
        <component :is="item.icon" class="icon" />
      </div>
      <div class="button-text-container">
        <span class="button-text">{{ item.name }}</span>
        <span class="button-description">{{ item.description }}</span>
      </div>
    </div>
  </div>
</template>

<style scoped>
.navigation-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
}

.nav-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 1rem;
  padding: 1rem 0.8rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  cursor: pointer;
  overflow: hidden;
  position: relative;
  border: 1px solid rgba(255, 255, 255, 0.6);
}

.nav-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
  z-index: 0;
}

.nav-button:active {
  transform: scale(0.98);
}

.nav-button:hover {
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  margin-bottom: 0.5rem;
  transition: transform 0.3s ease;
  position: relative;
  z-index: 1;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
}

.icon {
  font-size: 1.1rem;
  color: white;
  transform: scale(0.7);
}

.button-text-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.button-text {
  font-size: 0.9rem;
  font-weight: 500;
  background: linear-gradient(135deg, #3b82f6cc, #60a5facc);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transition: all 0.2s ease;
  position: relative;
  z-index: 1;
}

.button-description {
  font-size: 0.7rem;
  color: #3b82f6cc;
  margin-top: 0.2rem;
}

.nav-button:hover .icon-container {
  transform: scale(1.05);
}

/* 手机端隐藏特定按钮 */
@media (max-width: 767px) {
  .mobile-hidden {
    display: none;
  }

  /* 手机端特殊布局 */
  .navigation-grid {
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: auto auto;
    gap: 1rem;
  }

  /* 与我对话按钮占两列 */
  .nav-button[data-id="chat"] {
    grid-column: 1 / 3;
    grid-row: 1;
  }

  /* 集团简介按钮 */
  .nav-button[data-id="group-intro"] {
    grid-column: 1;
    grid-row: 2;
  }

  /* 联系我们按钮 */
  .nav-button[data-id="contact"] {
    grid-column: 2;
    grid-row: 2;
  }
}

@media (min-width: 768px) {
  .navigation-grid {
    gap: 2rem;
    grid-template-columns: repeat(4, 1fr);
    min-width: 800px;
    max-width: 1200px;
  }

  .nav-button {
    padding: 1.2rem 1rem;
  }

  .icon-container {
    width: 3rem;
    height: 3rem;
    margin-bottom: 0.6rem;
  }

  .icon {
    font-size: 1.3rem;
    transform: scale(0.7);
  }

  .button-text {
    font-size: 1rem;
  }

  .button-description {
    font-size: 0.8rem;
    margin-top: 0.3rem;
  }
}

@media (min-width: 1200px) {
  .navigation-grid {
    gap: 3rem;
  }
}
</style>
