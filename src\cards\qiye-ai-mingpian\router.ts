import { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'qiyeAIMingpianHome',
    component: () => import('./views/HomeView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/qiyeAImingpianLogo.jpg',
      title: '企业AI宣传官'
    }
  },
  {
    path: '/product-intro',
    name: 'qiyeAIMingpianProductIntro',
    component: () => import('./views/ProductIntroView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/qiyeAImingpianLogo.jpg',
      title: '企业AI宣传官 - 产品介绍'
    }
  },
  {
    path: '/case-center',
    name: 'qiyeAIMingpianCaseCenter',
    component: () => import('./views/CaseCenterView.vue'),
    meta: { 
      favicon: 'https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/ZhiLian/qiyeAImingpianLogo.jpg',
      title: '企业AI宣传官 - 案例中心'
    }
  }
]

export default routes 