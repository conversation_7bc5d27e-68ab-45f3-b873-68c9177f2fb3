<script setup lang="ts">
import { useRouter } from 'vue-router'
import { ArrowLeft, VideoPlay, OfficeBuilding, School, Connection, Trophy } from '@element-plus/icons-vue'
import { ref, onMounted } from 'vue'

const router = useRouter()

const goBack = () => {
  router.push('/card/csj-szrc-shangrao')
}

onMounted(() => {
  document.title = '长三角数字人才上饶创新基地 - 基地简介'
})

const videoUrl = ref('https://pic.sdtaa.com/ZhiLian/Video/Enterprise/RenCaiJiDi/JDxcsp.mp4')
</script>

<template>
  <div class="view-container">
    <div class="header">
      <el-button type="text" @click="goBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <h1>基地简介</h1>
    </div>

    <div class="content">
      <div class="introduction-container">
        <!-- 视频部分 -->
        <section class="video-section">
          <h2 class="section-title">基地宣传视频</h2>
          <div class="video-wrapper">
            <video
              controls
              preload="metadata"
              class="intro-video"
              :src="videoUrl"
              poster="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/RenCaiJiDi/Logo.jpg"
            ></video>
          </div>
        </section>
        
        <!-- 时代背景与战略机遇 -->
        <section class="text-section">
          <div class="section-header">
            <el-icon class="section-icon"><Connection /></el-icon>
            <h2 class="section-title">时代背景与战略机遇</h2>
          </div>
          <div class="section-content">
            <p>数字经济已成为核心引擎，长三角作为发展前沿，面临数字人才短缺挑战。为深入贯彻国家数字经济发展战略，积极响应江西省委关于一体推进教育科技人才领域改革、建立教育科技人才协同工作机制的号召，"长三角数字人才上饶创新基地"应运而生。</p>
            <div class="section-image">
              <img src="https://pic.sdtaa.com/ZhiLian/Picture/Enterprise/RenCaiJiDi/QianYueYiShi.jpg" alt="签约仪式" class="content-image">
            </div>
          </div>
        </section>
        
        <!-- 权威指导与多元共建 -->
        <section class="text-section">
          <div class="section-header">
            <el-icon class="section-icon"><OfficeBuilding /></el-icon>
            <h2 class="section-title">权威指导与多元共建</h2>
          </div>
          <div class="section-content">
            <div class="support-grid">
              <div class="support-item">
                <h4 class="support-title">指导单位</h4>
                <p>江西省数据局</p>
              </div>
              <div class="support-item">
                <h4 class="support-title">支持单位</h4>
                <p>上饶市数据局、上饶市人才服务发展中心</p>
              </div>
            </div>
            <h3 class="subsection-title">核心共建方</h3>
            <div class="partners-grid">
              <div class="partner-item">
                <div class="partner-icon-container">
                  <el-icon class="partner-icon"><School /></el-icon>
                </div>
                <div class="partner-content">
                  <h4>上饶师范学院</h4>
                  <p>实践与教学资源</p>
                </div>
              </div>
              <div class="partner-item">
                <div class="partner-icon-container">
                  <el-icon class="partner-icon"><School /></el-icon>
                </div>
                <div class="partner-content">
                  <h4>上海师范大学</h4>
                  <p>战略指导与课程体系</p>
                </div>
              </div>
              <div class="partner-item">
                <div class="partner-icon-container">
                  <el-icon class="partner-icon"><Connection /></el-icon>
                </div>
                <div class="partner-content">
                  <h4>上饶市数字技术应用协会</h4>
                  <p>项目落地与企业对接</p>
                </div>
              </div>
            </div>
            <p>我们融合三方优势，协同发力，确保基地高质量发展。</p>
          </div>
        </section>
        
        <!-- 基地核心定位与使命 -->
        <section class="text-section">
          <div class="section-header">
            <el-icon class="section-icon"><Trophy /></el-icon>
            <h2 class="section-title">基地核心定位与使命</h2>
          </div>
          <div class="section-content">
            <p>立足上饶，辐射长三角，我们致力于打造一个集数字人才培养、技术创新、产业孵化、招商引资于一体的综合性平台。核心使命是为上饶乃至长三角地区源源不断地输送高素质应用型数字人才，破解区域人才瓶颈，服务国家战略。</p>
          </div>
        </section>
        
        <!-- 战略遵循与目标愿景 -->
        <section class="text-section">
          <div class="section-header">
            <el-icon class="section-icon"><VideoPlay /></el-icon>
            <h2 class="section-title">战略遵循与目标愿景</h2>
          </div>
          <div class="section-content">
            <p>基地建设依托《加快数字人才培育支撑数字经济发展行动方案（2024-2026年）》、江西省教育科技人才协同工作机制及江西省"1269"行动计划等文件精神。</p>
            
            <div class="goal-container">
              <div class="goal-item">
                <h3 class="goal-title">近期目标</h3>
                <p>聚焦数字领域新职业，每年培养不少于2000名学员，并通过"育留结合"模式，引导人才扎根上饶。</p>
              </div>
              <div class="goal-item">
                <h3 class="goal-title">长远愿景</h3>
                <p>成为区域数字人才培养的标杆和教育科技人才协同发展的新高地，为长三角数字经济的繁荣和江西省奋力谱写中国式现代化篇章贡献核心力量。</p>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  </div>
</template>

<style scoped>
.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f9ff;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  min-width: 100vw;
  box-sizing: border-box;
}

.header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, #0D47A1, #2196F3);
  color: white;
  height: 3rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 100;
  box-sizing: border-box;
}

.back-button {
  color: white;
  margin-right: 1rem;
  font-size: 1.25rem;
  padding: 0.5rem;
  position: absolute;
  left: 0.5rem;
  z-index: 1;
}

.header h1 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  width: 100%;
  text-align: center;
}

.content {
  flex: 1;
  padding-top: 4.5rem;
  padding-bottom: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
  padding-left: 1rem;
  padding-right: 1rem;
}

.introduction-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

/* 视频部分 */
.video-section {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.video-wrapper {
  width: 100%;
  border-radius: 0.5rem;
  overflow: hidden;
  margin-top: 1rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  background-color: #000;
  aspect-ratio: 16/9;
}

.intro-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

/* 文本部分通用样式 */
.text-section {
  background: white;
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  gap: 0.75rem;
}

.section-icon {
  color: #1976D2;
  font-size: 2rem;
  background: rgba(25, 118, 210, 0.1);
  padding: 0.75rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
}

.section-title {
  color: #0D47A1;
  font-size: 1.3rem;
  font-weight: 600;
  margin: 0;
}

.section-content {
  color: #333;
  line-height: 1.6;
}

.section-content p {
  margin-top: 0;
  margin-bottom: 1rem;
}

.section-content p:last-child {
  margin-bottom: 0;
}

/* 图片样式 */
.section-image {
  margin-top: 1.5rem;
  text-align: center;
}

.content-image {
  max-width: 100%;
  height: auto;
  border-radius: 0.75rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.content-image:hover {
  transform: scale(1.02);
}

/* 支持单位与共建方部分 */
.support-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.support-item {
  background: rgba(33, 150, 243, 0.05);
  padding: 1rem;
  border-radius: 0.5rem;
  border-left: 3px solid #1976D2;
}

.support-title {
  color: #0D47A1;
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
}

.subsection-title {
  color: #0D47A1;
  font-size: 1.1rem;
  margin: 1.5rem 0 1rem 0;
}

.partners-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
  margin-bottom: 1rem;
}

.partner-item {
  display: flex;
  align-items: center;
  background: rgba(33, 150, 243, 0.05);
  padding: 1rem;
  border-radius: 0.5rem;
  gap: 1rem;
}

.partner-icon-container {
  flex-shrink: 0;
  width: 2.5rem;
  height: 2.5rem;
  background: linear-gradient(135deg, #1976D2, #42A5F5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.partner-icon {
  color: white;
  font-size: 1.2rem;
}

.partner-content {
  flex-grow: 1;
}

.partner-content h4 {
  margin: 0 0 0.25rem 0;
  color: #0D47A1;
  font-size: 1rem;
}

.partner-content p {
  margin: 0;
  color: #555;
  font-size: 0.9rem;
}

/* 目标和愿景部分 */
.goal-container {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
  margin-top: 1rem;
}

.goal-item {
  background: rgba(33, 150, 243, 0.05);
  padding: 1rem;
  border-radius: 0.5rem;
  border-left: 3px solid #1976D2;
}

.goal-title {
  color: #0D47A1;
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
}

/* 媒体查询-平板和桌面端 */
@media (min-width: 768px) {
  .video-section {
    padding: 2rem;
  }
  
  .video-wrapper {
    margin-top: 1.5rem;
  }

  .text-section {
    padding: 2rem;
  }
  
  .section-title {
    font-size: 1.5rem;
  }
  
  .support-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .partners-grid {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .goal-container {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .section-icon {
    font-size: 2.5rem;
    padding: 0.85rem;
    width: 3.5rem;
    height: 3.5rem;
  }
}

/* 媒体查询-小屏幕平板 */
@media (min-width: 600px) and (max-width: 767px) {
  .partners-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* 超大屏幕 */
@media (min-width: 1200px) {
  .content {
    padding-left: 2rem;
    padding-right: 2rem;
  }
  
  .introduction-container {
    gap: 2rem;
  }
}
</style> 