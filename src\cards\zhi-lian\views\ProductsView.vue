<script setup lang="ts">
import { onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { 
  ArrowLeft, 
  Service, 
  Promotion, 
  Connection, 
  DataAnalysis, 
  Cpu, 
  Reading, 
  Opportunity,
  Share,
  VideoPlay,
  Avatar
} from '@element-plus/icons-vue'

const router = useRouter()

const goBack = () => {
  router.push('/card/zhi-lian')
}

const goToTestimonials = (filter = '', industry = '') => {
  const query: Record<string, string> = {}
  if (filter) query.filter = filter
  if (industry) query.industry = industry
  
  router.push({ 
    path: '/card/zhi-lian/testimonials', 
    query
  })
}

onMounted(() => {
  document.title = '产品介绍 - 智链AI企业宣传中枢'
})
</script>

<template>
  <div class="view-container">
    <div class="header">
      <el-button type="text" @click="goBack" class="back-button">
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <h1>产品介绍</h1>
    </div>

    <div class="content">
      <!-- 整体引导语 -->
      <div class="intro-banner">
        <div class="intro-content">
          <h2 class="intro-heading">智链AI与您共创企业宣传新未来</h2>
          <p class="intro-text">从「专业亮相」到「智能拓客」再到「长效链接」，智链AI与您共创企业宣传新未来。更有专业引擎，助您加速前行！</p>
        </div>
      </div>
      
      <!-- 智链进化之旅 -->
      <div class="section">
        <div class="section-header">
          <h2 class="section-title">智链进化之旅</h2>
          <div class="section-underline"></div>
        </div>
        
        <!-- 第一步 -->
        <div class="journey-step">
          <div class="step-number">
            <span>1</span>
          </div>
          <div class="step-content">
            <h3 class="step-title">打造您的「企业AI智能名片」</h3>
            <h4 class="step-subtitle">专业亮相，高效初链</h4>
            
            <div class="pain-point">
              <h5>场景痛点：</h5>
              <p>客户想了解您，信息散乱难寻？传统名片信息量小，无法互动？</p>
            </div>
            
            <div class="solution">
              <h5>智链如何帮您：</h5>
              <ul>
                <li>
                  <el-icon><Share /></el-icon>
                  <div>
                    <strong>信息聚合：</strong>
                    <p>公司、产品、案例、联系方式，一站式专业展示。</p>
                  </div>
                </li>
                <li>
                  <el-icon><Service /></el-icon>
                  <div>
                    <strong>智能交互：</strong>
                    <p>AI宣传员7x24小时在线，解答疑问，不错过任何咨询。</p>
                  </div>
                </li>
                <li>
                  <el-icon><VideoPlay /></el-icon>
                  <div>
                    <strong>动态呈现：</strong>
                    <p>AI内容官制作宣传视频，生动展现企业风采。</p>
                  </div>
                </li>
              </ul>
            </div>
            
            <div class="value">
              <h5>核心价值：</h5>
              <p>塑造专业第一印象，高效承接客户初步链接。</p>
            </div>
            
            <el-button type="primary" class="case-button" @click="goToTestimonials('企业AI名片')">
              查看AI名片案例
            </el-button>
          </div>
        </div>
        
        <!-- 第二步 -->
        <div class="journey-step">
          <div class="step-number">
            <span>2</span>
          </div>
          <div class="step-content">
            <h3 class="step-title">装备您的「AI营销专家」</h3>
            <h4 class="step-subtitle">智能拓客，精准赋能</h4>
            
            <div class="pain-point">
              <h5>场景痛点：</h5>
              <p>如何让更多人知道您？团队营销能力参差不齐，效果难衡量？</p>
            </div>
            
            <div class="solution">
              <h5>智链如何帮您：</h5>
              <ul>
                <li>
                  <el-icon><Opportunity /></el-icon>
                  <div>
                    <strong>专家策略：</strong>
                    <p>AI营销专家提供实战策略，指导内容方向。</p>
                  </div>
                </li>
                <li>
                  <el-icon><Promotion /></el-icon>
                  <div>
                    <strong>内容助推：</strong>
                    <p>辅助生成多平台（抖音/小红书等）营销素材。</p>
                  </div>
                </li>
                <li>
                  <el-icon><DataAnalysis /></el-icon>
                  <div>
                    <strong>全员营销：</strong>
                    <p>赋能团队，让每个人都成为营销好手。</p>
                  </div>
                </li>
              </ul>
            </div>
            
            <div class="value">
              <h5>核心价值：</h5>
              <p>提升营销专业度与效率，主动发现并吸引潜在客户。</p>
            </div>
            
            <el-button type="primary" class="case-button" @click="goToTestimonials('AI营销专家')">
              查看AI营销专家案例
            </el-button>
          </div>
        </div>
        
        <!-- 第三步 -->
        <div class="journey-step">
          <div class="step-number">
            <span>3</span>
          </div>
          <div class="step-content">
            <h3 class="step-title">构建您的「客户智能连接平台」</h3>
            <h4 class="step-subtitle">深度链接，长效共赢</h4>
            
            <div class="pain-point">
              <h5>场景痛点：</h5>
              <p>客户获取后如何持续跟进？如何提升客户忠诚度和复购率？</p>
            </div>
            
            <div class="solution">
              <h5>智链如何帮您：</h5>
              <ul>
                <li>
                  <el-icon><Connection /></el-icon>
                  <div>
                    <strong>自动化旅程：</strong>
                    <p>从营销触达到售后服务，智能化管理客户全周期。</p>
                  </div>
                </li>
                <li>
                  <el-icon><Cpu /></el-icon>
                  <div>
                    <strong>个性化运营：</strong>
                    <p>洞察客户需求，提供精准的个性化服务与内容。</p>
                  </div>
                </li>
                <li>
                  <el-icon><DataAnalysis /></el-icon>
                  <div>
                    <strong>价值深挖：</strong>
                    <p>提升客户生命周期价值，实现持续增长。</p>
                  </div>
                </li>
              </ul>
            </div>
            
            <div class="value">
              <h5>核心价值：</h5>
              <p>打造从吸引到留存的闭环，实现"智能链接每一位客户"的愿景。</p>
            </div>
            
            <!-- 此处无案例按钮 -->
          </div>
        </div>
      </div>
      
      <!-- 专业智能引擎 -->
      <div class="section engines-section">
        <div class="section-header">
          <h2 class="section-title">专业智能引擎</h2>
          <div class="section-underline"></div>
          <p class="section-subtitle">独立可选产品</p>
        </div>
        
        <!-- AI数智代言人 -->
        <div class="engine-card">
          <div class="engine-icon">
            <el-icon><Avatar /></el-icon>
          </div>
          <div class="engine-content">
            <h3>AI数智代言人</h3>
            <h4>您的专属AI品牌大使，永不"塌房"</h4>
            
            <div class="engine-details">
              <div class="pain-point">
                <h5>场景痛点：</h5>
                <p>真人代言成本高、风险大？宣传内容形式单一，不够新颖？</p>
              </div>
              
              <div class="solution">
                <h5>智链如何帮您：</h5>
                <p>7x24小时工作的AI数字人，可定制形象，高效生成各类宣传视频、讲解内容，用于产品发布、品牌活动、在线客服等。</p>
              </div>
              
              <div class="value">
                <h5>核心价值：</h5>
                <p>降本增效，形象可控，科技感十足，提升品牌吸引力。</p>
              </div>
            </div>
            
            <el-button type="primary" class="case-button" @click="goToTestimonials('AI数智代言人')">
              查看数智代言人案例
            </el-button>
          </div>
        </div>
        
        <!-- 知行AI -->
        <div class="engine-card">
          <div class="engine-icon">
            <el-icon><Reading /></el-icon>
          </div>
          <div class="engine-content">
            <h3>知行AI</h3>
            <h4>企业AI能力内化加速器，"让AI真正用起来"</h4>
            
            <!-- 宣传视频 -->
            <div class="video-container">
              <video 
                controls 
                class="promo-video"
              >
                <source src="https://pic.sdtaa.com/ZhiLian/Video/Enterprise/ZhiLian/ZXAI1.mp4" type="video/mp4">
                您的浏览器不支持视频播放
              </video>
            </div>
            
            <div class="engine-details">
              <div class="pain-point">
                <h5>场景痛点：</h5>
                <p>团队想用AI，但不知从何学起？学了理论，实际工作用不上？</p>
              </div>
              
              <div class="solution">
                <h5>智链如何帮您：</h5>
                <p>提供从"知道-会用-有用"的系统化AI学习与应用平台，聚焦核心工作场景，集成工具与智能体，让员工快速掌握并应用AI提升工作效率。</p>
              </div>
              
              <div class="value">
                <h5>核心价值：</h5>
                <p>学以致用，场景实战，体系赋能，将AI转化为企业生产力。</p>
              </div>
            </div>
            
            <el-button type="primary" class="case-button" @click="goToTestimonials('知行AI')">
              查看知行AI案例
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.view-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: var(--background);
}

.header {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  color: white;
  height: 3rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 100;
  box-sizing: border-box;
}

.back-button {
  color: white;
  margin-right: 1rem;
  font-size: 1.25rem;
  padding: 0.5rem;
  position: absolute;
  left: 0.5rem;
  z-index: 1;
}

.header h1 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  width: 100%;
  text-align: center;
}

.content {
  flex: 1;
  padding-top: 3rem;
  display: flex;
  flex-direction: column;
}

/* 整体引导语 */
.intro-banner {
  background: linear-gradient(135deg, rgba(48, 96, 176, 0.9), rgba(77, 145, 255, 0.9));
  padding: 2rem 1rem;
  color: white;
  text-align: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.intro-content {
  max-width: 800px;
  margin: 0 auto;
}

.intro-heading {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.intro-text {
  font-size: 1rem;
  line-height: 1.6;
  font-weight: 400;
}

/* 通用部分样式 */
.section {
  padding: 2rem 1rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

.section-header {
  text-align: center;
  margin-bottom: 2.5rem;
  position: relative;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--primary);
  margin-bottom: 0.7rem;
}

.section-underline {
  height: 3px;
  width: 80px;
  background: var(--primary);
  margin: 0 auto 1rem;
  border-radius: 3px;
}

.section-subtitle {
  font-size: 1rem;
  color: var(--text-light);
  font-style: italic;
  margin-top: -0.5rem;
}

/* 智链进化之旅 */
.journey-step {
  display: flex;
  margin-bottom: 2.5rem;
  position: relative;
}

.journey-step:not(:last-child)::after {
  content: '';
  position: absolute;
  top: 60px;
  left: 25px;
  bottom: -40px;
  width: 2px;
  background: linear-gradient(to bottom, var(--primary-light), transparent);
  z-index: 0;
}

.step-number {
  flex-shrink: 0;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: bold;
  margin-right: 1.5rem;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  z-index: 1;
}

.step-content {
  flex: 1;
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.step-title {
  color: var(--primary);
  font-size: 1.3rem;
  margin: 0 0 0.3rem;
}

.step-subtitle {
  color: var(--text-light);
  font-size: 1rem;
  font-weight: 500;
  margin: 0 0 1.5rem;
}

.pain-point, .solution, .value {
  margin-bottom: 1.5rem;
}

.pain-point h5, .solution h5, .value h5 {
  font-size: 1rem;
  color: var(--text);
  margin: 0 0 0.5rem;
  font-weight: 600;
}

.pain-point p, .value p {
  margin: 0;
  color: var(--text);
  font-size: 0.95rem;
  line-height: 1.5;
}

.solution ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.solution ul li {
  display: flex;
  margin-bottom: 1rem;
  align-items: flex-start;
}

.solution ul li:last-child {
  margin-bottom: 0;
}

.solution ul li .el-icon {
  font-size: 1.2rem;
  color: var(--primary);
  margin-right: 0.75rem;
  flex-shrink: 0;
  margin-top: 0.1rem;
}

.solution ul li div {
  flex: 1;
}

.solution ul li strong {
  display: block;
  margin-bottom: 0.2rem;
  color: var(--primary);
}

.solution ul li p {
  margin: 0;
  color: var(--text);
  font-size: 0.95rem;
  line-height: 1.5;
}

.case-button {
  width: 100%;
  height: 2.5rem;
  border-radius: 8px;
  margin-top: 0.5rem;
  font-weight: 500;
  letter-spacing: 0.5px;
}

/* 专业智能引擎 */
.engines-section {
  background-color: #f9fafc;
  padding-bottom: 3rem;
}

.engine-card {
  display: flex;
  background: white;
  border-radius: 12px;
  margin-bottom: 2rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  flex-direction: column;
}

.engine-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  background: linear-gradient(135deg, var(--primary-light), var(--primary));
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 1.5rem;
}

.engine-icon .el-icon {
  font-size: 2rem;
}

.engine-content {
  flex: 1;
  padding: 0 1.5rem 1.5rem;
}

.engine-content h3 {
  font-size: 1.3rem;
  color: var(--primary);
  margin: 0 0 0.3rem;
}

.engine-content h4 {
  font-size: 1rem;
  color: var(--text-light);
  font-weight: 500;
  margin: 0 0 1.5rem;
}

.engine-details {
  margin-bottom: 1.5rem;
}

/* 视频相关 */
.video-container {
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
  margin: 0 0 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  background-color: #000;
  position: relative;
  aspect-ratio: 16 / 9;
}

.promo-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

/* 响应式调整 */
@media (min-width: 768px) {
  .intro-heading {
    font-size: 1.8rem;
  }
  
  .intro-text {
    font-size: 1.1rem;
  }
  
  .section {
    padding: 3rem 2rem;
  }
  
  .section-title {
    font-size: 1.8rem;
  }
  
  .section-underline {
    width: 100px;
  }
  
  .journey-step {
    margin-bottom: 3rem;
  }
  
  .journey-step:not(:last-child)::after {
    left: 25px;
  }
  
  .case-button {
    width: auto;
    min-width: 200px;
    padding: 0 2rem;
  }
  
  .engine-card {
    flex-direction: row;
  }
  
  .engine-icon {
    margin: 2rem;
    width: 80px;
    height: 80px;
  }
  
  .engine-content {
    padding: 2rem 2rem 2rem 0;
  }
}

@media (min-width: 992px) {
  .journey-step:not(:last-child)::after {
    left: 49px;
    bottom: -60px;
  }
  
  .step-number {
    width: 100px;
    height: 100px;
    font-size: 2.5rem;
    margin-right: 2rem;
  }
  
  .step-content {
    padding: 2rem;
  }
  
  .step-title {
    font-size: 1.5rem;
  }
  
  .video-container {
    max-width: 80%;
  }
}
</style>